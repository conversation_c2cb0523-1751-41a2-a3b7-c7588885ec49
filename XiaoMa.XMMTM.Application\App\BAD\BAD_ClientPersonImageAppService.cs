﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientPersonImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/12/22/星期二 15:26:03
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.FileServer;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientPersonImageAppService : XMMTMAppServiceBase, IBAD_ClientPersonImageAppService
    {
        private readonly IRepository<ClientPersonImage, Guid> repository;
        private readonly IRepository<SorderDetailImage, Guid> sorderDetailImageRepository;
        private readonly IObjectMapper objectMapper;
        private readonly IFileServer fileServer;

        public BAD_ClientPersonImageAppService(
       IRepository<ClientPersonImage, Guid> repository,
       IRepository<SorderDetailImage, Guid> sorderDetailImageRepository,

       IObjectMapper objectMapper,
            IFileServer fileServer
         )
        {
            this.repository = repository;
            this.sorderDetailImageRepository = sorderDetailImageRepository;
            this.objectMapper = objectMapper;
            this.fileServer = fileServer;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientPersonImageGetAllOutputDto>> Get(BAD_ClientPersonImageGetAllInputDto input)
        {
            var query = from t in repository.GetAll()
                        where t.ClientPersonID == input.PersonID
                        select new BAD_ClientPersonImageGetAllOutputDto()
                        {
                            ClientPersonID = t.ClientPersonID,
                            CreateBy = t.CreateBy,
                            CreateID = t.CreateID,
                            CreateOn = t.CreateOn,
                            Id = t.Id,
                            ImageUrl = t.ImageUrl,
                            Url = fileServer.GetImageUrl(t.ImageUrl),
                            Name = fileServer.GetImageUrl(t.ImageUrl),
                        };

            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<BAD_ClientPersonImageGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ClientPersonImageGetAllOutputDto>(count, list);
        }
        [HttpPost]
        //[AbpAuthorize]
        public async Task Adds(IFormFile file, Guid PersonID)
        {
            var personImagesId = Guid.NewGuid();
            var path = await fileServer.SaveImage(file, SystemConfig.SYS_ModelImageType.PersonImages, null, personImagesId);
            await repository.InsertAsync(new ClientPersonImage() { ClientPersonID = PersonID, Id = personImagesId, IsActive = true, IsDel = false, OriginalImgUrl = path, Url = path, ImageUrl = path });
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientPersonImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                if (sorderDetailImageRepository.GetAll().Any(a => a.ImageID == entity.Id))
                {
                    throw new UserFriendlyException("已有订单使用此顾客照片,请删除订单中的顾客照片,再删除此照片");
                }
                fileServer.RemoveImage(oldentity.ImageUrl);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

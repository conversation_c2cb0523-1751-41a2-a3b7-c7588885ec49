﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemRuleGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/11/17/星期二 20:34:33
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemRuleGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemRuleGetAllOutputDto : ModelElemRule
    {
        public string BomRuleTypeText { set; get; }
        public string ModelRuleTypeText { set; get; }
        public string ModelElemListText { set; get; }

        public bool GenderID{ set; get; }

        public Guid? GroupID { set; get; }
        public string GroupText { set; get; }
    }
}

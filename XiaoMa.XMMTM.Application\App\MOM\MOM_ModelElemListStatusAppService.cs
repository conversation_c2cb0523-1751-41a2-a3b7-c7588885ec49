/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemListStatusAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/10/15/星期四 14:30:23
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.Timing;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.SYM;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemListStatusAppService : XMMTMAppServiceBase, IMOM_ModelElemListStatusAppService
    {
        private readonly IRepository<ModelElemListStatus, Guid> repository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<User, Guid> userRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<SorderDetailModel, Guid> sorderDetailModelRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemListStatusAppService(
       IRepository<ModelElemListStatus, Guid> repository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
          IRepository<User, Guid> userRepository,
       IRepository<Group, Guid> groupRepository,
                   IRepository<SorderDetailModel, Guid> sorderDetailModelRepository,
            IRepository<SorderDetail, Guid> sorderDetailRepository,
            IRepository<Sorder, Guid> sorderRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemListRepository = modelElemListRepository;
            this.userRepository = userRepository;
            this.groupRepository = groupRepository;
            this.sorderDetailModelRepository = sorderDetailModelRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.sorderRepository = sorderRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemListStatusGetAllOutputDto>> Get(MOM_ModelElemListStatusGetAllInputDto input)
        {
            var sql = from t in repository.GetAll().AsNoTracking()
                      join t1 in modelElemListRepository.GetAll() on t.ModelElemListID equals t1.Id
                      join t1x in groupRepository.GetAll() on t1.GroupID equals t1x.Id
                      //join t1x2 in db.CFG_ModelElemType on t1.ModelElemTypeID equals t1x2.ID
                      //join t2 in db.CFG_Status on t.StatusID equals t2.ID
                      select new MOM_ModelElemListStatusGetAllOutputDto()
                      {
                          Id = t.Id,
                          Remark = t.Remark,
                          ModelElemListID = t.ModelElemListID,
                          ModelElemListName = t1.Code + ":" + t1.CodeName,
                          ItemRequired = t.ItemRequired,
                          QtyRequired = t.QtyRequired,
                          ModelElemRequired = t.ModelElemRequired,
                          InputRequired = t.InputRequired,
                          GroupID = t1x.Id,
                          GroupName = t1x.CodeName,
                          ModelElemTypeName = t1.ModelElemTypeID.GetDescription(),
                          StatusID = t.StatusID,
                          StatusName = t.StatusID.GetDescription(),
                          CreateBy = t.CreateBy,
                          CreateID = t.CreateID,
                          CreateOn = t.CreateOn,
                          IsActive = t.IsActive,
                          ModifyBy = t.ModifyBy,
                          ModifyID = t.ModifyID,
                          ModifyOn = t.ModifyOn,
                          ImageRequired = t.ImageRequired,
                      };
            sql = sql.WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                .WhereIf(input.StatusID.HasValue, a => (int)a.StatusID == input.StatusID.Value)
                .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemListName.Contains(input.Text));

            var count = await sql.CountAsync();
            var result = await sql.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelElemListStatusGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemListStatusGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemListStatusDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = ObjectMapper.Map<ModelElemListStatus>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemListStatusDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemListStatusDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        /// <summary>
        /// 更加款式ID以及订单SorderDetailModelID过去款式明细验证必输集合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ModelElemListStatusOutputDto>> GetModelElemListStatus(ModelElemListStatusInputDto input)
        {
            var modelElemListStatus = repository.GetAll().Where(a => a.IsActive);
            var user = await userRepository.GetAsync(this.SSOSession.UserId.Value);
            if (user.UserType == UserTypeEnums.ExternalAccount)
            {
                modelElemListStatus = from t in modelElemListStatus
                                      where input.ModelElemListIDs.Contains(t.ModelElemListID) && t.StatusID == ODM_SorderStatus.ClientUndetermined
                                      select t;
            }
            else
            {
                modelElemListStatus = from t in modelElemListStatus
                                      where input.ModelElemListIDs.Contains(t.ModelElemListID)
                                      select t;
                if (input.SorderDetailModelID.HasValue)
                {
                    var sorder = await (from t in sorderDetailModelRepository.GetAll()
                                        join t1 in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1.Id
                                        join t2 in sorderRepository.GetAll() on t1.SorderID equals t2.Id
                                        where t.Id == input.SorderDetailModelID.Value
                                        select t2).FirstOrDefaultAsync();
                    if (sorder == null)
                    {
                        modelElemListStatus = modelElemListStatus.Where(a => a.StatusID == ODM_SorderStatus.ClientUndetermined || a.StatusID == ODM_SorderStatus.Confirmed || a.StatusID == ODM_SorderStatus.CLock || a.StatusID == ODM_SorderStatus.CReject || a.StatusID == ODM_SorderStatus.MReject || a.StatusID == ODM_SorderStatus.CChecked);
                    }
                    else
                    {
                        switch (sorder.StatusID)
                        {
                            case ODM_SorderStatus.ClientUndetermined:
                            case ODM_SorderStatus.Confirmed:
                            case ODM_SorderStatus.CLock:
                            case ODM_SorderStatus.CReject:
                            case ODM_SorderStatus.MReject:
                            case ODM_SorderStatus.CChecked:
                                modelElemListStatus = modelElemListStatus.Where(a => a.StatusID == ODM_SorderStatus.ClientUndetermined || a.StatusID == ODM_SorderStatus.Confirmed || a.StatusID == ODM_SorderStatus.CLock || a.StatusID == ODM_SorderStatus.CReject || a.StatusID == ODM_SorderStatus.MReject || a.StatusID == ODM_SorderStatus.CChecked);
                                break;

                            case ODM_SorderStatus.MLock:
                            case ODM_SorderStatus.PReject:
                            case ODM_SorderStatus.MChecked:
                                modelElemListStatus = modelElemListStatus.Where(a => a.StatusID == ODM_SorderStatus.MChecked || a.StatusID == ODM_SorderStatus.MLock || a.StatusID == ODM_SorderStatus.PReject);
                                break;

                            case ODM_SorderStatus.Planed:

                                break;
                            default:
                                break;
                        }
                    }
                }
                else
                {
                    modelElemListStatus = modelElemListStatus.Where(a => a.StatusID == ODM_SorderStatus.ClientUndetermined || a.StatusID == ODM_SorderStatus.Confirmed || a.StatusID == ODM_SorderStatus.CLock || a.StatusID == ODM_SorderStatus.CReject || a.StatusID == ODM_SorderStatus.MReject || a.StatusID == ODM_SorderStatus.CChecked);
                }

            }
            var mlist = await modelElemListStatus.ToListAsync();
            var qlist = (from t in mlist
                         group t by t.ModelElemListID into g
                         select new ModelElemListStatusOutputDto()
                         {
                             ModelElemListID = g.Key,
                             InputRequired = g.OrderByDescending(a => a.InputRequired).FirstOrDefault().InputRequired,
                             ItemRequired = g.OrderByDescending(a => a.ItemRequired).FirstOrDefault().ItemRequired,
                             ModelElemRequired = g.OrderByDescending(a => a.ModelElemRequired).FirstOrDefault().ModelElemRequired,
                             QtyRequired = g.OrderByDescending(a => a.QtyRequired).FirstOrDefault().QtyRequired,
                             ImageRequired = g.OrderByDescending(a => a.ImageRequired).FirstOrDefault().ImageRequired,
                         }).ToList();
            return qlist;
        }
    }
}

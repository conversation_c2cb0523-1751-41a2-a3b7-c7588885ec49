﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemConfigAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/12/28/星期一 10:19:45
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ItemElemItemConfigAppService : XMMTMAppServiceBase, IBAD_ItemElemItemConfigAppService
    {
        private readonly IRepository<ItemElemItemConfig, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_ItemElemItemConfigAppService(
       IRepository<ItemElemItemConfig, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemElemItemConfigGetAllOutputDto>> Get(BAD_ItemElemItemConfigGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in groupRepository.GetAll() on t2.GroupID equals t3x.Id into t3xx 
                         from t3 in t3xx.DefaultIfEmpty()
                         select new BAD_ItemElemItemConfigGetAllOutputDto()
                         {
                             ModelElemID = t.ModelElemID,
                             ItemClassID = t.ItemClassID,
                             ClientID = t.ClientID,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             ItemID = t.ItemID,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Seq = t.Seq,
                             ItemClassText = t.ItemClassID.GetDescription(),
                             ModelElemCode = t1.Code,
                             ModelElemCodeName = t1.CodeName,
                             ModelElemListCode = t2.Code,
                             ModelElemListName = t2.CodeName,
                             GroupText = t3.CodeName,
                             GroupID = t2.GroupID 
                         })
                         .WhereIf(input.ItemClassID.HasValue, a => a.ItemClassID == input.ItemClassID.Value)
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemCode.Contains(input.Text) || a.ModelElemCodeName.Contains(input.Text) || a.ModelElemListCode.Contains(input.Text) || a.ModelElemListName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Seq).ThenByDescending(a => a.ModifyOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<BAD_ItemElemItemConfigGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ItemElemItemConfigGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ItemElemItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的款式明细");
                }
                var oldentity = ObjectMapper.Map<ItemElemItemConfig>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemElemItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的款式明细");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemElemItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemElemItemConfigDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.ModelElemID.Equals(input.ModelElemID) && a.ItemClassID == input.ItemClassID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.ModelElemID.Equals(input.ModelElemID) && a.Id != input.Id.Value && a.ItemClassID == input.ItemClassID);
            }

        }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:17
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelModelElemGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid Id { set; get; }

        public Guid? GroupID { set; get; }
        /// <summary>
        ///业务类型
        /// </summary>
        public ModelBusinessSubType? BusinessSubType { get; set; }
       public Guid? ModelGroupID { get; set; }
        public bool ? Selected { set; get; }
    }
}

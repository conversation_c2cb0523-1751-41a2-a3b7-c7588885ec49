﻿#region << 版 本 注 释 >>
/*----------------------------------------------------------------
* 项目名称 ：XiaoMa.XMMTM.App.MOM
* 类 名 称 ：MOM_BodyPriceAppService
* 类 描 述 ：
* 命名空间 ：XiaoMa.XMMTM.App.MOM
* 作    者 ：SundayPC
* 创建时间 ：2023/2/22 14:20:25
* 版 本 号 ：v1.0.0.0
*******************************************************************
* Copyright @ SundayPC 2023. All rights reserved.
*******************************************************************
//----------------------------------------------------------------*/
#endregion
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.MOM
{
    public class MOM_BodyPriceAppService : XMMTMAppServiceBase, IMOM_BodyPriceAppService
    {
        private readonly IRepository<BodyPrice, Guid> repository;
        private readonly IRepository<BodyList, Guid> bodyListRepository;
        private readonly IRepository<Body, Guid> bodyRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_BodyPriceAppService(
       IRepository<BodyPrice, Guid> repository,
       IRepository<BodyList, Guid> bodyListRepository,
       IRepository<Body, Guid> bodyRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.bodyListRepository = bodyListRepository;
            this.bodyRepository = bodyRepository;
            this.modelRepository = modelRepository;
            this.clientRepository = clientRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_BodyPriceGetAllOutputDto>> Get(MOM_BodyPriceGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in bodyRepository.GetAll() on t.BodyID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in bodyListRepository.GetAll() on t1.BodyListID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelRepository.GetAll() on t.ModelID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in clientRepository.GetAll() on t.ClientID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in groupRepository.GetAll() on t.GroupID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                             //where t.IsActive
                         select new MOM_BodyPriceGetAllOutputDto()
                         {
                             IsActive = t.IsActive,
                             CreateOn = t.CreateOn,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             Id = t.Id,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             ClientID = t.ClientID,
                             BodyID = t.BodyID,
                             GroupID = t.GroupID,
                             ModelID = t.ModelID,
                             RepairPrice = t.RepairPrice,
                             Sort = t.Sort,
                             BodyCode = t1.Code,
                             BodyListCode = t2.Code,
                             BodyListName = t2.CodeName,
                             BodyName = t1.CodeName,
                             ClientName = t4.ShortName,
                             ModelName = t3.CodeName,
                             GroupName = t5.CodeName
                         }
                       )

                       .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
                       .WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID.Value)
                       .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                       .WhereIf(input.BodyID.HasValue, a => a.BodyID == input.BodyID.Value)
                       .WhereIf(input.BodyListID.HasValue, a => a.BodyListID == input.BodyListID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.BodyCode.Contains(input.Text) || a.BodyName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sort).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_BodyPriceGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_BodyPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<BodyPrice>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_BodyPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_BodyPriceDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = await (from t in repository.GetAll()/*.Include(a => a.BAD_ProductionSeries)*/
                                       where t.Id == entity.Id.Value
                                       select t).FirstOrDefaultAsync();
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_BodyPriceDto input)
        {

            var any = await repository.GetAll()
                .WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID.Value)
                .WhereIf(!input.ModelID.HasValue, a => !a.ModelID.HasValue)
                .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
                .WhereIf(!input.ClientID.HasValue, a => !a.ClientID.HasValue)
                .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                .WhereIf(!input.GroupID.HasValue, a => !a.GroupID.HasValue)
                .AnyAsync(a => a.BodyID.Equals(input.BodyID));
            return any;

        }
    }
}

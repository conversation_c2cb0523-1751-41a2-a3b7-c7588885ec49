/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYM_ClientDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/10/星期五 21:51:10
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using System.Collections.Generic;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// SYM_ClientDto新增/修改数据对象Dto
    /// </summary>

    public class BAD_ClientDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }

        public bool IsActive { set; get; }

        /// <summary>
        /// 简称
        /// </summary>
        public string ShortName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 区县
        /// </summary>
        public string Area { get; set; }

        /// <summary>
        /// 街道
        /// </summary>
        public string Street { get; set; }
        /// <summary>
        /// 邮编
        /// </summary>
        public string ZipCode { set; get; }

        /// <summary>
        /// 来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 客户分类
        /// </summary>
        public ClientGroupEnums ClientGroup { set; get; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public ClientGradeEnums ClientGrade { set; get; }

        /// <summary>
        ///
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 结算折扣
        /// </summary>
        public decimal? Discount { set; get; }
        /// <summary>
        /// 面料耗量折扣(用于账单)
        /// </summary>
        public decimal? ItemDiscount { set; get; }

        /// <summary>
        ///
        /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string SupplierCode { get; set; }

        /// <summary>
        ///
        /// </summary>
        public Guid? OwnerID { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string Owner { get; set; }

        /// <summary>
        /// 银行Id
        /// </summary>
        public Guid? BankID { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        public Guid? CurrencyID { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public Guid? VatID { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        public CurrencyEnums? CurrencyType { get; set; } = CurrencyEnums.CNY;
        /// <summary>
        /// 汇率
        /// </summary>
        public decimal? ExchangeRate { set; get; } = 1;
        /// <summary>
        /// 付款条款
        /// </summary>
        public Guid? PaymentTermsID { get; set; }

        /// <summary>
        /// 付款方式ID
        /// </summary>
        public Guid? PaymentMethodID { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string ClientCode1 { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string ClientTypeCode { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string ChannelCode { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string SalerCode { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string SalerName { get; set; }
        /// <summary>
        /// 父级客户
        /// </summary>
        public Guid? ParentClientID { set; get; }
        /// <summary>
        /// 继承总店版型
        /// </summary>
        /// 
        public bool? InheritedParentModel { set; get; }
        /// <summary>
        /// 继承总店账单
        /// </summary>
        public bool? InheritedParentBill { set; get; }
        /// <summary>
        /// 账户余额
        /// </summary>
        public decimal? AccountBalance { set; get; }
        /// <summary>
        /// 账户额度
        /// </summary>
        public decimal? AccountLimit { set; get; }

        /// <summary>
        /// 业务员
        /// </summary>
        public Guid? SalesmanId { set; get; }
        public Guid? SalesmanId1 { set; get; }
        /// <summary>
        /// 支付类型
        /// </summary>
        public AccountPaymentGroupEnums? AccountPaymentGroup { set; get; }
        public List<BAD_ClientPersonDto> ClientPerson { get; set; }
        public List<BAD_ClientShopDto> ClientShop { get; set; }
        /// <summary>
        ///
        /// </summary>

        public List<BAD_ClientAddressDto> ClientAddress { get; set; }

        public List<BAD_ClientModelDto> ClientModel { get; set; }
    }
}

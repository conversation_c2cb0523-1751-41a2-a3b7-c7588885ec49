﻿{
    "RelativePath":  "RateLimitingActionFilter.cs",
    "SourceFile":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\RateLimitingActionFilter.cs",
    "TargetFile":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\RateLimitingActionFilter.cs",
    "SourceLines":  94,
    "TargetLines":  93,
    "TotalDifferences":  92,
    "NamespaceOnlyDiffs":  0,
    "ContentDiffs":  92,
    "SourceNamespace":  "namespace XiaoMa.XMMTM",
    "TargetNamespace":  "namespace XiaoMa.Shop.XMMTM\r",
    "MissingUsingsInTarget":  "using Microsoft.AspNetCore.Mvc;",
    "ExtraUsingsInTarget":  {

                            },
    "Differences":  [
                        {
                            "LineNumber":  2,
                            "SourceLine":  "using Microsoft.AspNetCore.Mvc;",
                            "TargetLine":  "using Microsoft.AspNetCore.Mvc.Filters;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  3,
                            "SourceLine":  "using Microsoft.AspNetCore.Mvc.Filters;",
                            "TargetLine":  "using Microsoft.Extensions.Caching.Memory;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  4,
                            "SourceLine":  "using Microsoft.Extensions.Caching.Memory;",
                            "TargetLine":  "using System;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  5,
                            "SourceLine":  "using System;",
                            "TargetLine":  "using System.Collections.Generic;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  6,
                            "SourceLine":  "using System.Collections.Generic;",
                            "TargetLine":  "using System.Text;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  7,
                            "SourceLine":  "using System.Text;",
                            "TargetLine":  "using System.Threading.Tasks;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  8,
                            "SourceLine":  "using System.Threading.Tasks;",
                            "TargetLine":  "",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  9,
                            "SourceLine":  "",
                            "TargetLine":  "namespace XiaoMa.Shop.XMMTM",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  10,
                            "SourceLine":  "namespace XiaoMa.XMMTM",
                            "TargetLine":  "{",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  11,
                            "SourceLine":  "{",
                            "TargetLine":  "    public class RateLimitingActionFilter : IAsyncActionFilter",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  12,
                            "SourceLine":  "    public class RateLimitingActionFilter : IAsyncActionFilter",
                            "TargetLine":  "    {",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  13,
                            "SourceLine":  "    {",
                            "TargetLine":  "        private readonly IMemoryCache _memoryCache;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  14,
                            "SourceLine":  "        private readonly IMemoryCache _memoryCache;",
                            "TargetLine":  "",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  15,
                            "SourceLine":  "",
                            "TargetLine":  "        //注入IMemoryCache",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  16,
                            "SourceLine":  "        //依赖注入IMemoryCache",
                            "TargetLine":  "        public RateLimitingActionFilter(IMemoryCache memoryCache)",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  17,
                            "SourceLine":  "        public RateLimitingActionFilter(IMemoryCache memoryCache)",
                            "TargetLine":  "        {",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  18,
                            "SourceLine":  "        {",
                            "TargetLine":  "            _memoryCache = memoryCache;",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  19,
                            "SourceLine":  "            _memoryCache = memoryCache;",
                            "TargetLine":  "        }",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  20,
                            "SourceLine":  "        }",
                            "TargetLine":  "",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        },
                        {
                            "LineNumber":  21,
                            "SourceLine":  "",
                            "TargetLine":  "        /// \u003csummary\u003e",
                            "IsNamespaceOnly":  false,
                            "DiffType":  "Content"
                        }
                    ],
    "IsIdenticalExceptNamespace":  false
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IMOM_SizeRuleCheckAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2022年11月1日,星期二 13:39:43
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IMOM_SizeRuleCheckAppService : IApplicationService
    {
        Task<PagedResultDto<MOM_SizeRuleCheckGetAllOutputDto>> Get(MOM_SizeRuleCheckGetAllInputDto input);
        Task Adds(List<MOM_SizeRuleCheckDto> input);
        Task Updates(List<MOM_SizeRuleCheckDto> input);
        Task Deletes(List<MOM_SizeRuleCheckDto> input);
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/7/28/星期二 16:20:31
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemGetAllOutputDto2 : ModelElem
    {

        public int Count { set; get; }
        public Guid GroupID { set; get; }
        public string GroupText { set; get; }

        public bool GenderID { set; get; }

        public string GenderText { set; get; }

        public int ModelElemTypeID { set; get; }

        public string ModelElemTypeText { set; get; }

        public Guid ModelElemBaseID { set; get; }

        public string ModelElemBaseText { set; get; }

        public string ModelElemListText { set; get; }
        public string ItemCode { set; get; }
        public string ItemName { set; get; }
        public string OriginalItemNo { set; get; }
        public string ItemGroupText { set; get; }
        public string InaStyleCodeText { set; get; }


    }
}

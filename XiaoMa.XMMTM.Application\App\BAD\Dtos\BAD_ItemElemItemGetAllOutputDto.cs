﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/12/8/星期二 13:13:40
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemElemItemGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ItemElemItemGetAllOutputDto : ItemElemItem
    {
        public string ModelElemListText { set; get; }
        public string SorderTypeText { set; get; }
        public string ModelElemText { set; get; }
        public string Item1Text { set; get; }

        public string OriginalItemNo { set; get; }

        public new Guid? ItemID1 { get; set; }

        public string ClientText { set; get; }
    }
}

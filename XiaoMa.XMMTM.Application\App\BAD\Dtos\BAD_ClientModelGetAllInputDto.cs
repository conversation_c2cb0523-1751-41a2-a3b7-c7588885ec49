﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/7/星期五 21:02:12
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientModelGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ClientModelGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid Id { set; get; }
        public Guid ?ClassID { set; get; }
    }
}

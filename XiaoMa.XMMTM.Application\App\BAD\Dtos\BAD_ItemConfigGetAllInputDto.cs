﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemConfigGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/10/26/星期一 9:10:44
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemConfigGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ItemConfigGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid Id { set; get; }

        public BAD_ItemConfigBase? ItemConfigBaseID { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientItemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/6/30/星期三 16:51:00
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientItemAppService : XMMTMAppServiceBase, IBAD_ClientItemAppService
    {
        private readonly IRepository<ClientItem, Guid> repository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientItemAppService(
       IRepository<ClientItem, Guid> repository,
       IRepository<Item, Guid> itemRepository,
       IRepository<Client, Guid> clientRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.itemRepository = itemRepository;
            this.clientRepository = clientRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientItemGetAllOutputDto>> Get(BAD_ClientItemGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in itemRepository.GetAll() on t.ItemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         select new BAD_ClientItemGetAllOutputDto()
                         {
                             Id = t.Id,
                             ItemID = t.ItemID,
                             ClientID = t.ClientID,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             IsActive = t.IsActive,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             ItemCode = t1.Code,
                             OriginalItemNo = t1.OriginalItemNo,
                         })
                         .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.OriginalItemNo.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.ItemCode.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<BAD_ClientItemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ClientItemDto> input)
        {
            foreach (var entity in input)
            {
                await this.ExistCodeAsync(entity);

                var oldentity = ObjectMapper.Map<ClientItem>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientItemDto> input)
        {
            foreach (var entity in input)
            {
                await this.ExistCodeAsync(entity);

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientItemDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task ExistCodeAsync(BAD_ClientItemDto input)
        {
            var b = await repository.GetAll().Where(a => a.ClientID.Equals(input.ClientID) && a.ItemID == input.ItemID).WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value).AnyAsync();
            if (b)
            {
                throw new UserFriendlyException("请勿添加重复项");
            }

        }
        /// <summary>
        /// 继承父级物料绑定
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task AutoBindClientItemByParent()
        {
            var clientsAll = await clientRepository.GetAll().Where(a => a.InheritedParentModel.HasValue && a.InheritedParentModel.Value && a.ParentClientID.HasValue).Select(a => new { ClientID = a.Id, a.ParentClientID }).ToListAsync();
            var parentClientAllIds = clientsAll.Select(a => a.ParentClientID).Distinct().ToList();
            var clientAllIds = clientsAll.Select(a => a.ClientID).Distinct().ToList();
            var clientItems1 = await (from t in repository.GetAll()
                                      where parentClientAllIds.Contains(t.ClientID)
                                      select new
                                      {
                                          t.Id,
                                          t.ClientID,
                                          t.ItemID,
                                      }).ToListAsync();
            var clientItems2 = await (from t in repository.GetAll()
                                      where clientAllIds.Contains(t.ClientID)
                                      select new
                                      {
                                          t.Id,
                                          t.ClientID,
                                          t.ItemID,
                                      }).ToListAsync();

            var pGroups = clientsAll.GroupBy(a => a.ParentClientID).ToList();
            var list = new List<ClientItem>();
            var delListIds = new List<Guid>();
            foreach (var item in pGroups)
            {
                //父级
                var ps = clientItems1.Where(a => a.ClientID == item.Key).ToList();
                //所有子集
                var clientids = item.Select(a => a.ClientID).Distinct().ToList();
                var cs = clientItems2.Where(a => clientids.Contains(a.ClientID)).ToList();
                if (cs.Any())
                { //单个子集
                    var csg = cs.GroupBy(a => a.ClientID).ToList();
                    foreach (var g in csg)
                    {
                        var itemids1 = ps.Select(a => a.ItemID).ToList();
                        var itemids2 = g.Select(a => a.ItemID).ToList();
                        //没有的需要添加
                        var a = itemids1.Except(itemids2).Select(a => new ClientItem() {Id=Guid.NewGuid(), ItemID = a, IsActive = true, ClientID = g.Key }).ToList();
                        list.AddRange(a);
                        //多的需要删除的
                        var ditemids = itemids2.Except(itemids1).Select(a => a).ToList();
                        var did = g.Where(a => ditemids.Contains(a.ItemID)).Select(a => a.Id).Distinct().ToList();
                        delListIds.AddRange(did);
                    }

                }
                else
                {
                    foreach (var id in clientids)
                    {
                        var cc = ps.Select(a => new ClientItem() { Id = Guid.NewGuid(), ClientID = id, ItemID = a.ItemID, IsActive = true }).ToList();
                        list.AddRange(cc);
                    }
                }

            }
            if (list.Any())
            {
                await repository.GetDbContext().BulkInsertAsync(list);
            }

            if (delListIds.Any())
            {
                //保留子集个性化,暂时不删除,如果不保留个性化 ,取消下方注释即可

                //var dels = await repository.GetAll().Where(a => delListIds.Contains(a.Id)).ToListAsync();
                //await repository.GetDbContext().BulkDeleteAsync(dels);
            }

        }
    }
}

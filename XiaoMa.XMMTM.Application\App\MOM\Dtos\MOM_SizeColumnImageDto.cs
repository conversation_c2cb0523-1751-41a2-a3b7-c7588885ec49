﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeColumnImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/10/15/星期四 14:29:09
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeColumnImageDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_SizeColumnImageDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 规格字段ID
        /// </summary>

        public Guid SizeColumnID { get; set; }

        /// <summary>
        /// 版型ID
        /// 用于特殊版型的规格字段展示特殊的图片ID
        /// </summary>
        public Guid? ModelID { set; get; }

        /// <summary>
        /// 图片ID
        /// 默认成衣图片ID
        /// </summary>
        public Guid ModelImageID { set; get; }
        /// <summary>
        ///  图片ID1
        /// 默认量体图片ID
        /// </summary>
        public Guid? ModelImageID1 { set; get; }
        /// <summary>
        /// 
        /// </summary>

        public string Remark { get; set; }

        public virtual bool IsActive { set; get; }

    }
}

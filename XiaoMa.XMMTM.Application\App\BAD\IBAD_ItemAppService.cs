/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/7/27/星期一 10:38:38
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemGetAllOutputDto>> Get(BAD_ItemGetAllInputDto input);
        Task Adds(List<BAD_ItemDto> input);
        Task Updates(List<BAD_ItemDto> input);
        Task Deletes(List<BAD_ItemDto> input);
        Task Check(Item item);
        Task<Item> createItemCode(Item item,int? number);
    }
}

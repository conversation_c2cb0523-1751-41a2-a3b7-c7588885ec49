﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ClientPersonImageAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/12/22/星期二 15:26:13
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ClientPersonImageAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ClientPersonImageGetAllOutputDto>> Get(BAD_ClientPersonImageGetAllInputDto input);
        Task Adds(IFormFile file, Guid PersonID);
        Task Deletes(List<BAD_ClientPersonImageDto> input);
    }
}

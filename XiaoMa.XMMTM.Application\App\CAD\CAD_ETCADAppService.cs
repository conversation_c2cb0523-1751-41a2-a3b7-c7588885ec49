/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    CAD_ETCADAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/12/9/星期三 8:37:55
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Abp.Web.Models;
using Castle.Core.Logging;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using XiaoMa.Helpers;
using XiaoMa.Log;
using XiaoMa.XMMTM.App.CAD.Dtos;
using XiaoMa.XMMTM.App.SYS;
using XiaoMa.XMMTM.CAD;
using XiaoMa.XMMTM.CAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.ODM;
using XiaoMa.XMMTM.Domain.SYS;
using XiaoMa.XMMTM.Domain.WAR;
using XiaoMa.XMMTM.FileServer;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.CAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class CAD_ETCADAppService : XMMTMAppServiceBase, ICAD_ETCADAppService
    {
        private readonly IETCadManager etCadManager;
        private readonly IRepository<SorderCadState, Guid> sorderCadStateRepository;
        private readonly IRepository<SorderCadDetail, Guid> sorderCadDetailRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<ItemBom, Guid> itemBomRepository;
        private readonly IRepository<ItemBomDetail, Guid> itemBomDetailRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly DESEncryptHelper encryptHelper;
        private readonly ICadKeyManager cadKeyManager;
        private readonly IObjectMapper objectMapper;
        //public Castle.Core.Logging.ILogger Logger { set; get; }
        private readonly ISYS_ItemTextureLengthAppService sYS_ItemTextureLengthAppService;
        private readonly IRepository<SorderLog, Guid> sorderLogRepository;
        private readonly IRepository<FileManagement, Guid> fileManagementRepository;
        private readonly IXiaoMaLog xiaoMaLog;
        private readonly IETCadManager eTCadManager;
        private readonly IFileServer fileServer;
        private readonly IHttpContextAccessor httpContextAccessor;

        public CAD_ETCADAppService(
            IETCadManager etCadManager,
             IRepository<SorderCadState, Guid> sorderCadStateRepository,
             IRepository<SorderCadDetail, Guid> sorderCadDetailRepository,
             IRepository<SorderDetail, Guid> sorderDetailRepository,
             IRepository<ItemBom, Guid> itemBomRepository,
             IRepository<ItemBomDetail, Guid> itemBomDetailRepository,
             IRepository<Sorder, Guid> sorderRepository,
             DESEncryptHelper encryptHelper,
             ICadKeyManager cadKeyManager,
       IObjectMapper objectMapper,
        ISYS_ItemTextureLengthAppService sYS_ItemTextureLengthAppService,
            IRepository<SorderLog, Guid> sorderLogRepository,
                IRepository<FileManagement, Guid> fileManagementRepository,
         IXiaoMaLog xiaoMaLog,
         IETCadManager eTCadManager,
           IFileServer fileServer,
           IHttpContextAccessor httpContextAccessor
         )
        {
            this.etCadManager = etCadManager;
            this.sorderCadStateRepository = sorderCadStateRepository;
            this.sorderCadDetailRepository = sorderCadDetailRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.itemBomRepository = itemBomRepository;
            this.itemBomDetailRepository = itemBomDetailRepository;
            this.sorderRepository = sorderRepository;
            this.encryptHelper = encryptHelper;
            this.cadKeyManager = cadKeyManager;
            this.objectMapper = objectMapper;
            //Logger = NullLogger.Instance;
            this.sYS_ItemTextureLengthAppService = sYS_ItemTextureLengthAppService;
            this.sorderLogRepository = sorderLogRepository;
            this.fileManagementRepository = fileManagementRepository;
            this.xiaoMaLog = xiaoMaLog;
            this.eTCadManager = eTCadManager;
            this.fileServer = fileServer;
            this.httpContextAccessor = httpContextAccessor;
        }

        #region CAD专用接口
        /// <summary>
        /// 获取Cad排料图
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAllowAnonymous]
        public async Task<ETCADDto> GetGD(CAD_ETCADGetAllOutputDto input)
        {
            //if (string.IsNullOrEmpty(input.Key))
            //{
            //    return null;
            //}
            //if (!await ExistKeyAsync(input.Key))
            //{
            //    throw new UserFriendlyException("密钥错误");
            //}
            var data = await getData(input.Id);
            return data;

        }
        private async Task<ETCADDto> getData(Guid? Id)
        {
            var dto = new ETCADDto_MTM();
            if (!Id.HasValue)
            {
                var satte = await (from t in sorderCadStateRepository.GetAll()
                                   join t1 in sorderRepository.GetAll() on t.SorderID equals t1.Id
                                   where !t1.IsDeleted
                                   select t).Where(a => a.IsActive && a.State == SystemConfig.SorderCadStateEnums.Wait || (a.State == SystemConfig.SorderCadStateEnums.Exception && a.Count <= 3)).OrderBy(a => a.State).ThenBy(a => a.CreateOn).ThenBy(a => a.Count).FirstOrDefaultAsync();


                //var satte = await sorderCadStateRepository.GetAll().Where(a => a.IsActive && a.State == SystemConfig.SorderCadStateEnums.Wait || (a.State == SystemConfig.SorderCadStateEnums.Exception && a.Count <= 3)).OrderBy(a => a.State).ThenBy(a => a.CreateOn).ThenBy(a => a.Count).FirstOrDefaultAsync();
                if (satte == null)
                {
                    return null;
                }
                satte.State = SystemConfig.SorderCadStateEnums.Running;
                var data = await etCadManager.GetCadLayout(new CadLayoutInputDto() { SorderIDs = new List<Guid>() { satte.SorderID }, SorderCadStateID = satte.Id });
                if (string.IsNullOrEmpty(data.Name) || data.Bed == null || !data.Bed.Any() || data.Cloth == null || !data.Cloth.Any())
                {
                    satte.State = SystemConfig.SorderCadStateEnums.Exception;
                    satte.Count += 1;
                }
                await sorderCadStateRepository.UpdateAsync(satte);
                await AddNewSorderCadDetail(satte, data);
                dto = data;
                //dto= ObjectMapper.Map<ETCADDto>(data);
            }
            else
            {
                var state = await sorderCadStateRepository.GetAll().Where(a => a.IsActive && a.Id == Id.Value).OrderBy(a => a.State).ThenBy(a => a.CreateOn).ThenBy(a => a.Count).FirstOrDefaultAsync();
                var data = await etCadManager.GetCadLayout(new CadLayoutInputDto() { SorderIDs = new List<Guid>() { state.SorderID }, SorderCadStateID = state.Id });
                await AddNewSorderCadDetail(state, data);
                var str = JsonConvert.SerializeObject(data);
                Logger.Info("输出CAD排料图");
                Logger.Info(str);
                //ObjectMapper.Map<ProductPacking>(entity);
                //dto = ObjectMapper.Map<ETCADDto>(data);
                dto = data;
            }
            var listbed = new List<ETCADMTMBED>();
            foreach (var item in dto.Bed)
            {
                var bed = ObjectMapper.Map<ETCADMTMBED>(item);
                listbed.Add(bed);
            }
            dto.Bed = listbed;
            var res = ObjectMapper.Map<ETCADDto>(dto);
            await xiaoMaLog.Log(res, "CAD接收的数据", "XiaoMa.XMMTM.App.CAD.CAD_ETCADAppService");
            return res;
        }

        /// <summary>
        /// 返回耗量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAllowAnonymous]
        public async Task Update(CAD_ETUpdateInputDto input)
        {
            //if (string.IsNullOrEmpty(input.Key))
            //{
            //    return;
            //}
            //if (!await ExistKeyAsync(input.Key))
            //{
            //    throw new UserFriendlyException("密钥错误");
            //}

            await updateData(input.Consumptions, input.Id);


        }
        private async Task updateData(List<ConsumptionDto> dto, string sordernumber)
        {
            var entity = await (from t in sorderCadStateRepository.GetAll()
                                join t1x in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1x.Id into t1xx
                                from t1 in t1xx.DefaultIfEmpty()
                                join t2x in sorderRepository.GetAll() on t.SorderID equals t2x.Id into t2xx
                                from t2 in t2xx.DefaultIfEmpty()
                                where t2.Code == sordernumber
                                orderby t.CreateOn descending
                                select t).FirstOrDefaultAsync();
            if (entity == null)
            {
                throw new UserFriendlyException("为查询到此订单");
            }
            var sorderId = entity.SorderID;
            using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
            {
                entity.State = SystemConfig.SorderCadStateEnums.Achieve;
                entity.ModifyOn = DateTime.Now;
                entity.ModifyBy = "ETCAD";
                await sorderCadStateRepository.UpdateAsync(entity);
                //更新cad明细标注耗量信息
                await updateSorderCadDetailSuccess(dto, entity.Id);
                await unit.CompleteAsync();
            }
            //跟新bom清单中的耗量
            await UpdateItemBomDetail(entity.Id, sorderId);
            //跟新订单耗量
            //await updateSorderDetailLen(entity);
            await updateSorderDetailLenNew(entity);
        }
        #endregion

        #region CAD新接口

        #endregion

        private async Task AddNewSorderCadDetail(SorderCadState input, ETCADDto_MTM dto)
        {
            var olds = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id).ToListAsync();
            //foreach (var item in olds)
            //{
            //    await sorderCadDetailRepository.DeleteAsync(item);
            //}
            foreach (var item in dto.MTMETCADBeds)
            {
                var itemid = Guid.Empty;
                var id = Guid.Empty;
                Guid.TryParse(item.Id, out id);
                var t = Guid.TryParse(item.Details, out itemid);//没有物料id 的不管
                if (t)
                {
                    var odl = olds.FirstOrDefault(a => a.ItemID == itemid && a.SorderCadLayout == item.Name && a.SorderDetailID == item.SorderDetailID);
                    if (odl != null)
                    {
                        //把历史数据中的ID 赋值给新的cad bed 中的id
                        item.Id = odl.Id.ToString();
                        continue;
                    }
                    else
                    {
                        //删除历史相同的排料图名称
                        var others = olds.Where(a => a.SorderCadLayout == item.Name).ToList();
                        foreach (var o in others)
                        {
                            await sorderCadDetailRepository.DeleteAsync(o);
                        }
                    }
                    var _d = new SorderCadDetail() { SorderCadStateID = input.Id, Success = false, Id = id, IsActive = true, ItemID = itemid, SorderCadLayout = item.Name, CreateOn = DateTime.Now, SorderDetailID = item.SorderDetailID };
                    await sorderCadDetailRepository.InsertAsync(_d);
                }

            }
        }
        /// <summary>
        /// cad 耗量
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="SorderCadStateID"></param>
        /// <returns></returns>
        private async Task updateSorderCadDetailSuccess(List<ConsumptionDto> dto, Guid SorderCadStateID)
        {
            var olds = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == SorderCadStateID).ToListAsync();
            foreach (var item in olds)
            {
                var length = dto.Where(a => a.Id.HasValue && a.Id.Value != Guid.Empty && a.Id.Value == item.Id).Sum(a => a.Len);
                if (length > 0)
                {
                    item.Length = length;
                    item.Success = true;
                    item.ModifyOn = DateTime.Now;
                    await sorderCadDetailRepository.UpdateAsync(item);
                }
            }
        }

        /// <summary>
        /// 物料清单
        /// </summary>
        /// <param name="SorderCadStateID"></param>
        /// <param name="sorderID"></param>
        /// <returns></returns>
        private async Task UpdateItemBomDetail(Guid SorderCadStateID, Guid sorderID)
        {
            var sorderCadDetail = await (from t in sorderCadDetailRepository.GetAll()
                                         join t1x in sorderCadStateRepository.GetAll() on t.SorderCadStateID equals t1x.Id into t1xx
                                         from t1 in t1xx.DefaultIfEmpty()
                                         where t.SorderCadStateID == SorderCadStateID
                                         select new { t.SorderCadLayout, t.Length, t.SorderDetailID, t1.SorderID, t.ItemID }).ToListAsync();

            var itemBomDetails = await (from t in itemBomDetailRepository.GetAll()
                                        join t1x in itemBomRepository.GetAll() on t.ItemBomID equals t1x.Id into t1xx
                                        from t1 in t1xx.DefaultIfEmpty()
                                        where t1.SorderID == sorderID && !string.IsNullOrEmpty(t.CadLayoutText)
                                        select t).ToListAsync();
            var sorderIDS = sorderCadDetail.Select(a => a.SorderID).Distinct().ToList();
            var modelqtys = await eTCadManager.GetDetailModelQty(new GetDetailModelQtyInputDto() { SorderIDs = sorderIDS });

            foreach (var item in sorderCadDetail)
            {
                var dto = itemBomDetails.Where(a => a.SorderDetailID == item.SorderDetailID).FirstOrDefault(a => a.ItemID == item.ItemID);
                var length = item.Length;

                if (dto != null)
                {
                    var ml = dto.CadLayoutText.Substring(dto.CadLayoutText.Length - 3, 3);
                    if (ml == "-ML")
                    {
                        var list = await sYS_ItemTextureLengthAppService.CalculateItemLength(new List<SYS.Dtos.CalculateItemLengthInputDto>() { new SYS.Dtos.CalculateItemLengthInputDto() { ItemID = item.ItemID, Qty = length, SorderDetailID = item.SorderDetailID } });
                        if (list.Any())
                        {
                            length = list.FirstOrDefault() != null ? list.FirstOrDefault().Qty : length;
                        }
                        dto.Qty = length;
                    }
                    else
                    {
                        dto.Qty = XiaoMa.XMMTM.Helper.Helper.Ceiling(length / 100, 2);
                    }
                    var modelqty = modelqtys.FirstOrDefault(a => a.SorderDetailID == item.SorderDetailID);
                    if (modelqty != null && modelqty.IsSpecial)
                    {
                        dto.ModelQty = modelqty.MultipleNumber;
                    }
                    else
                    {
                        dto.ModelQty = 1;
                    }

                    await itemBomDetailRepository.UpdateAsync(dto);
                }

            }
        }
        /// <summary>
        /// 更新订单所用耗量
        /// </summary>
        /// <returns></returns>
        public async Task updateSorderDetailLen(SorderCadState input)
        {
            //更新耗量信息
            var sorderDetail = await sorderDetailRepository.GetAll().OrderBy(a => a.LineNum).FirstOrDefaultAsync(a => a.Id == input.SorderDetailID);
            if (sorderDetail == null)
            {
                sorderDetail = await sorderDetailRepository.GetAll().OrderBy(a => a.LineNum).FirstOrDefaultAsync(a => a.SorderID == input.SorderID);
            }
            sorderDetail.ItemConsumption = 0;
            sorderDetail.ItemConsumptionL = 0;
            var sorder = await sorderRepository.GetAsync(input.SorderID);

            if (input.StatusID == sorder.StatusID)
            {
                var cadDetails = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id && a.SorderCadLayout.IndexOf("ML") > -1).ToListAsync();
                if (cadDetails.Any())
                {
                    sorderDetail.ItemConsumption = cadDetails.DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).Sum(a => a.Length);

                }
                var lb = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id && !a.SorderCadLayout.Contains("ML") && !a.SorderCadLayout.Contains("XL") && a.SorderCadLayout.Contains("L")).ToListAsync();
                lb = lb.Where(a => (a.SorderCadLayout.Substring(a.SorderCadLayout.LastIndexOf("-") + 1, 1).Equals("L"))).DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).ToList();

                if (lb.Any())
                {
                    sorderDetail.ItemConsumptionL = lb.Sum(a => a.Length);
                }
                //await sorderDetailRepository.UpdateAsync(sorderDetail);
            }
            else
            {
                if (input.StatusID.HasValue && (int)input.StatusID > (int)sorder.StatusID)
                {
                    var cadDetails = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id && a.SorderCadLayout.IndexOf("ML") > -1).ToListAsync();
                    if (cadDetails.Any())
                    {
                        sorderDetail.ItemConsumption = cadDetails.DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).Sum(a => a.Length);

                    }
                    var lb = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id && !a.SorderCadLayout.Contains("ML") && !a.SorderCadLayout.Contains("XL") && a.SorderCadLayout.Contains("L")).ToListAsync();
                    lb = lb.Where(a => (a.SorderCadLayout.Substring(a.SorderCadLayout.LastIndexOf("-") + 1, 1).Equals("L"))).DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).ToList();
                    if (lb.Any())
                    {
                        sorderDetail.ItemConsumptionL = lb.Sum(a => a.Length);
                    }

                }
                else
                {

                    var sordercadstates = await sorderCadStateRepository.GetAll().Where(a => a.SorderDetailID == input.SorderDetailID && a.SorderID == input.SorderID && a.State == SystemConfig.SorderCadStateEnums.Achieve).ToListAsync();

                    if (sordercadstates.Any())
                    {
                        var sordercadstate = new SorderCadState();
                        if (sordercadstates.Any(a => a.StatusID.HasValue))
                        {
                            sordercadstate = sordercadstates.Where(a => a.StatusID.HasValue).OrderByDescending(a => a.StatusID).FirstOrDefault();
                        }
                        else
                        {

                            sordercadstate = sordercadstates.OrderByDescending(a => a.ModifyOn).FirstOrDefault();
                        }
                        var sorderCadDetails = await (from t in sorderCadDetailRepository.GetAll()
                                                      where t.SorderCadStateID == sordercadstate.Id && t.SorderCadLayout.IndexOf("ML") > -1
                                                      select t).ToListAsync();
                        if (sorderCadDetails.Any())
                        {
                            sorderDetail.ItemConsumption = sorderCadDetails.DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).Sum(a => a.Length);
                        }
                        var lb = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id && !a.SorderCadLayout.Contains("ML") && !a.SorderCadLayout.Contains("XL") && a.SorderCadLayout.Contains("L")).ToListAsync();
                        lb = lb.Where(a => (a.SorderCadLayout.Substring(a.SorderCadLayout.LastIndexOf("-") + 1, 1).Equals("L"))).DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).ToList();
                        if (lb.Any())
                        {
                            sorderDetail.ItemConsumptionL = lb.Sum(a => a.Length);
                        }
                    }

                }

            }
            var modelqtys = await eTCadManager.GetDetailModelQty(new GetDetailModelQtyInputDto() { SorderIDs = new List<Guid>() { input.SorderID } });

            var modelqty = modelqtys.FirstOrDefault(a => a.SorderDetailID == input.SorderDetailID);
            var MultipleNumber = 1;
            if (modelqty != null && modelqty.IsSpecial)
            {
                MultipleNumber = modelqty.MultipleNumber;
            }
            #region 根据面料纹理计算耗量
            var list = await sYS_ItemTextureLengthAppService.CalculateItemLength(new List<SYS.Dtos.CalculateItemLengthInputDto>() { new SYS.Dtos.CalculateItemLengthInputDto() { ItemID = sorderDetail.ItemID, Qty = sorderDetail.ItemConsumption, SorderDetailID = sorderDetail.Id, MultipleNumber = MultipleNumber } });
            if (list.Any())
            {
                sorderDetail.ItemConsumption = list.FirstOrDefault()?.Qty;
            }
            else
            {
                //如果没有数据 要获得小数
                sorderDetail.ItemConsumption = Helper.Helper.Ceiling(sorderDetail.ItemConsumption.Value / 100, 2);
            }
            #endregion

            //sorderDetail.ItemConsumption = sorderDetail.ItemConsumption.Value;
            sorderDetail.ItemConsumptionL = XiaoMa.XMMTM.Helper.Helper.Ceiling(sorderDetail.ItemConsumptionL.Value / 100, 2) * MultipleNumber;
            await sorderDetailRepository.UpdateAsync(sorderDetail);

            var mes = $"CAD返回订单:{sorder.Code}面料耗量:{sorderDetail.ItemConsumption },里布耗量:{sorderDetail.ItemConsumptionL}";
            await sorderLogRepository.InsertAsync(new SorderLog() { ActionID = ODM_SorderAction.Submit, CreateOn = DateTime.Now, IsActive = true, CreateBy = "CAD耗量返回", Id = Guid.NewGuid(), SorderID = sorder.Id, StatusID = sorder.StatusID, StatusID1 = sorder.StatusID, Remark = mes });
            await xiaoMaLog.Log(mes, "updateSorderDetailLen", "XiaoMa.XMMTM.App.CAD.CAD_ETCADAppService");
        }


        public async Task updateSorderDetailLenNew(SorderCadState input)
        {        //更新耗量信息
            var sorderDetails = await sorderDetailRepository.GetAll().AsNoTracking().Where(a => a.SorderID == input.SorderID).OrderBy(a => a.LineNum).ToListAsync();
            var AllCadDetails = await sorderCadDetailRepository.GetAll().Where(a => a.SorderCadStateID == input.Id).ToListAsync();
            var sorder = await sorderRepository.GetAsync(input.SorderID);
            var mesmsg = "";
            foreach (var sorderDetail in sorderDetails)
            {
                sorderDetail.ItemConsumption = 0;
                sorderDetail.ItemConsumptionL = 0;
                var cadDetails = AllCadDetails.Where(a => a.SorderDetailID == sorderDetail.Id).ToList();
                if (!cadDetails.Any())
                {
                    continue;
                }
                sorderDetail.ItemConsumption = cadDetails.Where(a => a.SorderCadLayout.IndexOf("ML") > -1).DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).Sum(a => a.Length);
                sorderDetail.ItemConsumptionL = cadDetails.Where(a => !a.SorderCadLayout.Contains("ML") && !a.SorderCadLayout.Contains("XL") && a.SorderCadLayout.Contains("L")).Where(a => (a.SorderCadLayout.Substring(a.SorderCadLayout.LastIndexOf("-") + 1, 1).Equals("L"))).DistinctBy(a => new { a.ItemID, a.SorderCadLayout }).Sum(a => a.Length);

                var modelqtys = await eTCadManager.GetDetailModelQty(new GetDetailModelQtyInputDto() { SorderIDs = new List<Guid>() { input.SorderID } });

                var modelqty = modelqtys.FirstOrDefault(a => a.SorderDetailID == input.SorderDetailID);
                var MultipleNumber = 1;
                if (modelqty != null && modelqty.IsSpecial)
                {
                    MultipleNumber = modelqty.MultipleNumber;
                }
                #region 根据面料纹理计算耗量
                var list = await sYS_ItemTextureLengthAppService.CalculateItemLength(new List<SYS.Dtos.CalculateItemLengthInputDto>() { new SYS.Dtos.CalculateItemLengthInputDto() { ItemID = sorderDetail.ItemID, Qty = sorderDetail.ItemConsumption, SorderDetailID = sorderDetail.Id, MultipleNumber = MultipleNumber } });
                if (list.Any())
                {
                    sorderDetail.ItemConsumption = list.FirstOrDefault()?.Qty;
                }
                else
                {
                    //如果没有数据 要获得小数
                    sorderDetail.ItemConsumption = Helper.Helper.Ceiling(sorderDetail.ItemConsumption.Value / 100, 2);
                }
                #endregion
                sorderDetail.ItemConsumptionL = XiaoMa.XMMTM.Helper.Helper.Ceiling(sorderDetail.ItemConsumptionL.Value / 100, 2) * MultipleNumber;
                sorderDetail.ModifyOn = DateTime.Now;
                mesmsg += $"CAD返回订单并计算:{sorder.Code}-{sorderDetail.LineNum}:面料耗量:{sorderDetail.ItemConsumption },里布耗量:{sorderDetail.ItemConsumptionL}</br>";
            }
            await sorderDetailRepository.GetDbContext().BulkUpdateAsync(sorderDetails);
            await xiaoMaLog.Log(mesmsg, "updateSorderDetailLenNew", "XiaoMa.XMMTM.App.CAD.CAD_ETCADAppService");
            await sorderLogRepository.InsertAsync(new SorderLog() { ActionID = ODM_SorderAction.Submit, CreateOn = DateTime.Now, IsActive = true, CreateBy = "CAD耗量返回", Id = Guid.NewGuid(), SorderID = sorder.Id, StatusID = sorder.StatusID, StatusID1 = sorder.StatusID, Remark = mesmsg });

        }
        /// <summary>
        /// 更新cad订单状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAllowAnonymous]
        public async Task UpdateState(CAD_ETUpdateStateInputDto input)
        {
            //if (string.IsNullOrEmpty(input.Key))
            //{
            //    return;
            //}
            //if (!await ExistKeyAsync(input.Key))
            //{
            //    throw new UserFriendlyException("密钥错误");
            //}
            var sorder = await sorderRepository.GetAll().Where(a => a.Code.Equals(input.ID)).FirstOrDefaultAsync();

            if (sorder == null)
            {
                throw new UserFriendlyException("为查询到此订单");
            }
            var entity = await sorderCadStateRepository.GetAll().Where(a => a.SorderID == sorder.Id).FirstOrDefaultAsync();
            if (entity == null)
            {
                throw new UserFriendlyException("为查询到此订单");
            }
            switch (input.State)
            {
                case SystemConfig.SorderCadStateEnums.Received:
                    entity.State = SystemConfig.SorderCadStateEnums.Received;
                    break;
                case SystemConfig.SorderCadStateEnums.Exception:
                    entity.Count += 1;
                    entity.State = SystemConfig.SorderCadStateEnums.Exception;
                    break;
                default:
                    entity.Count += 1;
                    entity.State = SystemConfig.SorderCadStateEnums.Exception;
                    break;
            }
            entity.ModifyOn = DateTime.Now;
            entity.ModifyBy = "ETCAD";
            entity.Messages = input.Message;
            await sorderCadStateRepository.UpdateAsync(entity);
        }
        private async Task<bool> ExistKeyAsync(string key)
        {
            return await cadKeyManager.CheckSecretKey(key);

        }

        #region cad 新接口

        public async Task<string> createpwd(string input)
        {
            return await cadKeyManager.CreateSecretKey(input);
        }
        public async Task<string> decryptpwd(string input)
        {
            return await cadKeyManager.DecryptKey(input);
        }
        /// <summary>
        /// http://api-mtm-test.cnjyy.cn/api/XMMTM/cAD_ETCAD/get
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAllowAnonymous]
        [DontWrapResult]
        public async Task<GetETCADOutputDto> Get(GetETCADInputDto input)
        {
            var dto = new GetETCADOutputDto();
            try
            {
                var b = await cadKeyManager.CheckUser(input.user, input.password);
                if (!b)
                {
                    dto.errcode = 10;
                    dto.errmsg = "账号密码错误";
                    dto.callback = null;
                    dto.token = null;
                    return dto;
                }
            }
            catch (Exception)
            {

                var b = await cadKeyManager.CheckUser(input.user, input.password);
                if (!b)
                {
                    dto.errcode = 10;
                    dto.errmsg = "账号密码错误";
                    dto.callback = null;
                    dto.token = null;
                    return dto;
                }
            }
            dto.data = await getData(null);

            if (dto.data == null || dto.data.Cloth == null || dto.data.Cloth.Count == 0)
            {
                dto.errcode = 100;
                dto.errmsg = "暂无数据";
                dto.callback = null;
                dto.token = null;
                return dto;
            }
            //todo etcad回调地址
            dto.callback = await cadKeyManager.CallBackUrl();
            dto.upload = await cadKeyManager.CallBackSvgUrl();
            dto.token = await cadKeyManager.CreateToken(input.user, input.password, dto.data.Name);
            return dto;
        }
        [HttpPost]
        [AbpAllowAnonymous]
        [DontWrapResult]
        public async Task UpdateNew(UpdateETCADInput input)
        {
            try
            {
                var tokenstr = await cadKeyManager.CheckToken(input.token);
                if (string.IsNullOrEmpty(tokenstr))
                {
                    throw new UserFriendlyException("Token为空");
                }
                var tokenarr = tokenstr.Split("|");
                if (tokenarr.Length != 3)
                {
                    throw new UserFriendlyException("Token错误");
                }
                if (input.data == null || !input.data.Any())
                {
                    throw new UserFriendlyException("数据包不能为空!");
                }
                var b = await cadKeyManager.CheckUser(tokenarr[0], tokenarr[1]);
                if (!b)
                {
                    throw new UserFriendlyException("账号密码错误");
                }
                await updateData(input.data, tokenarr[2]);
            }
            catch (Exception ex)
            {

                throw new UserFriendlyException("数据错误" + ex.Message);
            }

        }
        #endregion
        /// <summary>
        /// 上传svg 数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAllowAnonymous]
        [DontWrapResult]
        public async Task SaveSvg(UpdateETCADSvgInput input)
        {
            //var tokenstr = await cadKeyManager.CheckToken(input.token);
            //if (string.IsNullOrEmpty(tokenstr))
            //{
            //    throw new UserFriendlyException("Token为空");
            //}
            //var tokenarr = tokenstr.Split("|");
            //if (tokenarr.Length != 3)
            //{
            //    throw new UserFriendlyException("Token错误");
            //}
            //if (input.data == null || !input.data.Any())
            //{
            //    throw new UserFriendlyException("数据包不能为空!");
            //}
            //var b = await cadKeyManager.CheckUser(tokenarr[0], tokenarr[1]);
            //if (!b)
            //{
            //    throw new UserFriendlyException("账号密码错误");
            //}
            if (input.File == null)
            {
                throw new UserFriendlyException("svg文件为空！");
            }
            await SaveFile(input.File, input.bedID, input.data);


        }
        private IFormFile Create()
        {
            using (var stream = File.OpenRead("testfile.jpg"))
            {
                FormFile file = new FormFile(stream, 0, stream.Length, null, Path.GetFileName(stream.Name))
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "image/jpeg"
                };
                return file;
            }
        }
        private async Task SaveFile(IFormFile File, Guid cadDetailID, string FileData)
        {
            if (File == null)
            {
                return;
            }
            var sordercadDetail = await sorderCadDetailRepository.GetAll().FirstOrDefaultAsync(a => a.Id == cadDetailID);
            if (sordercadDetail == null)
            {
                return;
            }
            var FileSuffix = System.IO.Path.GetExtension(File.FileName);
            var caddetail = File.FileName.Substring(0, File.FileName.IndexOf(FileSuffix));

            try
            {

                var fileResDto = await fileServer.SaveSvg(new FileServer.Dtos.FileServerInputDto { File = File, FileGroup = Enums.FileGroupEnums.CAD, ChangeGuidFileName = true, FileName = File.FileName, FileData = FileData });
                var sysFileDto = new FileManagement()
                {
                    IsActive = true,
                    FileGroup = fileResDto.FileGroup,
                    Id = Guid.NewGuid(),
                    FileName = fileResDto.FileName,
                    FileSuffix = fileResDto.FileSuffix,
                    FileType = fileResDto.FileType,
                    Path = fileResDto.Path,
                    RootPath = fileResDto.RootPath,
                    CreateOn = DateTime.Now,
                    //CreateID = UserID,
                    CreateBy = "cad排料图回传",
                    Remark = $"排料图名称:{ File.FileName}"
                };
                await fileManagementRepository.InsertAsync(sysFileDto);
                sordercadDetail.FileID = sysFileDto.Id;
                await sorderCadDetailRepository.UpdateAsync(sordercadDetail);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

        }
    }
}

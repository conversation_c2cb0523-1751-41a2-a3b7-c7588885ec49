/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SewListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 15:08:46
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SewListAppService : XMMTMAppServiceBase, IMOM_SewListAppService
    {
        private readonly IRepository<SewList, Guid> repository;
        private readonly IRepository<SewBase, Guid> sewBaserepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SewListAppService(
       IRepository<SewList, Guid> repository,
       IRepository<SewBase, Guid> sewBaserepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sewBaserepository = sewBaserepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SewListGetAllOutputDto>> Get(MOM_SewListGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in sewBaserepository.GetAll() on t.SewBaseID equals t1x.Id into g
                         from t1 in g.DefaultIfEmpty()
                         //where t.IsActive
                         select new MOM_SewListGetAllOutputDto()
                         {
                             IsActive = t.IsActive,
                             Id = t.Id,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             ElemSeq = t.ElemSeq,
                             ModelElemTypeID = t.ModelElemTypeID,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             SewBaseID = t.SewBaseID,
                             Remark = t.Remark,
                             SewBaseText = t1.Code + ":" + t1.CodeName,
                             ModelElemTypeText = t.ModelElemTypeID.GetDescription()
                         }).WhereIf(input.SewBaseID.HasValue,a=>a.SewBaseID==input.SewBaseID.Value)
                         .WhereIf(input.ModelElemTypeID.HasValue, a =>(int)a.ModelElemTypeID==input.ModelElemTypeID.Value)
                         .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.ModelElemTypeID).ThenByDescending(a=>a.ModifyOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SewListGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SewListGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SewListDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<SewList>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SewListDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SewListDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
    }
}

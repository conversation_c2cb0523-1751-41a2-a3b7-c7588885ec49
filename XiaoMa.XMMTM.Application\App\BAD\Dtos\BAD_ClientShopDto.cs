﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientShopDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/24/星期五 17:03:50
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientShopDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ClientShopDto : EntityDto<Guid?>
    {
        public string Code { set; get; }
        public string CodeName { set; get; }
        /// <summary>
        /// 联络人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 邮编
        /// </summary>
        public string ZipCode { set; get; }
        /// <summary>
        /// 简称
        /// </summary>
        public string StateCode { set; get; }
        /// <summary>
        /// 城市
        /// </summary>
        public string City { set; get; }
        /// <summary>
        /// 国家
        /// </summary>
        public string Country { set; get; }
        public string CountryCN { set; get; }
        /// <summary>
        /// 国家
        /// </summary>
        public string CountryCode { set; get; }
        /// <summary>
        /// 州省区
        /// </summary>
        public string StateProvince { set; get; }
        /// <summary>
        /// 州省区代码
        /// </summary>
        public string StateProvinceCode { set; get; }

        /// <summary>
        /// 地名
        /// </summary>
        public string LocationName { set; get; }

        /// <summary>
        /// 
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Address1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid ClientID { get; set; }

        public virtual bool IsActive { set; get; } = true;
    }
}

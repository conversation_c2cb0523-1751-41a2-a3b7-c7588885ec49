﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelSizeColumnBodyAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/4/3/星期六 14:55:56
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelSizeColumnBodyAppService : XMMTMAppServiceBase, IMOM_ModelSizeColumnBodyAppService
    {
        private readonly IRepository<ModelSizeColumnBody, Guid> repository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<Body, Guid> bodyRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelSizeColumnBodyAppService(
       IRepository<ModelSizeColumnBody, Guid> repository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<Body, Guid> bodyRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.bodyRepository = bodyRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelSizeColumnBodyGetAllOutputDto>> Get(MOM_ModelSizeColumnBodyGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in sizeColumnRepository.GetAll() on t.SizeColumnID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in bodyRepository.GetAll() on t.BodyID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                             // where t.IsActive
                         select new MOM_ModelSizeColumnBodyGetAllOutputDto()
                         {
                             BodyCode = t2.Code,
                             BodyID = t.BodyID,
                             BodyName = t2.CodeName,
                             BodyValue = t2.Value,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             SizeColumnCode = t1.Code,
                             SizeColumnID = t.SizeColumnID,
                             SizeColumnName = t1.CodeName,
                             Sort = t.Sort, Value=t.Value
                              
                         })
                         .WhereIf(input.BodyID.HasValue,a=>a.BodyID==input.BodyID.Value)
                         .WhereIf(input.SizeColumnID.HasValue,a=>a.SizeColumnID == input.SizeColumnID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.BodyCode.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.BodyName.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.SizeColumnCode.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.SizeColumnName.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sort).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelSizeColumnBodyGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelSizeColumnBodyDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelSizeColumnBody>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelSizeColumnBodyDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelSizeColumnBodyDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelSizeColumnBodyDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.SizeColumnID.Equals(input.SizeColumnID) && a.BodyID.Equals(input.BodyID));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.SizeColumnID.Equals(input.SizeColumnID) && a.BodyID.Equals(input.BodyID) && a.Id != input.Id.Value);
            }

        }
    }
}

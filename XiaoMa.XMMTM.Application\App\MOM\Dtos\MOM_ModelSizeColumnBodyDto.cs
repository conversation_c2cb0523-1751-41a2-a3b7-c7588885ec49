﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelSizeColumnBodyDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2021/4/3/星期六 14:55:25
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelSizeColumnBodyDto新增/修改数据对象Dto
    /// </summary>
    [AutoMapTo(typeof(ModelSizeColumnBody))]
    public class MOM_ModelSizeColumnBodyDto : EntityDto<Guid?>
    {
        public Guid SizeColumnID { set; get; }
        public Guid BodyID { set; get; }
        public int? Sort { set; get; }
        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }
        public decimal? Value { set; get; }
    }
}

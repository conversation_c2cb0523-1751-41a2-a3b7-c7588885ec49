﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_BodyAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 13:35:53
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_BodyAppService : XMMTMAppServiceBase, IMOM_BodyAppService
    {
        private readonly IRepository<Body, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public MOM_BodyAppService(
       IRepository<Body, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_BodyGetAllOutputDto>> Get(MOM_BodyGetAllInputDto input)
        {
            var query = repository.GetAll()
              .Where(a => a.BodyListID == input.Id)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Code).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<MOM_BodyGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_BodyGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_BodyDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                CheckMinMaxValye(entity);
                var oldentity = ObjectMapper.Map<Body>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_BodyDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                CheckMinMaxValye(entity);
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_BodyDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        private void CheckMinMaxValye(MOM_BodyDto input)
        {
            if (input.AllowMax < input.Max)
            {
                throw new UserFriendlyException("最大人工调整值不能小于最大值");
            }
            if (input.AllowMin > input.Min)
            {
                throw new UserFriendlyException("最小人工调整值不能大于最小值");
            }
            if (!input.Max.HasValue && input.AllowMax.HasValue)
            {
                throw new UserFriendlyException("错误,最大值为空,最大人工调整值也必须为空");
            }
            if (!input.Min.HasValue && input.AllowMin.HasValue)
            {
                throw new UserFriendlyException("错误,最小值为空,最小人工调整值也必须为空");
            }
            if (input.Max.HasValue && input.Min.HasValue && input.Max.Value <= input.Min.Value)
            {
                throw new UserFriendlyException("最大值不能小于等于最大值");
            }
            if (input.AllowMax.HasValue && input.AllowMin.HasValue && input.AllowMax.Value <= input.AllowMin.Value)
            {
                throw new UserFriendlyException("最小人工调整值不能大于等于最小人工调整值");
            }
        }
        protected async Task<bool> ExistCodeAsync(MOM_BodyDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

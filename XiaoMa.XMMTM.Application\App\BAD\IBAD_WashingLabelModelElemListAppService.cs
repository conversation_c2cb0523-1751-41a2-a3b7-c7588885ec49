﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_WashingLabelModelElemListAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2022年10月10日,星期一 14:01:43
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_WashingLabelModelElemListAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_WashingLabelModelElemListGetAllOutputDto>> Get(BAD_WashingLabelModelElemListGetAllInputDto input);
        Task Adds(List<BAD_WashingLabelModelElemListDto> input);
        Task Updates(List<BAD_WashingLabelModelElemListDto> input);
        Task Deletes(List<BAD_WashingLabelModelElemListDto> input);
    }
}

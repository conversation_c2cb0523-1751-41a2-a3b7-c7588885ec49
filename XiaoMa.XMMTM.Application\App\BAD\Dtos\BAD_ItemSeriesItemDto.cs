﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemSeriesItemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 19:46:16
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemSeriesItemDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemSeriesItemDto : EntityDto<Guid?>
    {
        public Guid ItemSeriesID { get; set; }
        public Guid ItemID { get; set; }
        public string SizeCode { get; set; }
        public string Remark { set; get; }
        public virtual bool IsActive { set; get; }
    }
}

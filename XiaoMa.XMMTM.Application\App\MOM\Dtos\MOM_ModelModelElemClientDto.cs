﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemClientDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/12/2/星期三 9:52:38
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemClientDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelModelElemClientDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 版型ID
        /// </summary>
        public Guid? ModelID { get; set; }

        /// <summary>
        /// 款式明细ID
        /// </summary>
        public Guid ModelElemID { get; set; }
        public Guid ModelElemListID { get; set; }

        public Guid? ItemID { set; get; }
        /// <summary>
        /// 客户
        /// </summary>
        public Guid ClientID { set; get; }

        /// <summary>
        /// 是否绑定
        /// </summary>

        public ModelModelElemClientStateEnums State { get; set; }

        public  bool IsActive { get; set; } = true;
        public string Remark { set; get; }
    }
}

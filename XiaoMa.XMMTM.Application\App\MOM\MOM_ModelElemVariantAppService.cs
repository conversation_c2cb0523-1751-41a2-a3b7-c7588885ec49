﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemVariantAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/6/星期四 15:58:54
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemVariantAppService : XMMTMAppServiceBase, IMOM_ModelElemVariantAppService
    {
        private readonly IRepository<ModelElemVariant, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemVariantAppService(
       IRepository<ModelElemVariant, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemVariantGetAllOutputDto>> Get(MOM_ModelElemVariantGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelElemRepository.GetAll() on t.ModelElemID1 equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         select new MOM_ModelElemVariantGetAllOutputDto()
                         {
                             Id = t.Id,
                             Variant = t.Variant,
                             Value = t.Value,
                             Remark = t.Remark,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelElemID = t.ModelElemID,
                             ModelElemID1 = t.ModelElemID1,
                             ModelElemText = t1.Code + ":" + t1.CodeName,
                             ModelElemCode = t1.Code,
                             ModelElemCodeName = t1.CodeName,
                             ModelElem1Text = t2 == null ? null : t2.Code + ": " + t2.CodeName,
                             ModelElemListID = t1.ModelElemListID,
                             ModelElemListText = t3.Code + ": " + t3.CodeName,
                         })
                         .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemText.Contains(input.Text) || a.ModelElem1Text.Contains(input.Text) || a.ModelElemListText.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            foreach (var item in result)
            {
                item.ModelElemText = item.ModelElemCode + ":" + item.ModelElemCodeName;
            }
            return new PagedResultDto<MOM_ModelElemVariantGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemVariantDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<ModelElemVariant>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemVariantDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemVariantDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleCheckDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2022年11月1日,星期二 13:40:11
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeRuleCheckDto新增/修改数据对象Dto
    /// </summary>
    [AutoMapTo(typeof(SizeRuleCheck))]
    public class MOM_SizeRuleCheckDto : EntityDto<Guid?>
    {
        public SYS_SorderSizeType SorderSizeType { set; get; }
        public ODM_SorderType SorderType { set; get; }

        public bool IsCheckNull { set; get; }
        public Guid SizeColumnID { set; get; }
        public SizeRuleCheckTypeEnums SizeRuleCheckType { set; get; }
    
        public decimal? Value { set; get; }
        public Guid? SizeColumnID1 { set; get; }
        public Guid? SizeColumnID2 { set; get; }

        public Guid? BodyListID1 { set; get; }
        public Guid? BodyListID2 { set; get; }

        public decimal? MaxValue { set; get; }

        public decimal? MinValue { set; get; }
        /// <summary>
        /// 胸围体型b
        /// </summary>
        public bool? HasSizeElemB { set; get; }
        public bool? HasSizeElemA { set; get; }
        /// <summary>
        /// 体型C
        /// </summary>
        public bool? HasSizeElemC { set; get; }
        /// <summary>
        /// 臀围体型D
        /// </summary>
        public bool? HasSizeElemD { set; get; }
        public bool ? IsActive { set; get; }
        public int? Sort { set; get; }
        public string Remark{ set; get; }
        public Guid? GroupID { set; get; }
        public decimal? IsManualMaxValue1 { set; get; }
        public decimal? IsManualMinValue1 { set; get; }
        public decimal? IsManualMaxValue2 { set; get; }
        public decimal? IsManualMinValue2 { set; get; }
    }
}

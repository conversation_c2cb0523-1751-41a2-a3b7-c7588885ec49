﻿{
    "Summary":  {
                    "TotalAnalyzed":  10,
                    "ContentDifference":  10,
                    "GeneratedAt":  "2025-09-16 15:45:20",
                    "NamespaceOnly":  0
                },
    "Files":  [
                  {
                      "FileName":  "XMMTMCoreModule.cs",
                      "RelativePath":  "XMMTMCoreModule.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\XMMTMCoreModule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\XMMTMCoreModule.cs",
                      "SourceLines":  72,
                      "TargetLines":  102,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM\r",
                      "SourceUsings":  21,
                      "TargetUsings":  28,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  4459,
                                        "Source":  3322
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1757661720974)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "XMMTMConsts.cs",
                      "RelativePath":  "XMMTMConsts.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\XMMTMConsts.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\XMMTMConsts.cs",
                      "SourceLines":  15,
                      "TargetLines":  15,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM\r",
                      "SourceUsings":  0,
                      "TargetUsings":  0,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  364,
                                        "Source":  345
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750834770134)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "AppVersionHelper.cs",
                      "RelativePath":  "AppVersionHelper.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\AppVersionHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\AppVersionHelper.cs",
                      "SourceLines":  28,
                      "TargetLines":  28,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM\r",
                      "SourceUsings":  3,
                      "TargetUsings":  3,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  765,
                                        "Source":  733
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750645102937)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "RateLimitingActionFilter.cs",
                      "RelativePath":  "RateLimitingActionFilter.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\RateLimitingActionFilter.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\RateLimitingActionFilter.cs",
                      "SourceLines":  94,
                      "TargetLines":  93,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM\r",
                      "SourceUsings":  8,
                      "TargetUsings":  7,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  3499,
                                        "Source":  3440
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750834770139)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "PermissionNames.cs",
                      "RelativePath":  "Authorization\\PermissionNames.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\PermissionNames.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\PermissionNames.cs",
                      "SourceLines":  13,
                      "TargetLines":  13,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.Authorization",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.Authorization\r",
                      "SourceUsings":  0,
                      "TargetUsings":  0,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  497,
                                        "Source":  480
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750659703819)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "XMMTMAuthorizationProvider.cs",
                      "RelativePath":  "Authorization\\XMMTMAuthorizationProvider.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\XMMTMAuthorizationProvider.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\XMMTMAuthorizationProvider.cs",
                      "SourceLines":  30,
                      "TargetLines":  30,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.Authorization",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.Authorization\r",
                      "SourceUsings":  3,
                      "TargetUsings":  3,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  1225,
                                        "Source":  1191
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1757919458325)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "High",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "SSOUserPermissionCacheItem.cs",
                      "RelativePath":  "Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "SourceLines":  21,
                      "TargetLines":  21,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.Authorization.Users",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.Authorization.Users\r",
                      "SourceUsings":  3,
                      "TargetUsings":  3,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  405,
                                        "Source":  380
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750645099432)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "Medium",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "CadKeyManager.cs",
                      "RelativePath":  "CAD\\CadKeyManager.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\CadKeyManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\CadKeyManager.cs",
                      "SourceLines":  164,
                      "TargetLines":  163,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.CAD",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.CAD\r",
                      "SourceUsings":  8,
                      "TargetUsings":  8,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  4993,
                                        "Source":  4790
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1757919679697)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "Medium",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "ETCadManger.cs",
                      "RelativePath":  "CAD\\ETCadManger.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\ETCadManger.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\ETCadManger.cs",
                      "SourceLines":  791,
                      "TargetLines":  858,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.CAD",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.CAD\r",
                      "SourceUsings":  15,
                      "TargetUsings":  17,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  46577,
                                        "Source":  41393
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1757225530235)\/",
                                           "Source":  "\/Date(1748860367000)\/"
                                       },
                      "Priority":  "Medium",
                      "DifferenceType":  "Content Difference"
                  },
                  {
                      "FileName":  "ICadKeyManager.cs",
                      "RelativePath":  "CAD\\ICadKeyManager.cs",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\ICadKeyManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\ICadKeyManager.cs",
                      "SourceLines":  40,
                      "TargetLines":  39,
                      "SourceNamespaces":  "namespace XiaoMa.XMMTM.CAD",
                      "TargetNamespaces":  "namespace XiaoMa.Shop.XMMTM.CAD\r",
                      "SourceUsings":  4,
                      "TargetUsings":  4,
                      "ContentIdentical":  false,
                      "SizeBytes":  {
                                        "Target":  1172,
                                        "Source":  1166
                                    },
                      "LastModified":  {
                                           "Target":  "\/Date(1750645099549)\/",
                                           "Source":  "\/Date(1720685787000)\/"
                                       },
                      "Priority":  "Medium",
                      "DifferenceType":  "Content Difference"
                  }
              ]
}

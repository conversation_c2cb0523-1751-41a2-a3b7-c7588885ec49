﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2021/1/11/星期一 11:14:14
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemImageDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemImageDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 图片链接（虚拟）
        /// </summary>

        public string ImageUrl { get; set; }

        /// <summary>
        /// 图片物理路径
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public Guid ItemID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}

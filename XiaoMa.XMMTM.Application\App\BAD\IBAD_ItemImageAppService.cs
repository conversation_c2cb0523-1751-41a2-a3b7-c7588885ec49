﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemImageAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2021/1/11/星期一 11:12:21
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemImageAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemImageGetAllOutputDto>> Get(BAD_ItemImageGetAllInputDto input);
        Task Adds(IFormFile file, Guid ItemID);
        Task Deletes(List<BAD_ItemImageDto> input);

        Task<List<ItemImagesByIdOutputDto>> GetByItemIDs(ItemImagesByIdGetInputDto input);
    }
}

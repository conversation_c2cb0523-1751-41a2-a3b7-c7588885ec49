/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemRuleAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/11/17/星期二 20:33:58
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.ModelElemRuleManager;
using XiaoMa.XMMTM.ModelElemRuleManager.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemRuleAppService : XMMTMAppServiceBase, IMOM_ModelElemRuleAppService
    {
        private readonly IRepository<ModelElemRule, Guid> repository;
        private readonly IRepository<ModelElemRuleDetail, Guid> modelElemRuleDetailRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        private readonly IRuleManager ruleManager;

        public MOM_ModelElemRuleAppService(
       IRepository<ModelElemRule, Guid> repository,
       IRepository<ModelElemRuleDetail, Guid> modelElemRuleDetailRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<Item, Guid> itemRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper,
            IRuleManager ruleManager
         )
        {
            this.repository = repository;
            this.modelElemRuleDetailRepository = modelElemRuleDetailRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.modelElemRepository = modelElemRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.itemRepository = itemRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
            this.ruleManager = ruleManager;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemRuleGetAllOutputDto>> Get(MOM_ModelElemRuleGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemListRepository.GetAll() on t.ModelElemListID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2 in groupRepository.GetAll() on t1.GroupID equals t2.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         select new MOM_ModelElemRuleGetAllOutputDto
                         {
                             BomRuleType = t.BomRuleType,
                             ConditionStr = t.ConditionStr,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             ExternalSort = t.ExternalSort,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             ModelElemListID = t.ModelElemListID,
                             ModelRuleType = t.ModelRuleType,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             OperationString = t.OperationString,
                             Remark = t.Remark,
                             Sort = t.Sort,
                             BomRuleTypeText = t.BomRuleType.GetDescription(),
                             ModelRuleTypeText = t.ModelRuleType.GetDescription(),
                             ModelElemListText = t1 != null ? t1.Code + ":" + t1.CodeName : "",
                             GenderID = t1 == null ? true : t1.GenderID,
                             GroupID = t1.GroupID,
                             GroupText = t2.CodeName,
                         })
                         .WhereIf(input.GenderID.HasValue, a => a.GenderID == input.GenderID.Value)
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                         .WhereIf(input.IsActive.HasValue, a => a.IsActive == input.IsActive.Value)
                        .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID)
                        .WhereIf(input.ModelRuleType.HasValue, a => a.ModelRuleType == input.ModelRuleType)
                        .WhereIf(input.BomRuleType.HasValue, a => a.BomRuleType == input.BomRuleType)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemListText.Contains(input.Text) || a.OperationString.ToLower().Contains(input.Text.ToLower()) || a.ConditionStr.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelElemRuleGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemRuleGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemRuleDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的公式");
                }
                var oldentity = ObjectMapper.Map<ModelElemRule>(entity);
                var list = new List<ModelElemRuleManager.Dtos.RuleOperand>();
                list.Add(new ModelElemRuleManager.Dtos.RuleOperand() { ExpStr = oldentity.ConditionStr });
                list.Add(new ModelElemRuleManager.Dtos.RuleOperand() { ExpStr = oldentity.OperationString });
                await ruleManager.RuleModelRegistryAsync(list);
                var res = await ruleManager.ComputeRuleAsync(entity.ConditionStr);
                var res1 = await ruleManager.ComputeRuleAsync(entity.OperationString);
                var ruleid = await repository.InsertAndGetIdAsync(oldentity);
                await ruleManager.UpdateRuleDetailAsync(ruleid);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemRuleDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的公式");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                var list = new List<ModelElemRuleManager.Dtos.RuleOperand>();
                list.Add(new ModelElemRuleManager.Dtos.RuleOperand() { ExpStr = oldentity.ConditionStr });
                list.Add(new ModelElemRuleManager.Dtos.RuleOperand() { ExpStr = oldentity.OperationString });
                await ruleManager.RuleModelRegistryAsync(list);
                var res = await ruleManager.ComputeRuleAsync(entity.ConditionStr);
                var res1 = await ruleManager.ComputeRuleAsync(entity.OperationString);
                await repository.UpdateAsync(oldentity);
                await ruleManager.UpdateRuleDetailAsync(oldentity.Id);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemRuleDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        /// <summary>
        /// 公式计算
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [UnitOfWork]
        public async Task<List<ElemRuleOutputDto>> CalculateElemRule(List<ElemRuleInputDto> inputs)
        {
            var list = new List<ElemRuleOutputDto>();

            var _ruleIDs = inputs.Select(a => a.ModelElemRuleID).Distinct().ToList();
            var elemRules = await (from t in repository.GetAll()
                                       //join t1 in modelElemRuleDetailRepository.GetAll() on t.Id equals t1.ModelElemRuleID
                                   where _ruleIDs.Contains(t.Id)
                                   select t).ToListAsync();

            var _modelElems = inputs.SelectMany(a => a.ModelElems).Where(a => a.ModelElemID.HasValue).DistinctBy(a => a.ModelElemID.Value).Select(a => a).ToList();
            var _sizeElem = inputs.SelectMany(a => a.SizeColumns).DistinctBy(a => a.SizeColumnID).Select(a => a).ToList();
            var _other = inputs.SelectMany(a => a.OtherElemRules).ToList();
            var _otherItem = _other.Where(a => a.ItemID.HasValue).Select(a => a.ItemID.Value).Distinct().ToList();
            var _otherTpye = _other.Where(a => a.SorderType.HasValue).Select(a => a.SorderType.Value).Distinct().ToList();
            var opeList = new List<ModelElemRuleManager.Dtos.Operand>();
            #region M款式 --款式明细
            var _modelELemIds = _modelElems.Where(a => a.ModelElemID.HasValue).Select(a => a.ModelElemID).ToList();
            var _modelElemQuery = await (from t in modelElemListRepository.GetAll()
                                         join t1 in modelElemRepository.GetAll() on t.Id equals t1.ModelElemListID
                                         where _modelELemIds.Contains(t1.Id)
                                         select new
                                         {
                                             ModelElemListID = t.Id,
                                             MCode = t.Code,
                                             Code = t1.Code,
                                         }).ToListAsync();
            var _modelElemOp = (from t in _modelElems
                                join t1x in _modelElemQuery on t.ModelElemListID equals t1x.ModelElemListID into t1xx
                                from t1 in t1xx.DefaultIfEmpty()
                                where t.ModelElemID.HasValue
                                select new ModelElemRuleManager.Dtos.Operand()
                                {
                                    IsRegistry = true,
                                    Key = "M" + t1.MCode,
                                    Code = t1.MCode,
                                    C = t1.Code,
                                    Q = t.Qty.HasValue ? ((double)t.Qty.Value) : 0,
                                    ID = t1.ModelElemListID,
                                    Type = ModelElemRuleManager.Dtos.OperandType.M
                                }).ToList();
            opeList.AddRange(_modelElemOp);
            #endregion

            #region I 普通货号
            var _items = _modelElems.Where(a => a.ItemID.HasValue).Select(a => a).ToList();
            var _itemids = _items.Select(a => a.ItemID.Value).Distinct().ToList();
            var _itemQuery = await (from t in itemRepository.GetAll()
                                    where _itemids.Contains(t.Id)
                                    select new
                                    {
                                        ItemID = t.Id,
                                        Code = t.Code
                                    }).ToListAsync();

            var _itemOps = (from t in _items
                            join t1x in _itemQuery on t.ItemID equals t1x.ItemID into t1xx
                            from t1 in t1xx.DefaultIfEmpty()
                            select new ModelElemRuleManager.Dtos.Operand()
                            {
                                C = t1.Code,
                                ID = t1.ItemID,
                                Code = t1.Code,
                                IsRegistry = true,
                                Key = "I" + t1.Code,
                                Q = t.Qty.HasValue ? ((double)t.Qty.Value) : 0,
                                Type = ModelElemRuleManager.Dtos.OperandType.I,
                            }).ToList();


            opeList.AddRange(_itemOps);
            #endregion

            #region S 规格
            var _sizeColumnIDs = _sizeElem.Select(a => a.SizeColumnID).Distinct().ToList();
            var _sizeColumnQuery = await (from t in sizeColumnRepository.GetAll()
                                          where _sizeColumnIDs.Contains(t.Id)
                                          select new
                                          {
                                              SizeColumnID = t.Id,
                                              t.Code

                                          }).ToListAsync();

            var _sizeColumnOp = (from t in _sizeElem
                                 join t1x in _sizeColumnQuery on t.SizeColumnID equals t1x.SizeColumnID into t1xx
                                 from t1 in t1xx.DefaultIfEmpty()
                                 select new ModelElemRuleManager.Dtos.Operand()
                                 {
                                     IsRegistry = true,
                                     F = t.Finish.HasValue ? (double)t.Finish.Value : 0,
                                     Code = t1.Code,
                                     Key = "S" + t1.Code,
                                     Type = ModelElemRuleManager.Dtos.OperandType.I
                                 }).ToList();

            opeList.AddRange(_sizeColumnOp);
            #endregion


            #region  LItem.C面料(配色方案)

            var _LitemOps = await (from t in itemRepository.GetAll()
                                   where _otherItem.Contains(t.Id)
                                   select new ModelElemRuleManager.Dtos.Operand()
                                   {
                                       Key = "LItem",
                                       C = t.Code,
                                       Code = t.Code,
                                       ID = t.Id,
                                       IsRegistry = true,
                                       Type = ModelElemRuleManager.Dtos.OperandType.L
                                   }).ToListAsync();
            opeList.AddRange(_LitemOps);
            #endregion

            #region  TSorderType.C (订单类型) 
            foreach (var item in _otherTpye)
            {
                opeList.Add(new ModelElemRuleManager.Dtos.Operand() { IsRegistry = true, Type = ModelElemRuleManager.Dtos.OperandType.T, Key = "TSorderType", C = item.ToString(), Code = item.ToString(), ID = (int)item });
            }
            #endregion

            #region 注册参数
            var cStr = elemRules.Select(a => a.ConditionStr).Distinct().ToList();
            var oStr = elemRules.Select(a => a.OperationString).Distinct().ToList();
            cStr = cStr.Union(oStr).ToList();

            #endregion


            #region 开始计算
            //款式明细计算
            var resElems =await CalculatesAsync(elemRules, RuleTypeEnum.RuleOfModelElem, opeList, cStr);
            list.AddRange(resElems);
            //普通货号计算
            var resItem1 =await CalculatesAsync(elemRules, RuleTypeEnum.RuleOfItmeID, opeList, cStr);
            list.AddRange(resItem1);
            //耗量计算
            var resQty =await CalculatesAsync(elemRules, RuleTypeEnum.RuleOfModelElemListQty, opeList, cStr);
            list.AddRange(resQty);
            ////配色计算（货号）
            var resItem2 =await CalculatesAsync(elemRules, RuleTypeEnum.RuleOfItme1ID, opeList, cStr);
            list.AddRange(resItem2);
            #endregion

            //lock (ruleManager)
            //{
            //    ruleManager.ReleaseRule();
            //}

            return list;
        }
        /// <summary>
        /// 根据类型计算结果
        /// </summary>
        /// <param name="list">所有相关公式列表</param>
        /// <param name="type">公式分类</param>
        /// <param name="operands">需要替换的对象(明确知道值的)</param>
        /// <param name="strs">条件字符串以及公式的合集</param>
        /// <returns></returns>
        [UnitOfWork]
        public async Task<List<ElemRuleOutputDto>> Calculate(List<ModelElemRule> list, RuleTypeEnum type, List<Operand> operands, List<string> strs)
        {
            await ruleManager.RuleModelRegistryAsync(operands, strs);
            var plist = new List<ElemRuleOutputDto>();
            var elemRules = list.Where(a => a.BomRuleType == type).GroupBy(a => a.ModelElemListID).OrderBy(a => a.FirstOrDefault().ExternalSort).Select(a => new { ModelElemListID = a.Key, ModelElemRules = a.OrderBy(a => a.Sort).ThenBy(a=>a.CreateOn).ToList() }).ToList();
            foreach (var item in elemRules)
            {
                var dto = new ElemRuleOutputDto();
                dto.ModelElemListID = item.ModelElemListID;
                dto.type = type;
                foreach (var rule in item.ModelElemRules)
                {
                    //var b = (bool)(ruleManager.ComputeRule(rule.ConditionStr));
                    var obj = await (ruleManager.ComputeRuleAsync(rule.ConditionStr));
                    var b = Convert.ToBoolean(obj);
                    if (b)
                    {
                        var res = ruleManager.ComputeRule(rule.OperationString);
                        switch (type)
                        {
                            case RuleTypeEnum.RuleOfModelElem:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ModelElemID = (Guid)res;
                                }
                                break;
                            case RuleTypeEnum.RuleOfModelElemListQty:
                                if (res != null)
                                {
                                    dto.Qty = decimal.Parse(res.ToString());
                                }
                                break;
                            case RuleTypeEnum.RuleOfItmeID:
                            case RuleTypeEnum.RuleOfItme1ID:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ItemID = (Guid)res;
                                }
                                break;
                            default:
                                break;
                        }
                        plist.Add(dto);
                        break;
                    }
                }
                //}
            }
            await ruleManager.ReleaseRuleAsync();
            return plist;
        }
        /// <summary>
        /// 同步老大款式明细计算
        /// </summary>
        /// <param name="list"></param>
        /// <param name="type"></param>
        /// <param name="operands"></param>
        /// <param name="strs"></param>
        /// <returns></returns>
        private List<ElemRuleOutputDto> Calculates(List<ModelElemRule> list, RuleTypeEnum type, List<Operand> operands, List<string> strs)
        {
            ruleManager.RuleModelRegistry(operands, strs);
            var plist = new List<ElemRuleOutputDto>();
            var elemRules = list.Where(a => a.BomRuleType == type).GroupBy(a => a.ModelElemListID).OrderBy(a => a.FirstOrDefault().ExternalSort).Select(a => new { ModelElemListID = a.Key, ModelElemRules = a.OrderBy(a => a.Sort).ToList() }).ToList();
            foreach (var item in elemRules)
            {
                var dto = new ElemRuleOutputDto();
                dto.ModelElemListID = item.ModelElemListID;
                dto.type = type;
                foreach (var rule in item.ModelElemRules)
                {
                    //var b = (bool)(ruleManager.ComputeRule(rule.ConditionStr));
                    var obj = (ruleManager.ComputeRule(rule.ConditionStr));
                    var b = Convert.ToBoolean(obj);
                    if (b)
                    {
                        var res = ruleManager.ComputeRule(rule.OperationString);
                        switch (type)
                        {
                            case RuleTypeEnum.RuleOfModelElem:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ModelElemID = (Guid)res;
                                }
                                break;
                            case RuleTypeEnum.RuleOfModelElemListQty:
                                if (res != null)
                                {
                                    dto.Qty = decimal.Parse(res.ToString());
                                }
                                break;
                            case RuleTypeEnum.RuleOfItmeID:
                            case RuleTypeEnum.RuleOfItme1ID:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ItemID = (Guid)res;
                                }
                                break;
                            default:
                                break;
                        }
                        plist.Add(dto);
                        break;
                    }
                }
                //}
            }
            ruleManager.ReleaseRule();
            return plist;

        }
        /// <summary>
        /// 异步款式明细计算
        /// </summary>
        /// <param name="list"></param>
        /// <param name="type"></param>
        /// <param name="operands"></param>
        /// <param name="strs"></param>
        /// <returns></returns>
        private async Task<List<ElemRuleOutputDto>> CalculatesAsync(List<ModelElemRule> list, RuleTypeEnum type, List<Operand> operands, List<string> strs)
        {
           await ruleManager.RuleModelRegistryAsync(operands, strs);
            var plist = new List<ElemRuleOutputDto>();
            var elemRules = list.Where(a => a.BomRuleType == type).GroupBy(a => a.ModelElemListID).OrderBy(a => a.FirstOrDefault().ExternalSort).Select(a => new { ModelElemListID = a.Key, ModelElemRules = a.OrderBy(a => a.Sort).ToList() }).ToList();
            foreach (var item in elemRules)
            {
                var dto = new ElemRuleOutputDto();
                dto.ModelElemListID = item.ModelElemListID;
                dto.type = type;
                foreach (var rule in item.ModelElemRules)
                {
                    //var b = (bool)(ruleManager.ComputeRule(rule.ConditionStr));
                    var obj =await (ruleManager.ComputeRuleAsync(rule.ConditionStr));
                    var b = Convert.ToBoolean(obj);
                    if (b)
                    {
                        var res =await ruleManager.ComputeRuleAsync(rule.OperationString);
                        switch (type)
                        {
                            case RuleTypeEnum.RuleOfModelElem:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ModelElemID = (Guid)res;
                                }
                                break;
                            case RuleTypeEnum.RuleOfModelElemListQty:
                                if (res != null)
                                {
                                    dto.Qty = decimal.Parse(res.ToString());
                                }
                                break;
                            case RuleTypeEnum.RuleOfItmeID:
                            case RuleTypeEnum.RuleOfItme1ID:
                                if (!rule.OperationString.Trim().Equals("0") && res != null)
                                {
                                    dto.ItemID = (Guid)res;
                                }
                                break;
                            default:
                                break;
                        }
                        plist.Add(dto);
                        break;
                    }
                }
                //}
            }
           await ruleManager.ReleaseRuleAsync();
            return plist;

        }
        protected async Task<bool> ExistCodeAsync(MOM_ModelElemRuleDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.ConditionStr.Equals(input.ConditionStr) && a.OperationString.Equals(input.OperationString) && a.BomRuleType == input.BomRuleType && a.ModelElemListID == input.ModelElemListID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.ConditionStr.Equals(input.ConditionStr) && a.OperationString.Equals(input.OperationString) && a.BomRuleType == input.BomRuleType && a.ModelElemListID == input.ModelElemListID && a.Id != input.Id.Value);
            }

        }
    }
}


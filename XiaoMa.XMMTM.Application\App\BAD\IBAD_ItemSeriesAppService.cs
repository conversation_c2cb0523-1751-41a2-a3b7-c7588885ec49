﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemSeriesAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 19:45:43
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemSeriesAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemSeriesGetAllOutputDto>> Get(BAD_ItemSeriesGetAllInputDto input);
        Task Adds(List<BAD_ItemSeriesDto> input);
        Task Updates(List<BAD_ItemSeriesDto> input);
        Task Deletes(List<BAD_ItemSeriesDto> input);
    }
}

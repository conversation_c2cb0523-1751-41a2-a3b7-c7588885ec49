/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/30/星期四 15:04:38
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public  MOM_ModelGetAllInputDto()
        {
            this.BusinessSubTypes = new List<ModelBusinessSubType>();
        }
        public Guid? ClientID { set; get; }
        public Guid? ModelBaseID { set; get; }
        public Guid? GroupID { set; get; }

        public bool? GenderID { set; get; }

        public Guid? SizeListID { set; get; }

        public Guid? SewBaseID { set; get; }

        public Guid? ModelGroupID { set; get; }

        public bool? IsActive { set; get; }

        public Guid? Id { set; get; }

        public ModelBusinessSubType? BusinessSubType { set; get; }
        public List<ModelBusinessSubType> BusinessSubTypes { set; get; }
        public ODM_SorderType? SorderTypeID { get; set; }

        public string DesignNo { set; get; }

    }
}

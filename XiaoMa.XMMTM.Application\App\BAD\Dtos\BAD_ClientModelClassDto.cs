﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelClassDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2021/3/2/星期二 14:41:04
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientModelClassDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ClientModelClassDto : EntityDto<Guid?>
    {
        public Guid ClientID { set; get; }
        public Guid ModelClassID { set; get; }
        public bool IsChecked { set; get; }
        //public string Code { set; get; }
        //public string CodeName { set; get; }
        //public bool IsActive { set; get; }
        //public string CreateBy { set; get; }
        //public string ModifyBy { set; get; }
        //public Guid? CreateID { set; get; }
        //public Guid? ModifyID { set; get; }
        //public DateTime? CreateOn { set; get; }
        //public DateTime? ModifyOn { set; get; }
    }
}

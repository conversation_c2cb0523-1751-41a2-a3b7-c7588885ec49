/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/30/星期四 15:04:32
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }
        public string ShortModelCode { set; get; }
        public string Code { get; set; }
        public string Remark { set; get; }
        /// <summary>
        ///
        /// </summary>

        public DateTime? IssueDate { get; set; }
        public virtual bool IsActive { set; get; }

        /// <summary>
        /// 简单版型/合成版型
        /// </summary>

        public bool Single { get; set; }
        /// <summary>
        /// 设计款号
        /// </summary>
        public string DesignNo { set; get; }
        /// <summary>
        ///
        /// </summary>

        public Guid? OwnerID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string Owner { get; set; }

        /// <summary>
        ///基础版型
        /// </summary>

        public Guid ModelBaseID { get; set; }

        /// <summary>
        /// 类别
        /// </summary>

        public Guid GroupID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public bool GenderID { get; set; }

        /// <summary>
        /// 规格单ID
        /// </summary>

        public Guid SizeListID { get; set; }

        /// <summary>
        ///缝份类别
        /// </summary>

        public Guid? SewBaseID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string ShortName { get; set; }

        ///// <summary>
        ///// 客户端是否显示
        ///// </summary>

        //public bool IsClientShow { get; set; }

        /// <summary>
        ///支持算法
        /// </summary>

        public bool IsRuleSize { get; set; }

        /// <summary>
        /// 最小加放量
        /// </summary>

        public decimal? MinEase { get; set; }

        /// <summary>
        /// 最大加放量
        /// </summary>

        public decimal? MaxEase { get; set; }

        /// <summary>
        ///排料前缀
        /// </summary>

        public string Prefix { get; set; }

        /// <summary>
        ///业务类型
        /// </summary>

        public ModelBusinessSubType BusinessSubType { get; set; }

        /// <summary>
        /// 版型来源
        /// </summary>

        //public int? OriginModelID { get; set; }

        public Guid? ModelGroupID { set; get; }
        /// <summary>
        /// 面料ID
        /// </summary>
        public Guid? ItemID { set; get; }

        /// <summary>
        /// 原始版型
        /// </summary>
        public Guid? OriginalModelID { set; get; }

        /// <summary>
        /// 订单版型来源
        /// </summary>
        public Guid? SoderDetailModelSource { set; get; }

        /// <summary>
        /// 来源自研发订单
        /// </summary>
        public bool? IsResearchDevelopmentSource { set; get; }
        /// <summary>
        /// 研发版工艺图片ID
        /// </summary>
        public Guid? FileManagementID { set; get; }
        /// <summary>
        /// 大货版 是否检验通过
        /// </summary>
        public bool? PlushModelChecked { set; get; }
        /// <summary>
        /// 大货版 检验内容
        /// </summary>
        public string PlushModelCheckMessage { set; get; }
    }
}

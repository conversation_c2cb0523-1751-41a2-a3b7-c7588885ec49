/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/1/星期六 10:50:07
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeListAppService : XMMTMAppServiceBase, IMOM_SizeListAppService
    {
        private readonly IRepository<SizeList, Guid> repository;
        //private readonly IRepository<SizeListBase, Guid> sizeListBaserepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Size, Guid> sizeRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<SorderDetailModel, Guid> sorderDetailModelRepository;
        private readonly IRepository<SizeListBase, Guid> sizeListBaseRepository;
        private readonly IRepository<SizeElema, Guid> sizeElemaRepository;
        private readonly IRepository<SizeElemb, Guid> sizeElembRepository;
        private readonly IRepository<SizeElemc, Guid> sizeElemcRepository;
        private readonly IRepository<SizeElemd, Guid> sizeElemdRepository;
        private readonly IRepository<SizeListSizeElema, Guid> sizeListSizeElemaRepository;
        private readonly IRepository<SizeListSizeElemb, Guid> sizeListSizeElembRepository;
        private readonly IRepository<SizeListSizeElemc, Guid> sizeListSizeElemcRepository;
        private readonly IRepository<SizeListSizeElemd, Guid> sizeListSizeElemdRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        //private readonly IObjectMapper objectMapper;
        public MOM_SizeListAppService(
       IRepository<SizeList, Guid> repository,
       //IRepository<SizeListBase, Guid> sizeListBaserepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Size, Guid> sizeRepository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<Sorder, Guid> sorderRepository,
       IRepository<SorderDetail, Guid> sorderDetailRepository,
       IRepository<SorderDetailModel, Guid> sorderDetailModelRepository,
       IRepository<SizeListBase, Guid> sizeListBaseRepository,
       IRepository<SizeElema, Guid> sizeElemaRepository,
       IRepository<SizeElemb, Guid> sizeElembRepository,
       IRepository<SizeElemc, Guid> sizeElemcRepository,
       IRepository<SizeElemd, Guid> sizeElemdRepository,
       IRepository<SizeListSizeElema, Guid> sizeListSizeElemaRepository,
       IRepository<SizeListSizeElemb, Guid> sizeListSizeElembRepository,
       IRepository<SizeListSizeElemc, Guid> sizeListSizeElemcRepository,
       IRepository<SizeListSizeElemd, Guid> sizeListSizeElemdRepository,

       IRepository<Group, Guid> groupRepository

         //IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            //this.sizeListBaserepository = sizeListBaserepository;
            this.modelRepository = modelRepository;
            this.sizeRepository = sizeRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.sorderRepository = sorderRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.sorderDetailModelRepository = sorderDetailModelRepository;
            this.sizeListBaseRepository = sizeListBaseRepository;
            this.sizeElemaRepository = sizeElemaRepository;
            this.sizeElembRepository = sizeElembRepository;
            this.sizeElemcRepository = sizeElemcRepository;
            this.sizeElemdRepository = sizeElemdRepository;
            this.sizeListSizeElemaRepository = sizeListSizeElemaRepository;
            this.sizeListSizeElembRepository = sizeListSizeElembRepository;
            this.sizeListSizeElemcRepository = sizeListSizeElemcRepository;
            this.sizeListSizeElemdRepository = sizeListSizeElemdRepository;
            this.groupRepository = groupRepository;
            //this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeListGetAllOutputDto>> Get(MOM_SizeListGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in groupRepository.GetAll() on t.GroupID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                             //where t.IsActive
                         select new MOM_SizeListGetAllOutputDto()
                         {
                             //ClassID = t.ClassID,
                             //ClassText = ((SYS_Class)t.ClassID).GetDescription(),
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             IsActive = t.IsActive,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             GroupID = t.GroupID,
                             GroupText = t1.Code + ":" + t1.CodeName,
                             Id = t.Id,
                             IssueDate = t.IssueDate,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Owner = t.Owner,
                             Remark = t.Remark,
                             Relax = t.Relax,
                             ClassID = (int)t1.GroupTypeID,
                             SizeElemListID = t.SizeElemListID,
                             SizeElemListText = t.SizeElemListID.GetDescription(),
                             BigBellyMax = t.BigBellyMax,
                             BigBellyMin = t.BigBellyMin,
                             NormalBellyMax = t.NormalBellyMax,
                             NormalBellyMin = t.NormalBellyMin,
                         })
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                         .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).ThenBy(a => a.GroupID).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SizeListGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeListGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<SizeList>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeListDto> input)
        {
            foreach (var entity in input)
            {
                if (await modelRepository.GetAll().AnyAsync(a => a.SizeListID == entity.Id))
                {
                    throw new UserFriendlyException("已有相关版型引用,请删除版型后,再删除此规格单！");
                }
                var sizeIds = await sizeRepository.GetAll().Where(a => a.SizeListID == entity.Id).Select(a => a.Id).Distinct().ToListAsync();
                using (UnitOfWorkManager.Current.DisableFilter(AbpDataFilters.SoftDelete))
                {
                    var sorderNumbers = await (from t in sorderDetailModelRepository.GetAll()
                                               join t1x in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1x.Id into t1xx
                                               from t1 in t1xx.DefaultIfEmpty()
                                               join t2x in sorderRepository.GetAll() on t1.SorderID equals t2x.Id into t2xx
                                               from t2 in t2xx.DefaultIfEmpty()
                                               where sizeIds.Contains(t.SizeID.Value) || sizeIds.Contains(t.SizeID1.Value)
                                               select new { SorderNumber = t2.Code, t1.SorderID, t.SorderDetailID, SorderDetailModelID = t.Id, t.SizeID, t.SizeID1, t2.IsDeleted }).Distinct().ToListAsync();
                    if (sorderNumbers.Any())
                    {
                        var deletes = sorderNumbers.Where(a => a.IsDeleted).Select(a => a.SorderID).ToList();
                        if (deletes.Any())
                        {

                            var list = await sorderRepository.GetAll().Where(a => deletes.Contains(a.Id) && a.IsDeleted).ToListAsync();
                            await sorderRepository.GetDbContext().BulkDeleteAsync(list);

                        }
                        var notdeletes = sorderNumbers.Where(a => !a.IsDeleted).ToList();
                        if (notdeletes.Any())
                        {
                            var code = notdeletes.Select(a => a.SorderNumber).Distinct().ToList();
                            throw new UserFriendlyException($"订单[{string.Join(",", code)}]有引用，请先删除订单！");
                        }

                    }
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SizeListDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }


        #region Get规格单组成

        /// <summary>
        /// 规格单组成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public PagedResultDto<MOM_SizeElemDtoGetAllOutputDto> GetSizeElem(MOM_SizeeListBaseSizeElemGetAllInputDto input)
        {
            var sizeelem = new List<MOM_SizeElemDtoGetAllOutputDto>();
            var sizeelema = new MOM_SizeElemDtoGetAllOutputDto
            {
                text = "身高",
                SizeElemType = "A"
            };
            sizeelema.children.AddRange(this.GetSizeElemA(input));
            sizeelem.Add(sizeelema);
            var sizeelemb = new MOM_SizeElemDtoGetAllOutputDto
            {
                text = "胸围/腰围",
                SizeElemType = "B"
            };
            sizeelemb.children.AddRange(this.GetSizeElemB(input));
            sizeelem.Add(sizeelemb);
            var sizeelemc = new MOM_SizeElemDtoGetAllOutputDto
            {
                text = "体型",
                SizeElemType = "C"
            };
            sizeelemc.children.AddRange(this.GetSizeElemC(input));
            sizeelem.Add(sizeelemc);
            var sizeelemd = new MOM_SizeElemDtoGetAllOutputDto
            {
                text = "臀围",
                SizeElemType = "D"
            };
            sizeelemd.children.AddRange(this.GetSizeElemD(input));
            sizeelem.Add(sizeelemd);

            return new PagedResultDto<MOM_SizeElemDtoGetAllOutputDto>(sizeelem.Count, sizeelem);
        }
        /// <summary>
        /// 身高
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private List<MOM_SizeElemDtoGetAllOutputDto> GetSizeElemA(MOM_SizeeListBaseSizeElemGetAllInputDto input)
        {
            // ********************
            // Left：左边的内容
            // ********************
            var _left = from t in repository.GetAll()
                        join t2 in sizeElemaRepository.GetAll() on t.SizeElemListID equals t2.SizeElemListID
                        where t.Id == input.Id
                        select new
                        {
                            t2.Id,
                            SizeElemaID = t2.Id,
                            SizeElemaCode = t2.Code,
                            SizeElemaName = t2.CodeName,
                            t2.Sequence,
                        };
            // ********************
            // Right：右边的内容
            // ********************
            var _right = from t in sizeListSizeElemaRepository.GetAll()
                         where t.SizeListID == input.Id
                         select t;
            // ********************
            // Union：合成的内容
            // ********************
            var _union = (from t in _left
                          join t1x in _right on t.Id equals t1x.SizeElemaID into t1xx
                          from t1 in t1xx.DefaultIfEmpty()
                          orderby t.Sequence
                          select new MOM_SizeElemDtoGetAllOutputDto
                          {
                              text = t.SizeElemaCode + ": " + t.SizeElemaName,
                              Selected = t1 == null ? false : true,
                              SizeElemType = "A",
                              ID = t1 == null ? Guid.Empty : t1.Id,
                              Sequence = t.Sequence, // t1 == null ? null : t1.Sequence,
                              CreateID = t1.CreateID,
                              CreateBy = t1.CreateBy,
                              CreateOn = t1.CreateOn,
                              ModifyID = t1.ModifyID,
                              ModifyBy = t1.ModifyBy,
                              ModifyOn = t1.ModifyOn,
                              SizeListID = input.Id,
                              SizeElemID = t.SizeElemaID,
                              children = null,
                          }).ToList();
            return _union;
        }
        /// <summary>
        /// 胸围
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private List<MOM_SizeElemDtoGetAllOutputDto> GetSizeElemB(MOM_SizeeListBaseSizeElemGetAllInputDto input)
        {
            // ********************
            // Left：左边的内容
            // ********************
            var _left = from t in repository.GetAll()
                        join t2 in sizeElembRepository.GetAll() on t.SizeElemListID equals t2.SizeElemListID
                        where t.Id == input.Id
                        select new
                        {
                            t2.Id,
                            SizeElembID = t2.Id,
                            SizeElembCode = t2.Code,
                            SizeElembName = t2.CodeName,
                            t2.Sequence,
                        };
            // ********************
            // Right：右边的内容
            // ********************
            var _right = from t in sizeListSizeElembRepository.GetAll()
                         where t.SizeListID == input.Id
                         select t;
            // ********************
            // Union：合成的内容
            // ********************
            var _union = (from t in _left
                          join t1x in _right on t.Id equals t1x.SizeElembID into t1xx
                          from t1 in t1xx.DefaultIfEmpty()
                          orderby t.Sequence
                          select new MOM_SizeElemDtoGetAllOutputDto
                          {
                              text = t.SizeElembCode + ": " + t.SizeElembName,
#pragma warning disable IDE0075 // 简化条件表达式
                              Selected = t1 == null ? false : true,
#pragma warning restore IDE0075 // 简化条件表达式
                              SizeElemType = "B",
                              ID = t1 == null ? Guid.Empty : t1.Id,
                              Sequence = t.Sequence, // t1 == null ? null : t1.Sequence,
                              CreateID = t1.CreateID,
                              CreateBy = t1.CreateBy,
                              CreateOn = t1.CreateOn,
                              ModifyID = t1.ModifyID,
                              ModifyBy = t1.ModifyBy,
                              ModifyOn = t1.ModifyOn,
                              SizeListID = input.Id,
                              SizeElemID = t.SizeElembID,
                              children = null,
                          }).ToList();
            return _union;
        }
        /// <summary>
        /// 体型
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private List<MOM_SizeElemDtoGetAllOutputDto> GetSizeElemC(MOM_SizeeListBaseSizeElemGetAllInputDto input)
        {
            var _left = from t in repository.GetAll()
                        join t2 in sizeElemcRepository.GetAll() on t.SizeElemListID equals t2.SizeElemListID
                        where t.Id == input.Id
                        select new
                        {
                            t2.Id,
                            SizeElemcID = t2.Id,
                            SizeElemcCode = t2.Code,
                            SizeElemcName = t2.CodeName,
                            t2.Sequence,
                        };
            // ********************
            // Right：右边的内容
            // ********************
            var _right = from t in sizeListSizeElemcRepository.GetAll()
                         where t.SizeListID == input.Id
                         select t;
            // ********************
            // Union：合成的内容
            // ********************
            var _union = (from t in _left
                          join t1x in _right on t.Id equals t1x.SizeElemcID into t1xx
                          from t1 in t1xx.DefaultIfEmpty()
                          orderby t.Sequence
                          select new MOM_SizeElemDtoGetAllOutputDto
                          {
                              text = t.SizeElemcCode + ": " + t.SizeElemcName,
#pragma warning disable IDE0075 // 简化条件表达式
                              Selected = t1 == null ? false : true,
#pragma warning restore IDE0075 // 简化条件表达式
                              SizeElemType = "C",
                              ID = t1 == null ? Guid.Empty : t1.Id,
                              Sequence = t.Sequence, // t1 == null ? null : t1.Sequence,
                              CreateID = t1.CreateID,
                              CreateBy = t1.CreateBy,
                              CreateOn = t1.CreateOn,
                              ModifyID = t1.ModifyID,
                              ModifyBy = t1.ModifyBy,
                              ModifyOn = t1.ModifyOn,
                              SizeListID = input.Id,
                              SizeElemID = t.SizeElemcID,
                              children = null,
                          }).ToList();
            return _union;
        }
        /// <summary>
        /// 臀围
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private List<MOM_SizeElemDtoGetAllOutputDto> GetSizeElemD(MOM_SizeeListBaseSizeElemGetAllInputDto input)
        {

            var _left = from t in repository.GetAll()
                        join t2 in sizeElemdRepository.GetAll() on t.SizeElemListID equals t2.SizeElemListID
                        where t.Id == input.Id
                        select new
                        {
                            t2.Id,
                            SizeElemdID = t2.Id,
                            SizeElemdCode = t2.Code,
                            SizeElemdName = t2.CodeName,
                            t2.Sequence,
                        };
            // ********************
            // Right：右边的内容
            // ********************
            var _right = from t in sizeListSizeElemdRepository.GetAll()
                         where t.SizeListID == input.Id
                         select t;
            // ********************
            // Union：合成的内容
            // ********************
            var _union = (from t in _left
                          join t1x in _right on t.Id equals t1x.SizeElemdID into t1xx
                          from t1 in t1xx.DefaultIfEmpty()
                          orderby t.Sequence
                          select new MOM_SizeElemDtoGetAllOutputDto
                          {
                              text = t.SizeElemdCode + ": " + t.SizeElemdName,
                              Selected = t1 == null ? false : true,
                              SizeElemType = "D",
                              ID = t1 == null ? Guid.Empty : t1.Id,
                              Sequence = t.Sequence, // t1 == null ? null : t1.Sequence,
                              CreateID = t1.CreateID,
                              CreateBy = t1.CreateBy,
                              CreateOn = t1.CreateOn,
                              ModifyID = t1.ModifyID,
                              ModifyBy = t1.ModifyBy,
                              ModifyOn = t1.ModifyOn,
                              SizeListID = input.Id,
                              SizeElemID = t.SizeElemdID,
                              children = null,
                          }).ToList();
            return _union;
        }


        #endregion

        /// <summary>
        /// 规格单组成
        /// 生成规格单元素
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task CreateSizes(MOM_SizeListDto input)
        {
            if (!input.Id.HasValue || input.Id == Guid.Empty)
            {
                throw new UserFriendlyException("规格单ID为空");
            }
            var oldsizes = await sizeRepository.GetAll().Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (oldsizes.Any())
            {
                await sizeRepository.GetDbContext().BulkDeleteAsync(oldsizes);
            }
            await CheckSizeListBySorder(input);
            var sizeElemA = await (from t in sizeElemaRepository.GetAll()
                                   join t1 in sizeListSizeElemaRepository.GetAll() on t.Id equals t1.SizeElemaID
                                   where t1.SizeListID == input.Id.Value
                                   select new
                                   {
                                       t.Id,
                                       t.Code,
                                   }).ToListAsync();
            var sizeElemB = await (from t in sizeElembRepository.GetAll()
                                   join t1 in sizeListSizeElembRepository.GetAll() on t.Id equals t1.SizeElembID
                                   where t1.SizeListID == input.Id.Value
                                   select new
                                   {
                                       t.Id,
                                       t.Code,
                                   }).ToListAsync();
            var sizeElemC = await (from t in sizeElemcRepository.GetAll()
                                   join t1 in sizeListSizeElemcRepository.GetAll() on t.Id equals t1.SizeElemcID
                                   where t1.SizeListID == input.Id.Value
                                   select new
                                   {
                                       t.Id,
                                       t.Code,
                                   }).ToListAsync();
            //var sizeElemD = await (from t in sizeElemdRepository.GetAll()
            //                       join t1 in sizeListSizeElemdRepository.GetAll() on t.Id equals t1.SizeElemdID
            //                       where t1.SizeListID == input.Id.Value
            //                       select new
            //                       {
            //                           t.Id,
            //                           t.Code,
            //                       }).ToListAsync();

            //var sizeBaseList = sizeListBaserepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var sizes = (from a in sizeElemA
                         from b in sizeElemB
                         from c in sizeElemC
                             //from d in sizeElemD
                         select new Size
                         {
                             Id = Guid.NewGuid(),
                             Code = a.Code + "/" + b.Code + c.Code,
                             SizeName = a.Code + "/" + b.Code + c.Code,
                             CodeName = a.Code + "/" + b.Code + c.Code,
                             CadSizeCode = a.Code + "/" + b.Code + c.Code,
                             SizeListID = input.Id.Value,
                             SizeElemaID = a.Id,
                             SizeElembID = b.Id,
                             SizeElemcID = c.Id,
                             //SizeElemdID = d.Id,
                             IsActive = false,
                             CreateBy = SSOSession.Name,
                             CreateID = SSOSession.UserId,
                             CreateOn = DateTime.Now,
                             //Remark="不可用"
                         }).ToList();
            //foreach (var item in sizes)
            //{
            //    var dd= sizeBaseList
            //}
            if (sizes.Any())
            {
                await sizeRepository.GetDbContext().BulkInsertAsync(sizes);
            }

        }


        #region 基础规格单--更新规格单


        /// <summary>
        /// 基础规格单
        /// 更新规格单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task CreateUpdateSizes(MOM_SizeListDto input)
        {
            await CheckSizeListBySorder(input);

            var sizeListDto = await repository.GetAsync(input.Id.Value);
            var sizes = await GetSize(input);
            var sizeListBase = await GetSizeListBase(input);
            var SizeListSizeElema = await GetSizeListSizeElema(input);
            var SizeListSizeElemb = await GetSizeListSizeElemb(input);
            var SizeListSizeElemc = await GetSizeListSizeElemc(input);
            var SizeListSizeElemd = await GetSizeListSizeElemd(input);
            var sizeColumnIDs = sizeListBase.Select(a => a.SizeColumnID).Distinct().ToList();
            var sizeCloums = await sizeColumnRepository.GetAll().Where(a => sizeColumnIDs.Contains(a.Id)).ToListAsync();

            foreach (var item in sizes)
            {
                SetSize(item, input.GroupID, sizeCloums, sizeListBase, SizeListSizeElema, SizeListSizeElemb, SizeListSizeElemc, SizeListSizeElemd);
                if (input.GroupID == SystemConfigGuid_SYS_Group.Trouser || input.GroupID == SystemConfigGuid_SYS_Group.WestSkirt)
                {
                    if (item.Waist > 0)
                    {
                        item.IsActive = true;
                    }
                }
                else
                {
                    item.SizeElemdID = null;
                    item.IsActive = true;
                }
            }
            await sizeRepository.GetDbContext().BulkUpdateAsync(sizes);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task SetSizeListSizeElem(MOM_SizeListDto input)
        {
            var sizeListDto = await repository.GetAsync(input.Id.Value);
            var sizes = new List<Size>();
            if (input.SizeIDs != null && input.SizeIDs.Count > 0)
            {
                sizes = await sizeRepository.GetAll().Where(a => input.SizeIDs.Contains(a.Id)).ToListAsync();
            }
            else
            {
                sizes = await GetSize(input);
            }

            var sizeListBase = await GetSizeListBase(input);
            var SizeListSizeElema = await GetSizeListSizeElema(input);
            var SizeListSizeElemb = await GetSizeListSizeElemb(input);
            var SizeListSizeElemc = await GetSizeListSizeElemc(input);
            var SizeListSizeElemd = await GetSizeListSizeElemd(input);
            var sizeColumnIDs = sizeListBase.Select(a => a.SizeColumnID).Distinct().ToList();
            var sizeCloums = await sizeColumnRepository.GetAll().Where(a => sizeColumnIDs.Contains(a.Id)).ToListAsync();
            foreach (var item in sizes)
            {
                SetSizeElems(item, input.GroupID, sizeCloums, sizeListBase, SizeListSizeElema, SizeListSizeElemb, SizeListSizeElemc, SizeListSizeElemd);

            }
            await sizeRepository.GetDbContext().BulkUpdateAsync(sizes);

        }
        private void SetSize(Size size, Guid GroupID, List<SizeColumn> sizeColumns, List<SizeListBase> sizeListBases, List<SizeListSizeElema> sizeListSizeElemas, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemc> sizeListSizeElemcs, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var parentType = typeof(Size);
            var properties = parentType.GetProperties();

            //循环规格属性
            foreach (var propertie in properties)
            {
                //根据规格属性名称,获取规格部位
                //propertie.Name;
                if (string.IsNullOrEmpty(propertie.Name))
                {
                    continue;
                }
                var sizeColumn = sizeColumns.FirstOrDefault(a => a.Code == propertie.Name);
                if (sizeColumn == null)
                {
                    continue;
                }
                var sizeListBaseDtos = GetSizeListBaseExtent(size, GroupID, sizeColumn, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds);
                var sizeListBaseDto = sizeListBaseDtos.FirstOrDefault();
                //做一个字段属性判断,如果属性是decimal 就给0
                if (sizeListBaseDto == null)
                {
                    if (propertie.GetType().ToString().ToLower() == "decimal")
                    {
                        propertie.SetValue(size, 0, null);
                    }
                    else
                    {
                        continue;

                    }
                }
                else
                {
                    size.SizeElemdID = sizeListBaseDto.SizeElemdID;
                }
                propertie.SetValue(size, sizeListBaseDto.Value, null);

            }
        }
        private void SetSizeElems(Size size, Guid GroupID, List<SizeColumn> sizeColumns, List<SizeListBase> sizeListBases, List<SizeListSizeElema> sizeListSizeElemas, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemc> sizeListSizeElemcs, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var sizeColumnA = sizeColumns.FirstOrDefault(a => a.Code == "Height");
            if (sizeColumnA != null)
            {
                var sizeListBaseDtosA = GetSizeListBaseExtent1(size, GroupID, sizeColumnA, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds).Where(a => a.Value == size.Height).ToList();
                var sizeListBaseDtoA = sizeListBaseDtosA.FirstOrDefault();
                size.SizeElemaID = sizeListBaseDtoA?.SizeElemaID;
            }
            var sizeColumnB = new SizeColumn();
            var sizeListBaseDtosB = new List<SizeListBase>();
            if (GroupID == SystemConfigGuid_SYS_Group.Trouser || GroupID == SystemConfigGuid_SYS_Group.WestSkirt)
            {
                sizeColumnB = sizeColumns.FirstOrDefault(a => a.Code == "Waist");//腰围
                sizeListBaseDtosB = GetSizeListBaseExtent1(size, GroupID, sizeColumnB, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds).Where(a => a.MinValue <= size.Waist && size.Waist <= a.MaxValue).ToList();
                var sizeColumnD = sizeColumns.FirstOrDefault(a => a.Code == "Hipline");//臀围
                var sizeListBaseDtosD = new List<SizeListBase>();
                if (sizeColumnD != null)
                {
                    sizeListBaseDtosD = GetSizeListBaseExtent1(size, GroupID, sizeColumnD, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds).Where(a => size.Hipline >= a.MinValue && size.Hipline <= a.MaxValue).ToList();
                }
                if (sizeListBaseDtosD.Any())
                {
                    var ds = sizeListBaseDtosD.Select(a => a.SizeElemdID).Distinct().ToList();
                    sizeListBaseDtosB = sizeListBaseDtosB.Where(a => ds.Contains(a.SizeElemdID)).ToList();
                }
            }
            else
            {
                sizeColumnB = sizeColumns.FirstOrDefault(a => a.Code == "Bust");//胸围
                sizeListBaseDtosB = GetSizeListBaseExtent1(size, GroupID, sizeColumnB, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds).ToList();
                sizeListBaseDtosB = sizeListBaseDtosB.Where(a => a.MinValue < size.Bust && size.Bust <= a.MaxValue).ToList();
                var sizeColumnD = sizeColumns.FirstOrDefault(a => a.Code == "MiddleWaist");//中腰
                var sizeListBaseDtosD = new List<SizeListBase>();
                if (sizeColumnD != null)
                {
                    sizeListBaseDtosD = GetSizeListBaseExtent1(size, GroupID, sizeColumnD, sizeListBases, sizeListSizeElemas, sizeListSizeElembs, sizeListSizeElemcs, sizeListSizeElemds).Where(a => size.MiddleWaist > a.MinValue && size.MiddleWaist <= a.MaxValue).ToList();
                }
                if (sizeListBaseDtosD.Any())
                {
                    var ds = sizeListBaseDtosB.Select(a => a.SizeElembID).Distinct().ToList();
                    sizeListBaseDtosB = sizeListBaseDtosD.Where(a => ds.Contains(a.SizeElembID)).ToList();
                }
            }


            var b = sizeListBaseDtosB.FirstOrDefault();
            size.SizeElembID = b?.SizeElembID;
            if (GroupID == SystemConfigGuid_SYS_Group.Trouser || GroupID == SystemConfigGuid_SYS_Group.WestSkirt)
            {
                if (size.Waist != decimal.Zero && b != null && b.Value == size.Waist)
                {
                    size.SizeElemcID = b?.SizeElemcID;
                }
                else
                {
                    size.SizeElemcID = null;
                }
            }
            else
            {
                size.SizeElemcID = b?.SizeElemcID;
            }



            size.SizeElemdID = b?.SizeElemdID;
        }
        private List<SizeListBase> GetSizeListBaseExtent(Size size, Guid GroupID, SizeColumn sizeColumn, List<SizeListBase> sizeListBases, List<SizeListSizeElema> sizeListSizeElemas, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemc> sizeListSizeElemcs, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var list = new List<SizeListBase>();
            var sizeListBaseDto = sizeListBases.FirstOrDefault(a => a.SizeColumnID == sizeColumn.Id);
            if (sizeListBaseDto == null)
            {
                return list;
            }

            if (!sizeListBaseDto.AlgorithmType.HasValue)
            {
                return list;
            }
            switch (sizeListBaseDto.AlgorithmType.Value)
            {
                case AlgorithmTypeEnums.AlgorithmTypeA:
                    list = ExtendBySizeElemA(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemas).ToList();
                    list = list.Where(a => a.SizeElemaID == size.SizeElemaID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeB:
                    list = ExtendBySizeElemB(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs).ToList();
                    list = list.Where(a => a.SizeElembID == size.SizeElembID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeD:
                    list = ExtendBySizeElemD(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemds).ToList();
                    list = list.Where(a => a.SizeElemdID == size.SizeElemdID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeA_B_C:
                    list = ExtendBySizeElemA_B_C(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemas, sizeListSizeElembs).ToList();
                    list = list.Where(a => a.SizeElemaID == size.SizeElemaID && a.SizeElembID == size.SizeElembID && a.SizeElemcID == size.SizeElemcID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeB_C:
                    if (GroupID == SystemConfigGuid_SYS_Group.Trouser || GroupID == SystemConfigGuid_SYS_Group.WestSkirt)
                    {
                        list = ExtendBySizeElemB_C_1(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs, sizeListSizeElemds);
                    }
                    else
                    {
                        list = ExtendBySizeElemB_C(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs);
                    }

                    list = list.Where(a => a.SizeElembID == size.SizeElembID && a.SizeElemcID == size.SizeElemcID).ToList();
                    break;

            }
            return list;
        }
        private List<SizeListBase> GetSizeListBaseExtent1(Size size, Guid GroupID, SizeColumn sizeColumn, List<SizeListBase> sizeListBases, List<SizeListSizeElema> sizeListSizeElemas, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemc> sizeListSizeElemcs, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var list = new List<SizeListBase>();
            var sizeListBaseDto = sizeListBases.FirstOrDefault(a => a.SizeColumnID == sizeColumn.Id);
            if (sizeListBaseDto == null)
            {
                return list;
            }

            if (!sizeListBaseDto.AlgorithmType.HasValue)
            {
                return list;
            }
            switch (sizeListBaseDto.AlgorithmType.Value)
            {
                case AlgorithmTypeEnums.AlgorithmTypeA:
                    list = ExtendBySizeElemA(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemas).ToList();
                    //list = list.Where(a => a.SizeElemaID == size.SizeElemaID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeB:
                    list = ExtendBySizeElemB(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs).ToList();
                    //list = list.Where(a => a.SizeElembID == size.SizeElembID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeD:
                    list = ExtendBySizeElemD(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemds).ToList();
                    //list = list.Where(a => a.SizeElemdID == size.SizeElemdID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeA_B_C:
                    list = ExtendBySizeElemA_B_C(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElemas, sizeListSizeElembs).ToList();
                    //list = list.Where(a => a.SizeElemaID == size.SizeElemaID && a.SizeElembID == size.SizeElembID && a.SizeElemcID == size.SizeElemcID).ToList();
                    break;
                case AlgorithmTypeEnums.AlgorithmTypeB_C:
                    if (GroupID == SystemConfigGuid_SYS_Group.Trouser || GroupID == SystemConfigGuid_SYS_Group.WestSkirt)
                    {
                        list = ExtendBySizeElemB_C_1(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs, sizeListSizeElemds);
                    }
                    else
                    {
                        list = ExtendBySizeElemB_C(sizeListBaseDto.SizeColumnID, sizeListBases, sizeListSizeElembs);
                    }

                    //list = list.Where(a => a.SizeElembID == size.SizeElembID && a.SizeElemcID == size.SizeElemcID).ToList();
                    break;

            }
            return list;
        }
        private async Task<List<Size>> GetSize(MOM_SizeListDto input)
        {
            var list = await sizeRepository.GetAll().AsNoTracking().Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any())
            {
                throw new UserFriendlyException("规格单明细数据不存在");
            }
            return list;
        }

        private async Task<List<SizeListBase>> GetSizeListBase(MOM_SizeListDto input)
        {
            var list = await sizeListBaseRepository.GetAll().AsNoTracking().Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any())
            {
                throw new UserFriendlyException("基础规格单数据不存在");
            }
            return list;
        }
        private async Task<List<SizeListSizeElema>> GetSizeListSizeElema(MOM_SizeListDto input)
        {
            var list = await sizeListSizeElemaRepository.GetAll().AsNoTracking().Include(t => t.SYS_SizeElema).Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any())
            {
                throw new UserFriendlyException("身高元素数据不存在");
            }
            return list;
        }
        private async Task<List<SizeListSizeElemb>> GetSizeListSizeElemb(MOM_SizeListDto input)
        {
            var list = await sizeListSizeElembRepository.GetAll().AsNoTracking().Include(t => t.SYS_SizeElemb).Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any())
            {
                throw new UserFriendlyException("臀围元素数据不存在");
            }
            return list;
        }
        private async Task<List<SizeListSizeElemc>> GetSizeListSizeElemc(MOM_SizeListDto input)
        {
            var list = await sizeListSizeElemcRepository.GetAll().AsNoTracking().Include(t => t.SYS_SizeElemc).Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any())
            {
                throw new UserFriendlyException("体型元素数据不存在");
            }
            return list;
        }
        private async Task<List<SizeListSizeElemd>> GetSizeListSizeElemd(MOM_SizeListDto input)
        {
            var list = await sizeListSizeElemdRepository.GetAll().AsNoTracking().Include(t => t.SYS_SizeElemd).Where(a => a.SizeListID == input.Id.Value).ToListAsync();
            if (!list.Any() && (input.GroupID == SystemConfigGuid_SYS_Group.Trouser || input.GroupID == SystemConfigGuid_SYS_Group.WestSkirt))//裤子检查臀围
            {
                throw new UserFriendlyException("基础规格单数据不存在");
            }
            return list;
        }


        /// <summary>
        /// A 身高
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElemas"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemA(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElema> sizeListSizeElemas)
        {
            var SLBExtend = new List<SizeListBase>();
            var SLB = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .FirstOrDefault();
            if (SLB == null) { return new List<SizeListBase>(); }
            var sizeListSizeElema = sizeListSizeElemas.Where(t => t.SizeElemaID == SLB.SizeElemaID).FirstOrDefault();
            if (sizeListSizeElema == null)
            {
                return SLBExtend;
            }
            var SequenceA = sizeListSizeElema.SYS_SizeElema.Sequence;
            SLBExtend = (from t in sizeListSizeElemas
                         select new SizeListBase
                         {
                             SizeColumnID = sizecolumnid,
                             SizeElemaID = t.SizeElemaID,
                             Value = SLB.Value + (decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA),
                             MinValue = SLB.MinValue + (decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA),
                             MaxValue = SLB.MaxValue + (decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA),
                         }).ToList();
            return SLBExtend;
        }
        /// <summary>
        ///  A身高/B 胸围/ C 体型
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElemas"></param>
        /// <param name="sizeListSizeElembs"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemA_B_C(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElema> sizeListSizeElemas, List<SizeListSizeElemb> sizeListSizeElembs)
        {
            var SLBExtend = new List<SizeListBase>();
            //得到臀围的基础规格单
            var SLBS = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .ToList();
            foreach (var SLB in SLBS)
            {
                var sizeListSizeElema = sizeListSizeElemas.Where(t => t.SizeElemaID == SLB.SizeElemaID).FirstOrDefault();
                var sizeListSizeElemb = sizeListSizeElembs.Where(t => t.SizeElembID == SLB.SizeElembID).FirstOrDefault();
                if (sizeListSizeElema == null || sizeListSizeElemb == null)
                {
                    continue;
                }
                var SequenceA = sizeListSizeElema.SYS_SizeElema.Sequence;
                var SequenceB = sizeListSizeElemb.SYS_SizeElemb.Sequence;
                var sql = (from t in sizeListSizeElemas
                           from t1 in sizeListSizeElembs
                           select new SizeListBase
                           {
                               SizeColumnID = sizecolumnid,
                               SizeElemaID = t.SizeElemaID,
                               SizeElembID = t1.SizeElembID,
                               SizeElemcID = SLB.SizeElemcID,
                               Value = SLB.Value + ((decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA)) + ((decimal)SLB.Stepb * (decimal)(t1.SYS_SizeElemb.Sequence - SequenceB)),
                               MinValue = SLB.MinValue + ((decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA)) + ((decimal)SLB.Stepb * (decimal)(t1.SYS_SizeElemb.Sequence - SequenceB)),
                               MaxValue = SLB.MaxValue + ((decimal)SLB.Stepa * (decimal)(t.SYS_SizeElema.Sequence - SequenceA)) + ((decimal)SLB.Stepb * (decimal)(t1.SYS_SizeElemb.Sequence - SequenceB)),
                           }).ToList<SizeListBase>();
                foreach (var s in sql)
                {
                    SLBExtend.Add(s);
                }
            }
            return SLBExtend;
        }
        /// <summary>
        /// B 胸围
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElembs"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemB(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElemb> sizeListSizeElembs)
        {
            var SLBExtend = new List<SizeListBase>();
            //得到臀围的基础规格单
            var SLB = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .FirstOrDefault();
            if (SLB == null) { return new List<SizeListBase>(); }
            var sizeListSizeElemb = sizeListSizeElembs.Where(t => t.SizeElembID == SLB.SizeElembID).FirstOrDefault();
            if (sizeListSizeElemb == null)
            {
                return SLBExtend;
            }
            var SequenceB = sizeListSizeElemb.SYS_SizeElemb.Sequence;

            SLBExtend = (from t in sizeListSizeElembs
                         select new SizeListBase
                         {
                             SizeColumnID = sizecolumnid,
                             SizeElembID = t.SizeElembID,
                             Value = SLB.Value + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                             MinValue = SLB.MinValue + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                             MaxValue = SLB.MaxValue + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                         }).ToList<SizeListBase>();
            return SLBExtend;
        }
        /// <summary>
        /// B 胸围/ C 体型
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElembs"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemB_C(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElemb> sizeListSizeElembs)
        {
            var SLBExtend = new List<SizeListBase>();
            var SLBS = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .ToList();
            foreach (var SLB in SLBS)
            {
                var sizeListSizeElemb = sizeListSizeElembs.Where(t => t.SizeElembID == SLB.SizeElembID).FirstOrDefault();
                if (sizeListSizeElemb == null)
                {
                    continue;
                }
                var SequenceB = sizeListSizeElemb.Sequence;
                var sql = (from t in sizeListSizeElembs
                           select new SizeListBase
                           {
                               SizeColumnID = sizecolumnid,
                               SizeElembID = t.SizeElembID,
                               SizeElemcID = SLB.SizeElemcID,
                               Value = SLB.Value + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                               MinValue = SLB.MinValue + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                               MaxValue = SLB.MaxValue + (decimal)SLB.Stepb * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                           }).ToList<SizeListBase>();
                foreach (var s in sql)
                {
                    SLBExtend.Add(s);
                }
            }

            return SLBExtend;
        }
        /// <summary>
        /// 裤子
        /// B 胸围/ C 体型
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElembs"></param>
        /// <param name="sizeListSizeElemds"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemB_C_1(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var SLBExtend = new List<SizeListBase>();
            var SLBS = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .ToList();
            foreach (var SLB in SLBS)
            {
                // 计算出SizeElemB和SizeElemD的Sequence差
                var SequenceB = sizeListSizeElembs.Where(t => t.SizeElembID == SLB.SizeElembID).FirstOrDefault()?.SYS_SizeElemb.Sequence;

                var SequenceD = sizeListSizeElemds.Where(t => t.SizeElemdID == SLB.SizeElemdID).FirstOrDefault()?.SYS_SizeElemd.Sequence;
                if (SequenceB == null || SequenceD == null)
                {
                    continue;
                }
                //通过Sequence差去关联SizeElemB和SizeElemD
                //*
                var sql = (from t in sizeListSizeElembs
                           join t1 in sizeListSizeElemds on (t.SYS_SizeElemb.Sequence - SequenceB) equals (t1.SYS_SizeElemd.Sequence - SequenceD)
                           select new SizeListBase
                           {
                               SizeColumnID = sizecolumnid,
                               SizeElembID = t.SizeElembID,
                               SizeElemcID = SLB.SizeElemcID,
                               SizeElemdID = t1.SizeElemdID,
                               Value = SLB.Value + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                               MinValue = SLB.MinValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                               MaxValue = SLB.MaxValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
                           }).ToList<SizeListBase>();
                foreach (var s in sql)
                {
                    SLBExtend.Add(s);
                }
            }

            return SLBExtend;
        }
        /// <summary>
        /// D 臀围
        /// </summary>
        /// <param name="sizecolumnid"></param>
        /// <param name="sizelistbases"></param>
        /// <param name="sizeListSizeElemds"></param>
        /// <returns></returns>
        private List<SizeListBase> ExtendBySizeElemD(Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElemd> sizeListSizeElemds)
        {
            var SLBExtend = new List<SizeListBase>();
            //得到臀围的基础规格单
            var SLB = sizelistbases
                .Where(t => t.SizeColumnID == sizecolumnid)
                .FirstOrDefault();
            if (SLB == null) { return new List<SizeListBase>(); }
            var sizeListSizeElemd = sizeListSizeElemds.Where(t => t.SizeElemdID == SLB.SizeElemdID).FirstOrDefault();
            if (sizeListSizeElemd == null)
            {
                return SLBExtend;
            }
            var SequenceD = sizeListSizeElemd.SYS_SizeElemd.Sequence;
            SLBExtend = (from t in sizeListSizeElemds
                         select new SizeListBase
                         {
                             SizeColumnID = sizecolumnid,
                             SizeElemdID = t.SizeElemdID,
                             Value = SLB.Value + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemd.Sequence - SequenceD),
                             MinValue = SLB.MinValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemd.Sequence - SequenceD),
                             MaxValue = SLB.MaxValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemd.Sequence - SequenceD),
                         }).ToList();
            return SLBExtend;
        }
        ///// <summary>
        ///// D 臀围
        ///// </summary>
        ///// <param name="sizecolumnid"></param>
        ///// <param name="sizelistbases"></param>
        ///// <param name="sizeListSizeElemds"></param>
        ///// <returns></returns>
        //private List<SizeListBase> ExtendBySizeElemD(Size size, Guid sizecolumnid, List<SizeListBase> sizelistbases, List<SizeListSizeElemb> sizeListSizeElembs, List<SizeListSizeElemd> sizeListSizeElemds)
        //{
        //    var SLBExtend = new List<SizeListBase>();
        //    //得到臀围的基础规格单
        //    var SLBS = sizelistbases
        //        .Where(t => t.SizeColumnID == sizecolumnid)
        //        .ToList();
        //    foreach (var SLB in SLBS)
        //    {

        //        size.SizeElemdID = SLB.SizeElemdID;

        //        // 计算出SizeElemB和SizeElemD的Sequence差
        //        var SequenceB = sizeListSizeElembs.Where(t => t.SizeElembID == SLB.SizeElembID).FirstOrDefault().SYS_SizeElemb.Sequence;
        //        var SequenceD = sizeListSizeElemds.Where(t => t.SizeElemdID == SLB.SizeElemdID).FirstOrDefault().SYS_SizeElemd.Sequence;
        //        //通过Sequence差去关联SizeElemB和SizeElemD
        //        //*
        //        var sql = (from t in sizeListSizeElembs
        //                   join t1 in sizeListSizeElemds on (t.SYS_SizeElemb.Sequence - SequenceB) equals (t1.SYS_SizeElemd.Sequence - SequenceD)
        //                   select new SizeListBase
        //                   {
        //                       SizeColumnID = sizecolumnid,
        //                       SizeElembID = t.SizeElembID,
        //                       SizeElemcID = SLB.SizeElemcID,
        //                       SizeElemdID = t1.SizeElemdID,
        //                       Value = SLB.Value + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
        //                       MinValue = SLB.MinValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
        //                       MaxValue = SLB.MaxValue + (decimal)SLB.Stepd * (decimal)(t.SYS_SizeElemb.Sequence - SequenceB),
        //                   }).ToList<SizeListBase>();
        //        foreach (var s in sql)
        //        {
        //            SLBExtend.Add(s);
        //        }
        //    }
        //    return SLBExtend;
        //}
        #endregion


        #region 修改规格单组成

        /// <summary>
        /// 检查规格单和订单引用关系,如果又引用则不可重新生成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task CheckSizeListBySorder(MOM_SizeListDto input)
        {
            var sorders1 = await (from t in sorderRepository.GetAll()
                                  join t1 in sorderDetailRepository.GetAll() on t.Id equals t1.SorderID
                                  join t2 in sorderDetailModelRepository.GetAll() on t1.Id equals t2.SorderDetailID
                                  join t3 in sizeRepository.GetAll() on t2.SizeID equals t3.Id
                                  where t3.SizeListID == input.Id.Value
                                  select new
                                  {
                                      t.Code,
                                      t.Id,
                                  }).ToListAsync();

            var sorders2 = await (from t in sorderRepository.GetAll()
                                  join t1 in sorderDetailRepository.GetAll() on t.Id equals t1.SorderID
                                  join t2 in sorderDetailModelRepository.GetAll() on t1.Id equals t2.SorderDetailID
                                  join t3 in sizeRepository.GetAll() on t2.SizeID1 equals t3.Id
                                  where t3.SizeListID == input.Id.Value
                                  select new
                                  {
                                      t.Code,
                                      t.Id,
                                  }).ToListAsync();
            var list = sorders1.Union(sorders2).DistinctBy(a => a.Id).ToList();
            if (list.Count > 0)
            {
                var str = "该规格单已被订单:";
                foreach (var item in list)
                {
                    str += item.Code + ",";
                }
                str += "使用了,无法更改";
                throw new UserFriendlyException(str);
            }

        }
        /// <summary>
        /// 修改规格单组成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task UpdatesSizeElem(List<MOM_SizeElemDtoGetAllOutputDto> input)
        {
            await this.UpdateSizeElemA(input.Where(a => a.SizeElemType == "A").FirstOrDefault().children.ToList());
            await this.UpdateSizeElemB(input.Where(a => a.SizeElemType == "B").FirstOrDefault().children.ToList());
            await this.UpdateSizeElemC(input.Where(a => a.SizeElemType == "C").FirstOrDefault().children.ToList());
            await this.UpdateSizeElemD(input.Where(a => a.SizeElemType == "D").FirstOrDefault().children.ToList());
        }

        private async Task UpdateSizeElemA(List<MOM_SizeElemDtoGetAllOutputDto> input)
        {
            var add = input.Where(a => a.Selected).Where(a => !a.ID.HasValue || a.ID.Value == Guid.Empty).Select(a => new SizeListSizeElema()
            {
                Sequence = a.Sequence,
                SizeElemaID = a.SizeElemID,
                SizeListID = a.SizeListID
            }).ToList().Distinct();
            var edit = input.Where(a => a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElema()
            {
                Sequence = a.Sequence,
                SizeElemaID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            var delete = input.Where(a => !a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElema()
            {
                Sequence = a.Sequence,
                SizeElemaID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            foreach (var item in add)
            {
                await sizeListSizeElemaRepository.InsertAsync(item);
            }
            foreach (var item in edit)
            {
                await sizeListSizeElemaRepository.UpdateAsync(item);
            }
            foreach (var item in delete)
            {
                await sizeListSizeElemaRepository.DeleteAsync(item);
            }

        }
        private async Task UpdateSizeElemB(List<MOM_SizeElemDtoGetAllOutputDto> input)
        {
            var add = input.Where(a => a.Selected).Where(a => !a.ID.HasValue || a.ID.Value == Guid.Empty).Select(a => new SizeListSizeElemb()
            {
                Sequence = a.Sequence,
                SizeElembID = a.SizeElemID,
                SizeListID = a.SizeListID
            }).ToList().Distinct();
            var edit = input.Where(a => a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemb()
            {
                Sequence = a.Sequence,
                SizeElembID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            var delete = input.Where(a => !a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemb()
            {
                Sequence = a.Sequence,
                SizeElembID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            foreach (var item in add)
            {
                await sizeListSizeElembRepository.InsertAsync(item);
            }
            foreach (var item in edit)
            {
                await sizeListSizeElembRepository.UpdateAsync(item);
            }
            foreach (var item in delete)
            {
                await sizeListSizeElembRepository.DeleteAsync(item);
            }
        }
        private async Task UpdateSizeElemC(List<MOM_SizeElemDtoGetAllOutputDto> input)
        {

            var add = input.Where(a => a.Selected).Where(a => !a.ID.HasValue || a.ID.Value == Guid.Empty).Select(a => new SizeListSizeElemc()
            {
                Sequence = a.Sequence,
                SizeElemcID = a.SizeElemID,
                SizeListID = a.SizeListID
            }).ToList().Distinct();
            var edit = input.Where(a => a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemc()
            {
                Sequence = a.Sequence,
                SizeElemcID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            var delete = input.Where(a => !a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemc()
            {
                Sequence = a.Sequence,
                SizeElemcID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            foreach (var item in add)
            {
                await sizeListSizeElemcRepository.InsertAsync(item);
            }
            foreach (var item in edit)
            {
                await sizeListSizeElemcRepository.UpdateAsync(item);
            }
            foreach (var item in delete)
            {
                await sizeListSizeElemcRepository.DeleteAsync(item);
            }
        }
        private async Task UpdateSizeElemD(List<MOM_SizeElemDtoGetAllOutputDto> input)
        {


            var add = input.Where(a => a.Selected).Where(a => !a.ID.HasValue || a.ID.Value == Guid.Empty).Select(a => new SizeListSizeElemd()
            {
                Sequence = a.Sequence,
                SizeElemdID = a.SizeElemID,
                SizeListID = a.SizeListID
            }).ToList().Distinct();
            var edit = input.Where(a => a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemd()
            {
                Sequence = a.Sequence,
                SizeElemdID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            var delete = input.Where(a => !a.Selected).Where(a => a.ID.HasValue && a.ID.Value != Guid.Empty).Select(a => new SizeListSizeElemd()
            {
                Sequence = a.Sequence,
                SizeElemdID = a.SizeElemID,
                SizeListID = a.SizeListID,
                Id = a.ID.Value,
            }).ToList().Distinct();
            foreach (var item in add)
            {
                await sizeListSizeElemdRepository.InsertAsync(item);
            }
            foreach (var item in edit)
            {
                await sizeListSizeElemdRepository.UpdateAsync(item);
            }
            foreach (var item in delete)
            {
                await sizeListSizeElemdRepository.DeleteAsync(item);
            }
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task DeepClone(DeepCloneDto input)
        {
            var entity = await repository.GetAsync(input.Id);
            var a = await sizeListSizeElemaRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var b = await sizeListSizeElembRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var c = await sizeListSizeElemcRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var d = await sizeListSizeElemdRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var size = await sizeRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();
            var sizeListBase = await sizeListBaseRepository.GetAll().Where(a => a.SizeListID == input.Id).ToListAsync();

            entity.Code += "---Copy";
            entity.CodeName += "---Copy";
            entity.Id = Guid.Empty;
            entity.MOM_SizeListSizeElema = null;
            entity.MOM_SizeListSizeElemb = null;
            entity.MOM_SizeListSizeElemc = null;
            entity.MOM_SizeListSizeElemd = null;
            entity.MOM_Size = null;
            entity.MOM_SizeListBase = null;
            entity.CreateOn = DateTime.Now;
            entity.CreateBy = SSOSession.Name;
            entity.CreateID = SSOSession.UserId;
            entity.ModifyBy = null;
            entity.ModifyID = null;
            entity.ModifyOn = null;
            var newID = await repository.InsertAndGetIdAsync(entity);
            if (a.Any())
            {
                foreach (var item in a)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = newID;
                    await sizeListSizeElemaRepository.InsertAsync(item);
                }
            }
            if (b.Any())
            {
                foreach (var item in b)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = Guid.Empty;
                    await sizeListSizeElembRepository.InsertAsync(item);
                }
            }
            if (c.Any())
            {
                foreach (var item in c)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = newID;
                    await sizeListSizeElemcRepository.InsertAsync(item);
                }
            }
            if (d.Any())
            {
                foreach (var item in d)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = newID;
                    await sizeListSizeElemdRepository.InsertAsync(item);
                }
            }
            if (size.Any())
            {
                foreach (var item in size)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = newID;
                    await sizeRepository.InsertAsync(item);
                }
            }
            if (sizeListBase.Any())
            {
                foreach (var item in sizeListBase)
                {
                    item.Id = Guid.Empty;
                    item.SizeListID = newID;
                    await sizeListBaseRepository.InsertAsync(item);
                }
            }


        }






        #endregion
    }
}

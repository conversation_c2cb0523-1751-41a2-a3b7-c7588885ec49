﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_WashingLabelModelElemListGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2022年10月10日,星期一 14:02:00
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_WashingLabelModelElemListGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_WashingLabelModelElemListGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? Id { set; get; }
        public Guid? ModelElemListID { set; get; }
        public Guid? GroupID { set; get; }
        /// <summary>
        /// 位置信息
        /// </summary>
        public WashingLabelPositionEnums? WashingLabelPosition { set; get; }
        /// <summary>
        /// 类型
        /// </summary>
        public WashingLabelPositionTypeEnums? WashingLabelPositionType { set; get; }
        /// <summary>
        /// 显示类型
        /// </summary>
        public SuitsupplyModelElemListShowTypeEnums? ModelElemListShowType { set; get; }
    }
}

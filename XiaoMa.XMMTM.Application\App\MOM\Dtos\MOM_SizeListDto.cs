/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeListDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/1/星期六 10:50:26
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using System.Collections.Generic;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeListDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_SizeListDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        public virtual bool IsActive { set; get; }
        
        public DateTime? IssueDate { get; set; }

        /// <summary>
        /// 放松量
        /// </summary>

        public decimal Relax { get; set; }


        /// <summary>
        /// 所有者
        /// </summary>

        public string Owner { get; set; }

        /// <summary>
        /// 版本
        /// </summary>

        public decimal Version { get; set; }

        /// <summary>
        /// 规格元素ID
        /// </summary>

        public SYS_SizeElemList SizeElemListID { get; set; }

        /// <summary>
        /// 归类ID
        /// </summary>

        public int? ClassID { get; set; }
        /// <summary>
        /// 类别ID
        /// </summary>

        public Guid GroupID { get; set; }

        public List<Guid> SizeIDs { set; get; }
        public string SizeElem { set; get; }

        /// <summary>
        /// 凸肚体最大值
        /// </summary>
        public decimal? BigBellyMax { set; get; }

        /// <summary>
        /// 凸肚体最小值
        /// </summary>
        public decimal? BigBellyMin { set; get; }
        /// <summary>
        /// 正常体最大
        /// </summary>
        public decimal? NormalBellyMax { set; get; }
        /// <summary>
        /// 正常体最小
        /// </summary>
        public decimal? NormalBellyMin { set; get; }
    }
}

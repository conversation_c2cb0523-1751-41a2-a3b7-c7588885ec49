﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemElemItemAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/12/8/星期二 13:13:11
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemElemItemAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemElemItemGetAllOutputDto>> GetML(BAD_ItemElemItemGetAllInputDto input);
        Task<PagedResultDto<BAD_ItemElemItemGetAllOutputDto>> GetFL(BAD_ItemElemItemGetAllInputDto input);
        Task Updates(List<BAD_ItemElemItemDto> input);
        Task Deletes(List<BAD_ItemElemItemDto> input);
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_BodyImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/8/星期六 10:20:29
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_BodyImageAppService : XMMTMAppServiceBase, IMOM_BodyImageAppService
    {
        private readonly IRepository<BodyImage, Guid> repository;
        private readonly IRepository<Body, Guid> bodyRepository;
        private readonly IRepository<BodyList, Guid> bodyListRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly XiaoMa.XMMTM.FileServer.FileServer fileServer;
        private readonly IObjectMapper objectMapper;
        public MOM_BodyImageAppService(
       IRepository<BodyImage, Guid> repository,
       IRepository<Body, Guid> bodyRepository,
       IRepository<BodyList, Guid> bodyListRepository,
       IRepository<ModelImage, Guid> modelImageRepository,
        XiaoMa.XMMTM.FileServer.FileServer fileServer,

       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.bodyRepository = bodyRepository;
            this.bodyListRepository = bodyListRepository;
            this.modelImageRepository = modelImageRepository;
            this.fileServer = fileServer;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_BodyImageGetAllOutputDto>> Get(MOM_BodyImageGetAllInputDto input)
        {
            var query = from t in repository.GetAll()
                      join t2x in bodyRepository.GetAll() on t.BodyID1 equals t2x.Id into t2xx
                      from t2 in t2xx.DefaultIfEmpty()
                      join t3 in bodyRepository.GetAll() on t.BodyID equals t3.Id
                      join t4 in bodyListRepository.GetAll() on t3.BodyListID equals t4.Id
                      join m in modelImageRepository.GetAll() on t.ModelImageID equals m.Id
                      select new MOM_BodyImageGetAllOutputDto()
                      {
                          Id = t.Id,
                          ImageSeq = t.ImageSeq,
                          CreateID = t.CreateID,
                          CreateBy = t.CreateBy,
                          CreateOn = t.CreateOn,
                          ModifyID = t.ModifyID,
                          ModifyBy = t.ModifyBy,
                          ModifyOn = t.ModifyOn,
                          BodyID = t.BodyID,
                          BodyID1 = t.BodyID1,
                          ModelImageID = t.ModelImageID, //
                          BodyCode1 = t2.Code,
                          BodyName1 = t2.CodeName,
                          BodyCode = t3.Code,
                          BodyName = t3.CodeName,
                          BodyListID = t3.BodyListID,
                          BodyListCode = t4.Code,
                          BodyListName = t4.CodeName,
                          ModelImage = m.Code + ": " + m.CodeName,//
                          PositionText = m.PositionID.GetDescription(), // 
                          ImageUrl = fileServer.GetImageUrl(m.ImagePath),
                          ImagePath = m.ImagePath
                      };
            //var query = repository.GetAll()
            //  .Where(a => a.IsActive);
            //.WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_BodyImageGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_BodyImageGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_BodyImageDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<BodyImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_BodyImageDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_BodyImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

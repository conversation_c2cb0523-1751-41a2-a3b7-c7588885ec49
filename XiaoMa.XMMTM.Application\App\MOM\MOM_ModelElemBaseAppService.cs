﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemBaseAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/28/星期二 16:18:44
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemBaseAppService : XMMTMAppServiceBase, IMOM_ModelElemBaseAppService
    {
        private readonly IRepository<ModelElemBase, Guid> repository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemBaseAppService(
       IRepository<ModelElemBase, Guid> repository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemBaseGetAllOutputDto>> Get(MOM_ModelElemBaseGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in groupRepository.GetAll() on t.GroupID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         select new MOM_ModelElemBaseGetAllOutputDto()
                         {
                             Id = t.Id,
                             GroupID = t.GroupID,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             GenderID = t.GenderID,
                             IsActive = t.IsActive,
                             Remark = t.Remark,
                             Sequence = t.Sequence,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             GroupText = t1.CodeName,
                         })

              //.Where(a => a.IsActive)
              .WhereIf(input.GenderID.HasValue, a => a.GenderID == input.GenderID.Value)
              .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sequence).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelElemBaseGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemBaseGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelElemBase>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemBaseDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelElemBaseDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code)&&a.GroupID==input.GroupID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value && a.GroupID == input.GroupID);
            }

        }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ItemGetOutputDto
// 功能描述：
// 作者    zhangby
// 时间    2020/7/23/星期四 11:35:45
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    public class BAD_ItemGetAllOutputDto: Item
    {

        /// <summary>
        /// 面辅料类型
        /// </summary>
        public string ItemClassText { set; get; }
        /// <summary>
        /// 业务归属
        /// </summary>
        public string BusinessGroupText { set; get; }
        /// <summary>
        /// 工艺属性
        /// </summary>
        public string TechnologyText { set; get; }
        /// <summary>
        /// 单位分类
        /// </summary>
        public string UnitGroupText { set; get; }
        /// <summary>
        /// 纹理分类
        /// </summary>
        public string TextureGroupText { set; get; }
        /// <summary>
        /// 物料分类名称
        /// </summary>
        public string ItemGroupText { set; get; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplierItemCode { set; get; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierItemName { set; get; }
        /// <summary>
        /// 季节编码
        /// </summary>
        public string SeasonGroupCode { set; get; }
        /// <summary>
        /// 季节名称
        /// </summary>
        public string SeasonGroupName { set; get; }

        public string ClientCode { set; get; }
        public string ClientName { set; get; }
        public string ItemFoldText { set; get; }

        public bool? HasImages { set; get; } = false;
        public bool? NeedImages { set; get; } = false;

    }
}

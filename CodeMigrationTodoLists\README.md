# 代码迁移TodoList系统

## 📖 概述

这是一个用于管理 `XiaoMa.XMMTM` 项目到 `XiaoMa.Shop.XMMTM` 项目代码迁移的TodoList系统。

## 🎯 目标

将源项目 (`e:\workgit\xm\XiaoMa.MTM\`) 中的代码有选择性地迁移到目标项目 (`e:\workgit\Shop\XiaoMa.MTM\`) 中。

## 📁 文件夹结构

```
CodeMigrationTodoLists/
├── README.md                    # 本文件 - 使用说明
├── 01_ModuleSelection.md        # 第一层：模块选择TodoList
├── 02_FileComparisons/          # 第二层：文件对比TodoList
│   ├── Core_Files.md
│   ├── Application_Files.md
│   ├── EntityFrameworkCore_Files.md
│   ├── WebCore_Files.md
│   ├── WebHost_Files.md
│   └── Migrator_Files.md
├── 03_DetailedComparisons/      # 第三层：详细代码对比
│   └── [按需生成具体文件对比]
├── CompareFiles.ps1             # PowerShell对比脚本
├── SimpleStats.ps1              # 简单统计脚本
└── FileComparisonResult.json    # 对比结果数据
```

## 🔄 工作流程

### 阶段1：模块选择
1. 打开 `01_ModuleSelection.md`
2. 勾选需要对比的模块
3. 告知AI您的选择

### 阶段2：文件对比
1. AI根据您选择的模块生成对应的文件对比清单
2. 文件保存在 `02_FileComparisons/` 文件夹中
3. 您可以查看每个文件的差异状态并决定是否需要详细对比

### 阶段3：详细对比
1. 对于您选择的文件，AI生成详细的代码差异对比
2. 结果保存在 `03_DetailedComparisons/` 文件夹中
3. 您可以基于详细对比决定是否覆盖文件

## ⚙️ 对比规则

- **文件类型**: 只对比 `.cs` 文件
- **命名空间处理**: 自动忽略 `XiaoMa.XMMTM` ↔ `XiaoMa.Shop.XMMTM` 的命名空间差异
- **决策要求**: 所有有差异的文件都需要手动确认是否覆盖
- **排除内容**: 自动排除 `obj` 和 `bin` 文件夹

## 📊 状态说明

### 模块状态
- [ ] 未选择
- [x] 已选择

### 文件状态
- 📄 **无差异** - 内容完全相同（忽略命名空间差异）
- 📄 **有差异** - 存在业务逻辑或结构差异
- 📄 **仅命名空间差异** - 只有命名空间不同
- 📄 **目标缺失** - 目标项目中不存在该文件
- 📄 **源缺失** - 源项目中不存在该文件

### 操作建议
- ✅ **直接覆盖** - 推荐直接用源文件覆盖目标文件
- 🔄 **手动合并** - 需要手动合并两个文件的差异
- 🚫 **保持目标** - 保持目标项目的版本不变
- 🔍 **需要分析** - 需要进一步分析差异

## 🚀 快速开始

1. **开始**: 打开 `01_ModuleSelection.md` 选择要对比的模块
2. **对比**: 告知AI您的选择，AI将生成文件对比清单
3. **决策**: 根据对比结果决定具体的迁移策略
4. **执行**: 根据TodoList逐步执行代码迁移

## ⚠️ 注意事项

- 建议从高优先级模块（Core、Application）开始
- 每次只处理一个模块，避免混乱
- 在执行任何覆盖操作前，请确保已备份目标项目
- 如有疑问，请先进行详细对比再做决定

## 📞 支持

如果在使用过程中遇到问题，请：
1. 检查文件路径是否正确
2. 确认PowerShell脚本执行权限
3. 联系AI助手获取帮助

---

**创建时间**: 2024-12-19  
**版本**: v1.0  
**状态**: 已初始化，等待模块选择

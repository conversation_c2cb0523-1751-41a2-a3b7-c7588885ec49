﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_WashingLabelModelElemListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2022年10月10日,星期一 13:59:55
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Base;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_WashingLabelModelElemListAppService : XMMTMAppServiceBase, IBAD_WashingLabelModelElemListAppService
    {
        private readonly IRepository<WashingLabelModelElemList, Guid> repository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_WashingLabelModelElemListAppService(
       IRepository<WashingLabelModelElemList, Guid> repository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemListRepository = modelElemListRepository;
            this.modelElemRepository = modelElemRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_WashingLabelModelElemListGetAllOutputDto>> Get(BAD_WashingLabelModelElemListGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemListRepository.GetAll() on t.ModelElemListID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelElemRepository.GetAll() on t.ModelElemID1 equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelElemRepository.GetAll() on t.ModelElemID2 equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in groupRepository.GetAll() on t1.GroupID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                             // where t.IsActive
                         select new BAD_WashingLabelModelElemListGetAllOutputDto()
                         {
                             ModelElemID1 = t.ModelElemID1,
                             Id = t.Id,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Info = t.Info,
                             IsActive = t.IsActive,
                             ModelElemID2 = t.ModelElemID2,
                             ModelElemListID = t.ModelElemListID,
                             ModelElemListShowType = t.ModelElemListShowType,
                             ModelElemListShowTypeText = t.ModelElemListShowType.GetDescription(),
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             OtherPosition = t.OtherPosition,
                             Remark = t.Remark,
                             Sort = t.Sort,
                             WashingLabelPosition = t.WashingLabelPosition,
                             WashingLabelPositionText = t.WashingLabelPosition.GetDescription(),
                             WashingLabelPositionType = t.WashingLabelPositionType,
                             WashingLabelPositionTypeText = t.WashingLabelPositionType.GetDescription(),
                             ModelElem1Text = t2.Code + ":" + t2.CodeName,
                             ModelElem2Text = t3.Code + ":" + t3.CodeName,
                             ModelElemListText = t1.Code + ":" + t1.CodeName,
                             GroupName = t4.CodeName,
                              GroupID=t1.GroupID
                         })
                         .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID.HasValue&&a.GroupID.Value == input.GroupID.Value)
                         .WhereIf(input.WashingLabelPosition.HasValue, a => a.WashingLabelPosition == input.WashingLabelPosition.Value)
                         .WhereIf(input.WashingLabelPositionType.HasValue, a => a.WashingLabelPositionType == input.WashingLabelPositionType.Value)
                         .WhereIf(input.ModelElemListShowType.HasValue, a => a.ModelElemListShowType == input.ModelElemListShowType.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElem1Text.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.ModelElem2Text.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<BAD_WashingLabelModelElemListGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_WashingLabelModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<WashingLabelModelElemList>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_WashingLabelModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_WashingLabelModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_WashingLabelModelElemListDto input)
        {

            var any = await repository.GetAll()
                .Where(a=>a.WashingLabelPosition==input.WashingLabelPosition)
                .Where(a=>a.WashingLabelPositionType==input.WashingLabelPositionType)
                .Where(a=>a.ModelElemListShowType==input.ModelElemListShowType)
                .WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.ModelElemID1.HasValue, a => a.ModelElemID1 == input.ModelElemID1.Value)
                .WhereIf(input.ModelElemID2.HasValue, a => a.ModelElemID2 == input.ModelElemID2.Value)
                .AnyAsync(a => a.ModelElemListID == input.ModelElemListID);
            return any;

        }
    }
}

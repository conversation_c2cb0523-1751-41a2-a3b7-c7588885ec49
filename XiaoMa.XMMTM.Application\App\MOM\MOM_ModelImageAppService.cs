﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:05
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Configuration;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Dto;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelImageAppService : XMMTMAppServiceBase, IMOM_ModelImageAppService
    {
        private readonly IRepository<ModelImage, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IMTMConfiguration configuration;
        private readonly XiaoMa.XMMTM.FileServer.FileServer fileServer;
        private readonly IUnitOfWorkManager unitOfWorkManager;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelImageAppService(
       IRepository<ModelImage, Guid> repository,
       IRepository<Model, Guid> modelRepository,
       IMTMConfiguration configuration,
       XiaoMa.XMMTM.FileServer.FileServer fileServer,
       IUnitOfWorkManager unitOfWorkManager,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.configuration = configuration;
            this.fileServer = fileServer;
            this.unitOfWorkManager = unitOfWorkManager;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelImageGetAllOutputDto>> Get(MOM_ModelImageGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelRepository.GetAll() on t.ModelID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                             //where t.IsActive
                         select new MOM_ModelImageGetAllOutputDto()
                         {
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Id = t.Id,
                             ImagePath = t.ImagePath,
                             IsActive = t.IsActive,
                             PositionID = t.PositionID,
                             PositionText = t.PositionID.GetDescription(),
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             ModelImageTypeID = t.ModelImageTypeID,
                             ModelImageTypeText = t.ModelImageTypeID.GetDescription(),
                             Remark = t.Remark,
                             ImageUrl = fileServer.GetImageUrl(t.ImagePath),
                             xid = t.Id,
                             ModelID = (t.ModelID.HasValue && t.ModelID.Value != Guid.Empty) ? t.ModelID : null,
                             ModelText = t1 != null ? t1.Code + ":" + t1.CodeName : null
                         })
                         .WhereIf(input.PositionID.HasValue, a => a.PositionID == input.PositionID.Value)
                         .WhereIf(input.ModelImageTypeID.HasValue, a => a.ModelImageTypeID == input.ModelImageTypeID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text))
              .WhereIf(!string.IsNullOrEmpty(input.Remark), a => a.Remark.Contains(input.Remark));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelImageGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelImageDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                if (entity.Id.HasValue)
                {
                    var oldentity = repository.Get(entity.Id.Value);
                    ObjectMapper.Map(entity, oldentity);
                    await repository.UpdateAsync(oldentity);
                }
                else
                {
                    var oldentity = ObjectMapper.Map<ModelImage>(entity);
                    await repository.InsertAsync(oldentity);
                }
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                fileServer.RemoveImage(oldentity.ImagePath);
                await repository.DeleteAsync(oldentity);
            }
        }
        [HttpPost]
        [AllowAnonymous]
        public async Task AddOrUpdate(IFormFile file, int ModelImageTypeID, int PositionID, string Code, string CodeName, string ModelID, string CreateBy, Guid CreateID, Guid? xid)
        {
            using (var unit = unitOfWorkManager.Begin())
            {
                var fs = file.OpenReadStream();
                byte[] srcBuf = new Byte[fs.Length];
                fs.Read(srcBuf, 0, srcBuf.Length);
                fs.Seek(0, SeekOrigin.Begin);

                var entity = new ModelImage();
                if (xid.HasValue && xid.Value != Guid.Empty)
                {
                    entity = repository.Get(xid.Value);
                    if (entity != null && file != null)
                    {
                        fileServer.RemoveImage(entity.ImagePath);
                    }
                }

                entity.ModelID = Guid.TryParse(ModelID, out Guid modelid) ? modelid : Guid.Empty;
                entity.CodeName = CodeName;
                entity.Code = Code;
                entity.CreateBy = CreateBy;
                entity.CreateID = CreateID;
                entity.PositionID = (SYS_Position)PositionID;
                entity.ModelImageTypeID = (SYS_ModelImageType)ModelImageTypeID;

                if (Guid.Empty == entity.Id)
                {
                    var b = repository.GetAll().AsNoTracking().Any(a => a.Code.Equals(entity.Code) && a.PositionID == entity.PositionID);
                    if (b)
                    {
                        throw new UserFriendlyException("已经存在相同的编码");
                    }
                }
                else
                {
                    var b = repository.GetAll().AsNoTracking().Any(a => a.Code.Equals(entity.Code) && a.Id != entity.Id && a.PositionID == entity.PositionID);
                    if (b && file == null)
                    {
                        throw new UserFriendlyException("已经存在相同的编码");
                    }
                }
                var id = await repository.InsertOrUpdateAndGetIdAsync(entity);
                if (file != null)
                {

                    //entity.ImagePath = await fileServer.SaveImage(file, (SYS_ModelImageType)ModelImageTypeID, (SYS_Position)PositionID, id);
                    entity.ImagePath = await fileServer.SaveImage(srcBuf, (SYS_ModelImageType)ModelImageTypeID, (SYS_Position)PositionID, id, file.FileName);

                }


                await fs.DisposeAsync();
                //await repository.UpdateAsync(entity);
                await unit.CompleteAsync();
            }

        }


        protected async Task<bool> ExistCodeAsync(MOM_ModelImageDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.PositionID == input.PositionID && a.ModelImageTypeID == input.ModelImageTypeID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value && a.PositionID == input.PositionID && a.ModelImageTypeID == input.ModelImageTypeID);
            }

        }


        protected async Task<bool> ExistCodeAsync(ModelImage input)
        {

            if (Guid.Empty == input.Id)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id);
            }

        }
        [HttpPost]
        [AbpAuthorize]
        public List<FileImageDirectoryDto> GetDirectory()
        {
            var list = fileServer.GetDirectory("", new List<FileImageDirectoryDto>());
            return list;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<List<ImageFileoutputDto>> getImageFile(ImageFileoutputDto input)
        {
            var list = fileServer.getImageFile(input.Path);

            var paths = list.Select(a => a.Path).Distinct().FirstOrDefault();
            var names = list.Select(a => a.Name).Distinct().ToList();
            var querylist = await (from t in repository.GetAll()
                                   where t.ImagePath.Contains(paths)
                                   select t.ImagePath).ToListAsync();
            List<ImageFileoutputDto> arrTmp = new List<ImageFileoutputDto>(list);
            foreach (var item in arrTmp)
            {
                var dto = querylist.FirstOrDefault(a => a.Contains(item.Path) && a.Contains(item.Name));
                if (dto != null)
                {
                    list.Remove(item);
                }
            }
            return list;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task delImageFile(List<ImageFileoutputDto> input)
        {
            await Task.Run(() =>
            {
                var paths = input.Select(a => a.Path + "/" + a.Name).ToList();
                fileServer.RemoveImage(paths);
            });


        }
    }
}

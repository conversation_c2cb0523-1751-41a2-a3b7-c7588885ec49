﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/24/星期五 17:01:57
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientModelDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ClientModelDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid ClientID { get; set; }

        /// <summary>
        /// 
        /// </summary>

        public Guid ModelID { get; set; }

        public bool Selected { set; get; }

    }
}

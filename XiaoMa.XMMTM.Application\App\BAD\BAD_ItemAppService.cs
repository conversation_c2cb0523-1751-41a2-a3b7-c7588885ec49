/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ItemBaseAppService
// 功能描述：    
// 作者    zhangby
// 时间    2020/7/23/星期四 9:59:00
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Combo;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.FileServer;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    public class BAD_ItemAppService : XMMTMAppServiceBase, IBAD_ItemAppService
    {
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<SubGroup, Guid> subGroupRepository;
        private readonly IRepository<Unit, Guid> unitRepository;
        private readonly IRepository<Season, Guid> seasonRepository;
        private readonly IRepository<BrandLine, Guid> brandLintRepository;
        private readonly IRepository<ItemConfig, Guid> itemConifgRepository;
        private readonly IRepository<ItemImage, Guid> itemImageRepository;
        private readonly IRepository<ItemStock, Guid> itemStockRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IFileServer fileServer;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IComboAppService comboAppService;

        public BAD_ItemAppService(IRepository<Item, Guid> itemRepository,
            IRepository<SubGroup, Guid> subGroupRepository,
               IRepository<Unit, Guid> unitRepository,
                IRepository<Season, Guid> seasonRepository,
                IRepository<BrandLine, Guid> brandLintRepository,
                IRepository<ItemConfig, Guid> itemConifgRepository,
                IRepository<ItemImage, Guid> itemImageRepository,
                IRepository<ItemStock, Guid> itemStockRepository,
                IRepository<Sorder, Guid> sorderRepository,
                IRepository<SorderDetail, Guid> sorderDetailRepository,
               IFileServer fileServer,
             IRepository<Client, Guid> clientRepository,
             IComboAppService comboAppService
            )
        {
            this.itemRepository = itemRepository;
            this.subGroupRepository = subGroupRepository;
            this.unitRepository = unitRepository;
            this.seasonRepository = seasonRepository;
            this.brandLintRepository = brandLintRepository;
            this.itemConifgRepository = itemConifgRepository;
            this.itemImageRepository = itemImageRepository;
            this.itemStockRepository = itemStockRepository;
            this.sorderRepository = sorderRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.fileServer = fileServer;
            this.clientRepository = clientRepository;
            this.comboAppService = comboAppService;
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ItemDto> input)
        {
            for (int i = 0; i < input.Count; i++)
            {
                var itemid = Guid.Empty;
                var entity = input[i];
                entity.Code = null;
                using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
                {

                    entity.Id = null;
                    entity.Number = null;
                    var oldentity = ObjectMapper.Map<Item>(entity);
                    oldentity = await createItemCode(oldentity, 0);
                    await Check(oldentity);
                    await CheckCode(oldentity);
                    await this.ExistCodeAsync(oldentity);
                    itemid = await itemRepository.InsertAndGetIdAsync(oldentity);
                    await unit.CompleteAsync();
                }
                //如果物料仓库不存在就添加
                //await insertItemStock(itemid, entity.InventoryQty);
            }
        }
        private async Task CheckCode(Item item)
        {
            var any = await itemRepository.GetAll().AnyAsync(a => a.Code == item.Code && item.Id != a.Id);
            if (any)
            {
                item = await createItemCode(item, 0);
                await CheckCode(item);
            }
            else
            {
                return;
            }
        }
        [HttpPost]
        [AllowAnonymous]
        public async Task Check(Item item)
        {
            var itemConfigGroup =await itemConifgRepository.GetAll().Where(a=>a.IsActive).FirstOrDefaultAsync(a => a.Id == item.ItemGroupID);
            if (itemConfigGroup == null)
            {
                return;
            }
            if (itemConfigGroup.WidthIsRequired.HasValue && itemConfigGroup.WidthIsRequired.Value)
            {
                if (!item.Width.HasValue || item.Width.Value <= 0)
                {
                    throw new UserFriendlyException($"{itemConfigGroup.CodeName}:物料分类,幅宽不能小于等于0");
                }
            }
        }
        /// <summary>
        /// 增加物料仓库信息
        /// </summary>
        /// <param name="itemID"></param>
        /// <param name="qty"></param>
        /// <returns></returns>
        private async Task insertItemStock(Guid itemID, decimal? qty)
        {
            if (itemID == Guid.Empty)
            {
                return;
            }
            var any = await itemStockRepository.GetAll().AnyAsync(a => a.ItemID == itemID);
            if (any)
            {
                return;
            }
            await itemStockRepository.InsertAsync(new ItemStock() { ItemID = itemID, InventoryQty = qty });

        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = itemRepository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await Check(oldentity);
                await CheckCode(oldentity);
                await this.ExistCodeAsync(oldentity);
                await itemRepository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = itemRepository.Get(entity.Id.Value);
                if (oldentity.Id == SystemConfigGuid_BAD_Item.KGFL || oldentity.Id == SystemConfigGuid_BAD_Item.KGML)
                {
                    throw new UserFriendlyException("系统必要数据,不可删除。");
                }
                var itemImages = await itemImageRepository.GetAll().Where(a => a.ItemID == entity.Id).ToListAsync();
                foreach (var item in itemImages)
                {
                    fileServer.RemoveImage(item.Url);
                    await itemImageRepository.DeleteAsync(item);
                }

                var sordercodes = await (from t in sorderDetailRepository.GetAll()
                                         join t1x in sorderRepository.GetAll() on t.SorderID equals t1x.Id into t1xx
                                         from t1 in t1xx.DefaultIfEmpty()
                                         where t.ItemID == entity.Id
                                         select t1.Code).Distinct().ToListAsync();
                if (sordercodes.Any())
                {
                    var str = string.Join(',', sordercodes);
                    throw new UserFriendlyException($"订单{str}中有使用此面料,无法删除.");
                }
                ObjectMapper.Map(entity, oldentity);
                await itemRepository.DeleteAsync(oldentity);
            }
        }

        protected async Task ExistCodeAsync(Item input)
        {
            if (input.ClientID.HasValue && input.ClientID != Guid.Empty && input.IsClientItem.HasValue && input.IsClientItem.Value)
            {
                throw new UserFriendlyException("绑定客户和客户指定不能同时存在,绑定客户为1对1,指定客户为1对多");
            }
            var any = await itemRepository.GetAll().WhereIf(input.Id != Guid.Empty, a => a.Id != input.Id).AnyAsync(a => a.OriginalItemNo.Equals(input.OriginalItemNo) && input.Code == a.Code);
            if (any)
            {
                throw new UserFriendlyException("已经存在相同的编码");
            }
        }



        public async Task<Item> createItemCode(Item item, int? number)
        {
            var num = 0;
            var numDto = await itemRepository.GetAll().Where(a => a.OriginalItemNo == item.OriginalItemNo).Where(a => a.ItemClassID == item.ItemClassID).OrderByDescending(a => a.Number.Value).FirstOrDefaultAsync();
            if (numDto != null)
            {
                num = numDto.Number.Value;
            }
            var configlist = await itemConifgRepository.GetAll().Where(a => a.ItemConfigBase == BAD_ItemConfigBase.ItemFLGroup).ToListAsync();
            item.Number = num + 1;
            var str = "ML";
            if (item.ItemClassID == BAD_ItemClass.FL)
            {
                var code = configlist.FirstOrDefault(a => a.Id == item.ItemGroupID);
                if (code != null)
                {
                    str = code.Code;
                }

            }
            item.Code = str + "-" + item.OriginalItemNo + "-" + item.Number.ToString();
            number += 1;
            var any = await itemRepository.GetAll().AnyAsync(a => a.Code == item.Code && item.Id != a.Id);
            if (any)
            {
                item = await createItemCode(item, number);
            }
            return item;

        }
        /// <summary>
        /// 批量创建编码
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public async Task CreateNumber()
        {
            var list = await itemRepository.GetAll().OrderBy(a => a.ItemClassID).Where(a => !a.Number.HasValue).Take(500).ToListAsync();
            var configlist = await itemConifgRepository.GetAll().Where(a => a.ItemConfigBase == BAD_ItemConfigBase.ItemFLGroup).ToListAsync();
            if (!list.Any())
            {
                return;
            }
            var num = 1;
            //var numDto = await itemRepository.GetAll().Where(a => a.Number.HasValue).OrderByDescending(a => a.Number.Value).FirstOrDefaultAsync();
            //if (numDto != null)
            //{
            //    num = numDto.Number.Value;
            //}
            foreach (var item in list)
            {
                item.Number = num;
                var str = "ML";
                if (item.ItemClassID == BAD_ItemClass.FL)
                {
                    var code = configlist.FirstOrDefault(a => a.Id == item.ItemGroupID);
                    if (code != null)
                    {
                        str = code.Code;
                    }

                }
                item.Code = str + "-" + item.OriginalItemNo + "-" + item.Number.ToString();


            }
            await itemRepository.GetDbContext().BulkUpdateAsync(list);
            await itemRepository.GetDbContext().SaveChangesAsync();
            await CreateNumber();
        }
        /// <summary>
        /// 物料
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public virtual async Task<PagedResultDto<BAD_ItemGetAllOutputDto>> Get(BAD_ItemGetAllInputDto input)
        {
            var sql = (from t in itemRepository.GetAll()
                       join t1x in itemConifgRepository.GetAll() on t.BusinessGroup equals t1x.Id into t1xx
                       from t1 in t1xx.DefaultIfEmpty()
                       join t2x in itemConifgRepository.GetAll() on t.ItemGroupID equals t2x.Id into t2xx
                       from t2 in t2xx.DefaultIfEmpty()
                       join t3x in clientRepository.GetAll() on t.ClientID equals t3x.Id into t3xx
                       from t3 in t3xx.DefaultIfEmpty()
                       join t4x in itemConifgRepository.GetAll() on t.TechnologyGroupID equals t4x.Id into t4xx
                       from t4 in t4xx.DefaultIfEmpty()
                       join t5x in itemConifgRepository.GetAll() on t.TextureGroupID equals t5x.Id into t5xx
                       from t5 in t5xx.DefaultIfEmpty()
                       join t6x in itemConifgRepository.GetAll() on t.UnitGroupID equals t6x.Id into t6xx
                       from t6 in t6xx.DefaultIfEmpty()
                       join t7x in clientRepository.GetAll() on t.SupplierItemID equals t7x.Id into t7xx
                       from t7 in t7xx.DefaultIfEmpty()
                       join t8x in itemConifgRepository.GetAll() on t.SeasonGroupID equals t8x.Id into t8xx
                       from t8 in t8xx.DefaultIfEmpty()
                           //orderby t.Code descending
                           //where (int)t.ClassID == Classid
                       select new BAD_ItemGetAllOutputDto
                       {
                           Id = t.Id,
                           IsActive = t.IsActive,
                           Code = t.Code,
                           CodeName = t.CodeName,
                           Yarn = t.Yarn,
                           Weight = t.Weight,
                           Width = t.Width,
                           Shrink = t.Shrink,
                           WholesalePrice = t.WholesalePrice,
                           RetailPrice = t.RetailPrice,
                           CreateID = t.CreateID,
                           CreateBy = t.CreateBy,
                           CreateOn = t.CreateOn,
                           ModifyID = t.ModifyID,
                           ModifyBy = t.ModifyBy,
                           ModifyOn = t.ModifyOn,
                           BusinessGroup = t.BusinessGroup,
                           ColorNo = t.ColorNo,
                           InventoryQty = t.InventoryQty,
                           ItemBrand = t.ItemBrand,
                           ItemComp = t.ItemComp,
                           //ItemConfigBaseID = t.ItemConfigBaseID,
                           ItemGroupID = t.ItemGroupID,
                           ItemSize = t.ItemSize,
                           ItemClassID = t.ItemClassID,
                           Number = t.Number,
                           OriginalItemNo = t.OriginalItemNo,
                           TechnologyGroupID = t.TechnologyGroupID,
                           SupplierItemCode = t7.Code,
                           SupplierItemName = t7.CodeName,
                           SupplierItemID = t.SupplierItemID,
                           TextureGroupID = t.TextureGroupID,
                           Transparency = t.Transparency,
                           UnitGroupID = t.UnitGroupID,
                           YearNo = t.YearNo,
                           BusinessGroupText = t1.CodeName,
                           ItemGroupText = t2.CodeName,
                           ItemClassText = t.ItemClassID.GetDescription(),
                           TechnologyText = t4.CodeName,
                           TextureGroupText = t5.CodeName,
                           UnitGroupText = t6.CodeName,
                           ClientID = t.ClientID,
                           ClientCode = t3.Code,
                           ClientName = t3.ShortName,
                           Remark = t.Remark,
                           IsElemShow = t.IsElemShow,
                           UnitSellingPrice = t.UnitSellingPrice,
                           PlaceOfOrigin = t.PlaceOfOrigin,
                           IsClientItem = t.IsClientItem,
                           ItemFold = t.ItemFold,
                           ItemFoldText = t.ItemFold.HasValue ? t.ItemFold.Value.GetDescription() : null,
                           SeasonGroupID = t.SeasonGroupID,
                           IsRollItem = t.IsRollItem,
                           SeasonGroupCode = t8.Code,
                           SeasonGroupName = t8.CodeName,
                           NotInBill = t.NotInBill,
                           LenShrink = t.LenShrink,
                           HasImages= t2.HasImages.HasValue?t2.HasImages.Value:false,
                           NeedImages= t2.HasImages.HasValue?t2.HasImages.Value:false,
                       });
            var query = sql.WhereIf(input.BusinessGroupID.HasValue, a => a.BusinessGroup == input.BusinessGroupID)
                    .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID)
                    .WhereIf(input.ItemGroupID.HasValue, a => a.ItemGroupID == input.ItemGroupID)
                    .WhereIf(input.IsClientItem.HasValue, a => a.IsClientItem == input.IsClientItem.Value)
                    .WhereIf(input.ItemClassID.HasValue, a => a.ItemClassID == input.ItemClassID)
                    .WhereIf(input.TechnologyGroupID.HasValue, a => a.TechnologyGroupID == input.TechnologyGroupID)
                    .WhereIf(input.TextureGroupID.HasValue, a => a.TextureGroupID == input.TextureGroupID)
                    .WhereIf(input.UnitGroupID.HasValue, a => a.UnitGroupID == input.UnitGroupID)
                    .WhereIf(input.SeasonGroupID.HasValue, a => a.SeasonGroupID == input.SeasonGroupID)
                 .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.Code.ToLower().Contains(input.Text.ToLower()) || a.CodeName.ToLower().Contains(input.Text.ToLower()) || a.OriginalItemNo.Contains(input.Text));
            var count = await query.CountAsync();

            var result = new List<BAD_ItemGetAllOutputDto>();
            if (input.ItemClassID.HasValue && input.ItemClassID == BAD_ItemClass.ML)
            {
                 result = await query.OrderByDescending(a => a.YearNo).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
               
            }
            else
            {
                 result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            }
         
            var itemIDs = result.Where(a => a.NeedImages.HasValue && a.NeedImages.Value).Select(a => a.Id).ToList();
            var itemimages =await itemImageRepository.GetAll().Where(a => itemIDs.Contains(a.ItemID)).Select(a => a.ItemID).ToListAsync();
            foreach (var item in result)
            {
                if (item.NeedImages.HasValue && item.NeedImages.Value)
                {
                    item.HasImages = itemimages.Any(a => a == item.Id);
                }
            }
            return new PagedResultDto<BAD_ItemGetAllOutputDto>(count, result);
        }
    }
}

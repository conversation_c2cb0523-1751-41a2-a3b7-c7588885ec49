/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYM_ClientAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/10/星期五 21:51:48
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Collections.Extensions;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using XiaoMa.Domain.Entities.Auditing;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Base;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Domain.SYM;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientAppService : XMMTMAppServiceBase, IBAD_ClientAppService
    {
        private readonly IRepository<Client, Guid> repository;
        private readonly IRepository<ClientPerson, Guid> clientPersonRepository;
        private readonly IRepository<ClientAddress, Guid> clientAddressRepository;
        private readonly IRepository<ClientShop, Guid> clientShopRepository;
        private readonly IRepository<ClientModel, Guid> clientModelRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IRepository<User, Guid> userRepository;
        private readonly IBAD_ClientModelClassAppService bAD_ClientModelClassAppService;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientAppService(
       IRepository<Client, Guid> repository,
          IRepository<ClientPerson, Guid> clientPersonRepository,
          IRepository<ClientAddress, Guid> clientAddressRepository,
          IRepository<ClientShop, Guid> clientShopRepository,
          IRepository<ClientModel, Guid> clientModelRepository,
          IRepository<Sorder, Guid> sorderRepository,
          IRepository<User, Guid> userRepository,
          IBAD_ClientModelClassAppService bAD_ClientModelClassAppService,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.clientPersonRepository = clientPersonRepository;
            this.clientAddressRepository = clientAddressRepository;
            this.clientShopRepository = clientShopRepository;
            this.clientModelRepository = clientModelRepository;
            this.sorderRepository = sorderRepository;
            this.userRepository = userRepository;
            this.bAD_ClientModelClassAppService = bAD_ClientModelClassAppService;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientGetAllOutputDto>> Get(BAD_ClientGetAllInputDto input)
        {
            var clientQuery = GetClientQuery();
            var query = clientQuery.Where(a => !a.ParentClientID.HasValue)
                         .WhereIf(input.ClientGrade.HasValue, a => a.ClientGrade == input.ClientGrade.Value)
                         .WhereIf(input.ClientGroup.HasValue, a => a.ClientGroup == input.ClientGroup.Value)
                         .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.ToLower().Contains(input.Text.ToLower()) || a.Code.ToLower().Contains(input.Text.ToLower()) || a.ShortName.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.ModifyOn).ThenBy(a => a.FirstNum).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<BAD_ClientGetAllOutputDto>>(result);
            result = await this.getClientChilds(result);
            return new PagedResultDto<BAD_ClientGetAllOutputDto>(count, result);
        }

        private IQueryable<BAD_ClientGetAllOutputDto> GetClientQuery()
        {
            var clientQuery = (from t in repository.GetAll()
                               join t1x in userRepository.GetAll() on t.SalesmanId equals t1x.Id into t1xx
                               from t1 in t1xx.DefaultIfEmpty()
                               join t2x in userRepository.GetAll() on t.SalesmanId1 equals t2x.Id into t2xx
                               from t2 in t2xx.DefaultIfEmpty()
                               select new BAD_ClientGetAllOutputDto
                               {
                                   Code = t.Code,
                                   Source = t.Source,
                                   AccountBalance = t.AccountBalance,
                                   AccountLimit = t.AccountLimit,
                                   AccountPaymentGroup = t.AccountPaymentGroup,
                                   //AccountPaymentGroupText = t.AccountPaymentGroupText,
                                   ChannelCode = t.ChannelCode,
                                   Remark = t.Remark,
                                   CreateBy = t.CreateBy,
                                   Street = t.Street,
                                   Address = t.Address,
                                   Area = t.Area,
                                   ModifyBy = t.ModifyBy,
                                   BankID = t.BankID,
                                   City = t.City,
                                   ClientCode1 = t.ClientCode1,
                                   ClientGroup = t.ClientGroup,
                                   ClientGrade = t.ClientGrade,
                                   Province = t.Province,
                                   CodeName = t.CodeName,
                                   Id = t.Id,
                                   SupplierCode = t.SupplierCode,
                                   Contact = t.Contact,
                                   Discount = t.Discount,
                                   IsActive = t.IsActive,
                                   CreateID = t.CreateID,
                                   CreateOn = t.CreateOn,
                                   Email = t.Email,
                                   Owner = t.Owner,
                                   FirstNum = t.FirstNum,
                                   SalerCode = t.SalerCode,
                                   ExchangeRate = t.ExchangeRate,
                                   Fax = t.Fax,
                                   CustomerCode = t.CustomerCode,
                                   ItemDiscount = t.ItemDiscount,
                                   CurrencyType = t.CurrencyType,
                                   InheritedParentBill = t.InheritedParentBill,
                                   Mobile = t.Mobile,
                                   ModifyID = t.ModifyID,
                                   ModifyOn = t.ModifyOn,
                                   OwnerID = t.OwnerID,
                                   PaymentMethodID = t.PaymentMethodID,
                                   ClientGroupText = t.ClientGroup.GetDescription(),
                                   ClientGradeText = t.ClientGrade.GetDescription(),
                                   AccountPaymentGroupText = t.AccountPaymentGroup.HasValue ? t.AccountPaymentGroup.Value.GetDescription() : null,
                                   CurrencyTypeText = t.CurrencyType.HasValue ? t.CurrencyType.GetDescription() : null,
                                   ClientTypeCode = t.ClientTypeCode,
                                   InheritedParentModel = t.InheritedParentModel,
                                   SalerName = t.SalerName,
                                   SalesmanId = t.SalesmanId,
                                   SalesmanId1 = t.SalesmanId1,
                                   SalesmanName = t1.Name,
                                   SalesmanName1 = t2.Name,
                                   SecondNum = t.SecondNum,
                                   ZipCode = t.ZipCode,
                                   PaymentTermsID = t.PaymentTermsID,
                                   State = t.State,
                                   ParentClientID = t.ParentClientID,
                                   VatID = t.VatID,
                                   ShortName = t.ShortName,
                                   Tel = t.Tel,
                               });
            return clientQuery;
        }
        private async Task<List<BAD_ClientGetAllOutputDto>> getClientChilds(List<BAD_ClientGetAllOutputDto> list)
        {
            var clientids = list.Select(a => a.Id).Distinct().ToList();
            var clientquery = GetClientQuery();
            var clients = await (from t in clientquery
                                 where t.ParentClientID.HasValue && clientids.Contains(t.ParentClientID.Value)
                                 select t).ToListAsync();
            if (!clients.Any())
            {
                return list;
            }
            var clientList = objectMapper.Map<List<BAD_ClientGetAllOutputDto>>(clients);
            foreach (var item in list)
            {
                var childs = clientList.Where(a => a.ParentClientID.HasValue && a.ParentClientID.Value == item.Id).ToList();
                if (childs.Any())
                {
                    item.Childs.AddRange(childs);
                    await this.getClientChilds(childs);
                }

            }
            return list;

        }
        [HttpPost]
        [AbpAuthorize]
        public virtual async Task Adds(List<BAD_ClientDto> input)
        {
            foreach (var entity in input)
            {
                var id = Guid.Empty;
                using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
                {
                    var oldentity = ObjectMapper.Map<Client>(entity);
                    await CreateCode(oldentity);
                    id = await repository.InsertAndGetIdAsync(oldentity);
                    await unit.CompleteAsync();
                }
                if (id != Guid.Empty)
                {
                    //版型系列
                    await bAD_ClientModelClassAppService.InheritedParentModel(id);
                }



            }
        }
        [HttpPost]
        [AbpAuthorize]
        public virtual async Task Updates(List<BAD_ClientDto> input)
        {
            foreach (var entity in input)
            {
                using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
                {
                    if (entity.ClientModel != null && entity.ClientModel.Any())
                    {
                        var addsModel = entity.ClientModel.Where(a => a.Id == Guid.Empty && a.Selected).ToList();
                        var editsModel = entity.ClientModel.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected).ToList();
                        var delsModel = entity.ClientModel.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected).ToList();
                        foreach (var item in delsModel)
                        {
                            await clientModelRepository.DeleteAsync(item.Id.Value);
                        }
                        entity.ClientModel = addsModel;

                    }

                    var oldentity = repository.Get(entity.Id.Value);
                    ObjectMapper.Map(entity, oldentity);

                    await CreateCode(oldentity);
                    var id = oldentity.Id;
                    oldentity.Code = await ChangeCode(oldentity);
                    oldentity.Id = id;
                    await repository.UpdateAsync(oldentity);
                    await updateChilds(oldentity);
                    await unit.CompleteAsync();
                }

                //版型系列
                await bAD_ClientModelClassAppService.InheritedParentModel(entity.Id.Value);
            }
        }

        private async Task<string> ChangeCode(Client client)
        {
            if (!await repository.GetAll().AnyAsync(a => a.Code == client.Code && a.Id != client.Id))
            {
                return client.Code;
            }
            var _client = objectMapper.Map<Client>(client);
            _client.Id = Guid.Empty;
            var dto = await CreateCode(_client);
            return dto.Code;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="clientIds"></param>
        /// <param name="list"></param>
        /// <param name="FilterInheritedParentBill"></param>
        /// <param name="FilterInheritedParentModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<List<Guid>> GetChildClient(List<Guid> clientIds, List<Guid> list, bool FilterInheritedParentBill = false, bool FilterInheritedParentModel = false)
        {
            list.AddRange(clientIds);
            var clients = await (from t in repository.GetAll()
                                 where t.IsActive &&
                               clientIds.Contains(t.Id) ||
                                 clientIds.Contains(t.ParentClientID.Value)
                                 select new { t.Id, ParentClientID = t.ParentClientID, t.InheritedParentBill, t.InheritedParentModel }).ToListAsync();
            if (FilterInheritedParentBill)
            {
                clients = clients.Where(a => a.InheritedParentBill.HasValue && a.InheritedParentBill.Value).ToList();
            }
            if (FilterInheritedParentModel)
            {
                clients = clients.Where(a => a.InheritedParentModel.HasValue && a.InheritedParentModel.Value).ToList();
            }
            if (clients.Any())
            {
                var ids = clients.Select(a => a.Id).ToList();
                //var pids = clients.Where(a => a.ParentClientID.HasValue).Select(a => a.ParentClientID.Value).ToList();
                //ids.AddRange(pids);

                var _ids = ids.Except(list).ToList();
                if (!_ids.Any())
                {
                    return list.Distinct().ToList();

                }
                list.AddRange(ids);
                list = list.Distinct().ToList();
                await this.GetChildClient(_ids, list, FilterInheritedParentBill, FilterInheritedParentBill);
            }
            return list.Distinct().ToList();

        }
        private async Task updateChilds(Client dto)
        {
            var childs = await repository.GetAll().Where(a => a.ParentClientID.HasValue && dto.Id == a.ParentClientID.Value).Select(a => a).ToListAsync();
            foreach (var item in childs)
            {
                await CreateCode(item);
                await repository.UpdateAsync(item);
                await this.updateChilds(item);
            }
        }

        [HttpPost]
        [AbpAuthorize]
        public virtual async Task Deletes(List<BAD_ClientDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);

                await checkClientIsUsed(oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        private async Task deleteChilds(Client dto)
        {
            var childs = await repository.GetAll().Where(a => a.ParentClientID.HasValue && a.ParentClientID.Value == dto.Id).Select(a => a).ToListAsync();
            foreach (var item in childs)
            {
                await checkClientIsUsed(item);
                await repository.DeleteAsync(item);
                await this.deleteChilds(item);
            }
        }
        /// <summary>
        /// 简称订单是否有使用客户
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task checkClientIsUsed(Client dto)
        {
            var sorder = await sorderRepository.GetAll().FirstOrDefaultAsync(a => a.ClientID == dto.Id);
            if (sorder != null)
            {
                throw new UserFriendlyException($"订单[{sorder.Code}]已使用此[{dto.Code}]客户信息,请先删除订单数据后再重试！");
            }
        }
        private async Task checkClientHasChild(Client dto)
        {
            var client = await repository.FirstOrDefaultAsync(a => a.ParentClientID == dto.Id);
            if (client != null)
            {
                throw new UserFriendlyException($"子客户{client.Code}:{client.CodeName}已经绑定当前删除的客户,请先删除子客户！");
            }
        }
        protected async Task<bool> ExistCode(BAD_ClientDto input)
        {
            if (input is IHasCodeEntity codeEntity)
            {
                if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
                {
                    return await repository.GetAll().AnyAsync(a => a.Code.Equals(codeEntity.Code));
                }
                else
                {
                    return await repository.GetAll().AnyAsync(a => a.Code.Equals(codeEntity.Code) && a.Id != input.Id.Value);
                }
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 创建客户编码
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task<Client> CreateCode(Client dto)
        {
            //第一组（第一位） P 客户分类  P D G  (供应商编码为1-999) 其他客户为1000以上
            //第二组（第二位） 客户等级 A B C
            //第三组（3-7位） 编码
            //第四组 （8-10位） 子编码类似子店铺
            //code P A 00001 001
            //     D C 00002 001
            //     D C 00002 002
            var a = dto.ClientGroup.ToString();
            //var b = dto.ClientGrade.ToString();
            dto = await getFirstNum(dto);
            dto = await getSecondNum(dto);
            var c = (dto.FirstNum).ToString();
            var d = (dto.SecondNum).ToString();

            c = c.PadLeft(5, '0');
            d = d.PadLeft(3, '0');
            dto.Code = a + c + d;
            return dto;
        }

        /// <summary>
        /// 生成客户编码第三组编码
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task<Client> getFirstNum(Client dto)
        {
            if (dto.ParentClientID.HasValue && dto.ParentClientID != Guid.Empty)
            {
                var client = await repository.GetAsync(dto.ParentClientID.Value);
                dto.FirstNum = client.FirstNum.Value;
                return dto;
            }
            else
            {
                if (dto.Id == Guid.Empty)
                {
                    var clients = repository.GetAll();
                    if (dto.ClientGroup == Enums.ClientGroupEnums.G)
                    {
                        if (clients.Any(a => a.ClientGroup == dto.ClientGroup))
                        {
                            var f = await clients.Where(a => a.FirstNum.HasValue && a.ClientGroup == dto.ClientGroup).Select(a => a.FirstNum.Value).ToListAsync();
                            if (!f.Any())
                            {
                                dto.FirstNum = 1;
                                return dto;
                            }
                            else
                            {
                                var number = f.Max(a => a);
                                if (number > 10001)
                                {
                                    throw new UserFriendlyException("供应商编码已超过最大数1000");
                                }
                                dto.FirstNum = number == 0 ? 1 : number + 1;
                                return dto;
                            }

                        }
                        else
                        {
                            dto.FirstNum = 1;
                            return dto;
                        }

                    }
                    else
                    {
                        var f = await repository.GetAll().Where(a => a.FirstNum.HasValue && a.ClientGroup == dto.ClientGroup).Select(a => a.FirstNum.Value).ToListAsync();
                        if (!f.Any())
                        {
                            dto.FirstNum = 1;
                        }
                        else
                        {
                            var number = f.Max(a => a);
                            number = number + 1;
                            dto.FirstNum = number;
                        }

                        return dto;
                    }
                }
                else
                {
                    return dto;
                }
            }
        }
        /// <summary>
        /// 生成客户编码第四组编码
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private async Task<Client> getSecondNum(Client dto)
        {
            if (dto.ParentClientID.HasValue && dto.ParentClientID != Guid.Empty)
            {
                if (dto.Id != Guid.Empty)
                {
                    var number = await repository.GetAll().Where(a => (a.ParentClientID == dto.ParentClientID.Value || a.Id == dto.ParentClientID.Value) && a.SecondNum.HasValue && dto.Id != a.Id).Select(a => a.SecondNum.Value).MaxAsync(a => a);
                    dto.SecondNum = number + 1;
                    return dto;
                }
                else
                {
                    return dto;
                }

            }
            else
            {
                dto.SecondNum = 1;
                return dto;
            }

        }

        public async Task<Client> GetParentClient(ParentClientInputDto input)
        {

            var client = await repository.FirstOrDefaultAsync(input.ClientID);
            if (client == null)
            {
                return null;
            }
            if (client.ParentClientID.HasValue && (input.InheritedParentModel || input.InheritedParentBill))
            {
                client = await getParentClient(client.ParentClientID.Value);
            }
            return client;
        }

        private async Task<Client> getParentClient(Guid clientID)
        {
            var pclient = await repository.FirstOrDefaultAsync(clientID);
            if (pclient == null)
            {
                return await repository.FirstOrDefaultAsync(clientID); ;
            }
            if (pclient.InheritedParentModel.HasValue && pclient.InheritedParentModel.Value && pclient.ParentClientID.HasValue)
            {
                await this.getParentClient(pclient.ParentClientID.Value);
            }
            return pclient;
        }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientPersonGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/24/星期五 17:14:05
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientPersonGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ClientPersonGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        /// <summary>
        /// ClientId
        /// </summary>
        [Required]
        public Guid Id { set; get; }

        public bool? IsActive { set; get; }
    }
}

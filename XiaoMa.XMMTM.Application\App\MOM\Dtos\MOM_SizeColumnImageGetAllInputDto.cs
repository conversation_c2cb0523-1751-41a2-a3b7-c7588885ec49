﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeColumnImageGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/10/15/星期四 14:29:17
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeColumnImageGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_SizeColumnImageGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? SizeColumnID { set; get; }

        public Guid? ModelID { set; get; }
    }
}

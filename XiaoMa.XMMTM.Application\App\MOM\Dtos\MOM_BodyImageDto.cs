﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_BodyImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:20:02
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_BodyImageDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_BodyImageDto : EntityDto<Guid?>
    {
        /// <summary>
        ///
        /// </summary>

        public int? ImageSeq { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid BodyID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? BodyID1 { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelImageID { get; set; }
        public virtual bool IsActive { set; get; }
    }
}

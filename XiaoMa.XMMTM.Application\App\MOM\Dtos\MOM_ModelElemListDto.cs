/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemListDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/28/星期二 16:19:41
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemListDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemListDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        public virtual bool IsActive { set; get; }
        /// <summary>
        /// CAD顺序
        /// </summary>

        public int? CadSeq { get; set; }

        /// <summary>
        /// 市场顺序
        /// </summary>

        public int? MarketSeq { get; set; }

        /// <summary>
        /// 报表顺序
        /// </summary>

        public int? ElemSeq { get; set; }

        /// <summary>
        /// 
        /// </summary>

        public bool ElemShow { get; set; }

        /// <summary>
        /// 报表显示
        /// </summary>

        public int ReportShow { get; set; }

        /// <summary>
        /// 计划显示?
        /// </summary>

        public bool IsPlanShow { get; set; }

        /// <summary>
        ///计划编辑
        /// </summary>

        public bool IsPlanEdit { get; set; }

        /// <summary>
        /// 属于辅料？
        /// </summary>

        public bool IsItem { get; set; }

        /// <summary>
        /// 客户端显示？
        /// </summary>

        public bool IsClientShow { get; set; }
        /// <summary>
        ///客服显示
        /// </summary>

        public bool IsCustomerShow { get; set; }

        /// <summary>
        ///客服编辑 ？
        /// </summary>

        public bool IscustomerEdit { get; set; }

        ///// <summary>
        /////内容输入?
        ///// </summary>

        //public bool IsInput { get; set; }


        /// <summary>
        /// 款式部位ID
        /// </summary>

        public Guid ModelElemBaseID { get; set; }
        /// <summary>
        /// 深定制简定制
        /// </summary>
        public bool IsPlus { set; get; } = true;

        /// <summary>
        /// 款式类别
        /// </summary>

        public SystemConfig.SYS_ModelElemType ModelElemTypeID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? GroupID { get; set; }


        /// <summary>
        ///
        /// </summary>

        public string SapPartsCode { get; set; }



        /// <summary>
        /// 性别
        /// </summary>
        public bool GenderID { get; set; }

        /// <summary>
        /// 返修选项
        /// </summary>
        public bool? IsRepair { set; get; }

        /// <summary>
        /// 衣拿吊挂款式编码生成规则位置
        /// </summary>
        public InaStyleIndexEnums? InaStyleIndex { set; get; }
    }
}

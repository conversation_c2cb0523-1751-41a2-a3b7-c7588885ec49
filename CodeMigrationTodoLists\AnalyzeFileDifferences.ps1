# Analyze File Differences Script
param(
    [string]$SourcePath = "e:\workgit\xm\XiaoMa.MTM\XiaoMa.XMMTM.Core",
    [string]$TargetPath = "e:\workgit\Shop\XiaoMa.MTM\XiaoMa.Shop.XMMTM.Core",
    [int]$MaxFiles = 10
)

Write-Host "Analyzing file differences for Core module..." -ForegroundColor Green

# Load comparison results
$comparisonFile = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
if (-not (Test-Path $comparisonFile)) {
    Write-Host "Error: Comparison file not found - $comparisonFile" -ForegroundColor Red
    exit 1
}

$comparisonData = Get-Content $comparisonFile | ConvertFrom-Json
$bothExistFiles = $comparisonData.Files | Where-Object { $_.Status -eq "Both Exist" }

Write-Host "Found $($bothExistFiles.Count) files that exist in both projects" -ForegroundColor Cyan
Write-Host "Analyzing first $MaxFiles files..." -ForegroundColor Yellow

$analysisResults = @()

# Priority files to analyze first
$priorityFiles = @(
    "XMMTMCoreModule.cs",
    "XMMTMConsts.cs", 
    "AppVersionHelper.cs",
    "RateLimitingActionFilter.cs",
    "Authorization\PermissionNames.cs",
    "Authorization\XMMTMAuthorizationProvider.cs"
)

# Analyze priority files first
foreach ($priorityFile in $priorityFiles) {
    $file = $bothExistFiles | Where-Object { $_.RelativePath -eq $priorityFile }
    if ($file) {
        Write-Host "Analyzing priority file: $($file.RelativePath)" -ForegroundColor Cyan
        
        try {
            # Read source file
            $sourceContent = Get-Content $file.SourcePath -Raw -Encoding UTF8
            $targetContent = Get-Content $file.TargetPath -Raw -Encoding UTF8
            
            # Basic difference analysis
            $sourceLines = $sourceContent -split "`n"
            $targetLines = $targetContent -split "`n"
            
            # Check for namespace differences
            $sourceNamespaces = $sourceLines | Where-Object { $_ -match "^namespace\s+" }
            $targetNamespaces = $targetLines | Where-Object { $_ -match "^namespace\s+" }
            
            # Check for using statements
            $sourceUsings = $sourceLines | Where-Object { $_ -match "^using\s+" }
            $targetUsings = $targetLines | Where-Object { $_ -match "^using\s+" }
            
            # Simple content comparison (ignoring namespace differences)
            $normalizedSource = $sourceContent -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"
            $contentIdentical = ($normalizedSource -eq $targetContent)
            
            $analysis = [PSCustomObject]@{
                FileName = $file.FileName
                RelativePath = $file.RelativePath
                SourcePath = $file.SourcePath
                TargetPath = $file.TargetPath
                SourceLines = $sourceLines.Count
                TargetLines = $targetLines.Count
                SourceNamespaces = $sourceNamespaces -join "; "
                TargetNamespaces = $targetNamespaces -join "; "
                SourceUsings = $sourceUsings.Count
                TargetUsings = $targetUsings.Count
                ContentIdentical = $contentIdentical
                SizeBytes = @{
                    Source = (Get-Item $file.SourcePath).Length
                    Target = (Get-Item $file.TargetPath).Length
                }
                LastModified = @{
                    Source = (Get-Item $file.SourcePath).LastWriteTime
                    Target = (Get-Item $file.TargetPath).LastWriteTime
                }
                Priority = "High"
                DifferenceType = if ($contentIdentical) { "Namespace Only" } else { "Content Difference" }
            }
            
            $analysisResults += $analysis
            
            Write-Host "  Lines: Source=$($analysis.SourceLines), Target=$($analysis.TargetLines)" -ForegroundColor White
            Write-Host "  Content identical (after namespace normalization): $($analysis.ContentIdentical)" -ForegroundColor White
            Write-Host "  Difference type: $($analysis.DifferenceType)" -ForegroundColor White
            
        } catch {
            Write-Host "  Error analyzing file: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Analyze a few more non-priority files
$remainingFiles = $bothExistFiles | Where-Object { $_.RelativePath -notin $priorityFiles } | Select-Object -First ($MaxFiles - $priorityFiles.Count)

foreach ($file in $remainingFiles) {
    Write-Host "Analyzing file: $($file.RelativePath)" -ForegroundColor Cyan
    
    try {
        $sourceContent = Get-Content $file.SourcePath -Raw -Encoding UTF8
        $targetContent = Get-Content $file.TargetPath -Raw -Encoding UTF8
        
        $sourceLines = $sourceContent -split "`n"
        $targetLines = $targetContent -split "`n"
        
        $normalizedSource = $sourceContent -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"
        $contentIdentical = ($normalizedSource -eq $targetContent)
        
        $analysis = [PSCustomObject]@{
            FileName = $file.FileName
            RelativePath = $file.RelativePath
            SourcePath = $file.SourcePath
            TargetPath = $file.TargetPath
            SourceLines = $sourceLines.Count
            TargetLines = $targetLines.Count
            SourceNamespaces = ($sourceLines | Where-Object { $_ -match "^namespace\s+" }) -join "; "
            TargetNamespaces = ($targetLines | Where-Object { $_ -match "^namespace\s+" }) -join "; "
            SourceUsings = ($sourceLines | Where-Object { $_ -match "^using\s+" }).Count
            TargetUsings = ($targetLines | Where-Object { $_ -match "^using\s+" }).Count
            ContentIdentical = $contentIdentical
            SizeBytes = @{
                Source = (Get-Item $file.SourcePath).Length
                Target = (Get-Item $file.TargetPath).Length
            }
            LastModified = @{
                Source = (Get-Item $file.SourcePath).LastWriteTime
                Target = (Get-Item $file.TargetPath).LastWriteTime
            }
            Priority = "Medium"
            DifferenceType = if ($contentIdentical) { "Namespace Only" } else { "Content Difference" }
        }
        
        $analysisResults += $analysis
        
        Write-Host "  Difference type: $($analysis.DifferenceType)" -ForegroundColor White
        
    } catch {
        Write-Host "  Error analyzing file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Save analysis results
$outputPath = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\FileAnalysisResults.json"
$finalResult = @{
    Summary = @{
        TotalAnalyzed = $analysisResults.Count
        NamespaceOnly = ($analysisResults | Where-Object { $_.DifferenceType -eq "Namespace Only" }).Count
        ContentDifference = ($analysisResults | Where-Object { $_.DifferenceType -eq "Content Difference" }).Count
        GeneratedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    Files = $analysisResults
}

$finalResult | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host ""
Write-Host "=== Analysis Summary ===" -ForegroundColor Green
Write-Host "Total analyzed: $($analysisResults.Count) files" -ForegroundColor Cyan
Write-Host "Namespace only differences: $($finalResult.Summary.NamespaceOnly) files" -ForegroundColor Yellow
Write-Host "Content differences: $($finalResult.Summary.ContentDifference) files" -ForegroundColor Red
Write-Host ""
Write-Host "Results saved to: $outputPath" -ForegroundColor Green

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYS_ModelTypeAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/17/星期五 9:56:55
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.SYS.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.SYS
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelTypeAppService : XMMTMAppServiceBase, IMOM_ModelTypeAppService
    {
        private readonly IRepository<ModelType, Guid> repository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly FileServer.FileServer fileServer;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelTypeAppService(
       IRepository<ModelType, Guid> repository,
       IRepository<ModelImage, Guid> modelImageRepository,
 FileServer.FileServer fileServer,
        IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelImageRepository = modelImageRepository;
            this.fileServer = fileServer;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelTypeGetAllOutputDto>> Get(MOM_ModelTypeGetAllInputDto input)
        {
            var query = repository.GetAll()
                      //.Where(a => a.IsActive)
                      .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var imagesid = result.Where(a => a.ModelImageID.HasValue).Select(a => a.ModelImageID);
            var modelimages = await modelImageRepository.GetAll().Where(a => imagesid.Contains(a.Id)).ToListAsync();

            var list = objectMapper.Map<List<MOM_ModelTypeGetAllOutputDto>>(result);
            foreach (var item in list)
            {
                if (item.ModelImageID.HasValue)
                {
                    var image = modelimages.FirstOrDefault(a => a.Id == item.ModelImageID);
                    item.ImagePath = image == null ? null : fileServer.GetImageUrl(image.ImagePath);
                }

            }
            return new PagedResultDto<MOM_ModelTypeGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelTypeDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelType>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelTypeDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelTypeDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelTypeDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

# 文件对比脚本
param(
    [string]$SourcePath = "e:\workgit\xm\XiaoMa.MTM",
    [string]$TargetPath = "e:\workgit\Shop\XiaoMa.MTM"
)

Write-Host "开始扫描文件..." -ForegroundColor Green

# 获取源项目文件
Write-Host "扫描源项目: $SourcePath" -ForegroundColor Yellow
$sourceFiles = @()
Get-ChildItem -Path $SourcePath -Recurse -Filter "*.cs" | Where-Object {
    $_.FullName -notlike "*\obj\*" -and
    $_.FullName -notlike "*\bin\*"
} | ForEach-Object {
    $relativePath = $_.FullName.Replace($SourcePath, "").TrimStart('\')
    $sourceFiles += [PSCustomObject]@{
        Name         = $_.Name
        RelativePath = $relativePath
        FullPath     = $_.FullName
        Module       = ($relativePath -split '\\')[0]
    }
}

Write-Host "源项目文件数: $($sourceFiles.Count)" -ForegroundColor Cyan

# 获取目标项目文件
Write-Host "扫描目标项目: $TargetPath" -ForegroundColor Yellow
$targetFiles = @()
Get-ChildItem -Path $TargetPath -Recurse -Filter "*.cs" | Where-Object {
    $_.FullName -notlike "*\obj\*" -and
    $_.FullName -notlike "*\bin\*"
} | ForEach-Object {
    $relativePath = $_.FullName.Replace($TargetPath, "").TrimStart('\')
    $targetFiles += [PSCustomObject]@{
        Name         = $_.Name
        RelativePath = $relativePath
        FullPath     = $_.FullName
        Module       = ($relativePath -split '\\')[0]
    }
}

Write-Host "目标项目文件数: $($targetFiles.Count)" -ForegroundColor Cyan

# 找出相同的文件（基于相对路径和文件名）
Write-Host "开始对比文件..." -ForegroundColor Green
$commonFiles = @()
foreach ($sourceFile in $sourceFiles) {
    # 转换命名空间路径
    $targetRelativePath = $sourceFile.RelativePath -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"

    $matchingTarget = $targetFiles | Where-Object {
        $_.RelativePath -eq $targetRelativePath -or
        ($_.Name -eq $sourceFile.Name -and $_.Module -like "*XMMTM*")
    }

    if ($matchingTarget) {
        $commonFiles += [PSCustomObject]@{
            FileName       = $sourceFile.Name
            SourcePath     = $sourceFile.RelativePath
            TargetPath     = $matchingTarget.RelativePath
            SourceModule   = $sourceFile.Module
            TargetModule   = $matchingTarget.Module
            SourceFullPath = $sourceFile.FullPath
            TargetFullPath = $matchingTarget.FullPath
        }
    }
}

Write-Host "找到相同文件数: $($commonFiles.Count)" -ForegroundColor Cyan

# 按模块分组统计
$moduleStats = @()
$commonFiles | Group-Object SourceModule | ForEach-Object {
    $moduleStats += [PSCustomObject]@{
        Module = $_.Name
        Count  = $_.Count
        Files  = $_.Group
    }
}

# 输出结果
Write-Host ""
Write-Host "=== 文件对比统计 ===" -ForegroundColor Green
Write-Host "源项目路径: $SourcePath" -ForegroundColor Yellow
Write-Host "目标项目路径: $TargetPath" -ForegroundColor Yellow
Write-Host ""

Write-Host "=== 模块统计 ===" -ForegroundColor Green
foreach ($stat in $moduleStats) {
    Write-Host "$($stat.Module): $($stat.Count) 个相同文件" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "总计: $($commonFiles.Count) 个相同文件" -ForegroundColor Green

# 保存详细结果到JSON文件
$outputPath = Join-Path $TargetPath "CodeMigrationTodoLists\FileComparisonResult.json"
$result = @{
    Summary     = @{
        TotalCommonFiles = $commonFiles.Count
        ModuleStats      = $moduleStats
        GeneratedAt      = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    CommonFiles = $commonFiles
}

$result | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8
Write-Host ""
Write-Host "详细结果已保存到: $outputPath" -ForegroundColor Green

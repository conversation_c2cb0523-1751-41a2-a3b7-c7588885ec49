﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_CadLayoutModelElemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/6/星期四 16:00:10
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_CadLayoutModelElemAppService : XMMTMAppServiceBase, IMOM_CadLayoutModelElemAppService
    {
        private readonly IRepository<CadLayoutModelElem, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<CadLayout, Guid> cadLayoutRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_CadLayoutModelElemAppService(
       IRepository<CadLayoutModelElem, Guid> repository,
       IRepository<CadLayout, Guid> cadLayoutRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.cadLayoutRepository = cadLayoutRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_CadLayoutModelElemGetAllOutputDto>> Get(MOM_CadLayoutModelElemGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1 in cadLayoutRepository.GetAll() on t.CadLayoutID equals t1.Id
                         join t2 in modelElemRepository.GetAll() on t.ModelElemID equals t2.Id
                         join t3 in modelElemListRepository.GetAll() on t2.ModelElemListID equals t3.Id
                         where t.IsActive
                         select new MOM_CadLayoutModelElemGetAllOutputDto()
                         {
                             Id = t.Id,
                             Default = t.Default,
                             Remark = t.Remark,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelElemID = t.ModelElemID,
                             CadLayoutID = t.CadLayoutID,
                             CadLayoutText = t1.Code + ": " + t1.CodeName,
                             Suffix = t1.Suffix,
                             //ModelElemText = t2.Code + ": " + t2.CodeName,
                             ModelElemText = t2.Code + (string.IsNullOrEmpty(t2.CodeName) ? "" : (":" + t2.CodeName)),
                             ModelElemListID = t2.ModelElemListID,
                             ModelElemListText = t3.Code + ": " + t3.CodeName,
                         })
            .WhereIf(input.CadLayoutID.HasValue,a=>a.CadLayoutID==input.CadLayoutID.Value)
            .WhereIf(input.ModelElemID.HasValue,a=>a.ModelElemID == input.ModelElemID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CadLayoutText.Contains(input.Text) || a.ModelElemText.Contains(input.Text) || a.ModelElemListText.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CadLayoutText).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_CadLayoutModelElemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_CadLayoutModelElemDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<CadLayoutModelElem>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_CadLayoutModelElemDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_CadLayoutModelElemDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemPriceGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2021/6/4/星期五 10:42:16
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemPriceGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemPriceGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid Id { set; get; }

        public Guid? ModelElemListID { set; get; }
        public Guid? ClientID { set; get; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public ODM_SorderType? SorderType { set; get; }
        public bool ? ClientHasCommon { set; get; }
        public bool ? IsCommon { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemImageGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2021/1/11/星期一 11:13:57
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemImageGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ItemImageGetAllOutputDto : ItemImage
    {
        public string Name { set; get; }
    }
}

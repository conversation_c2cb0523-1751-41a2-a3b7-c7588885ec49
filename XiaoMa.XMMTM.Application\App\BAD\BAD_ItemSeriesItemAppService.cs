﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemSeriesItemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 19:46:34
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ItemSeriesItemAppService : XMMTMAppServiceBase, IBAD_ItemSeriesItemAppService
    {
        private readonly IRepository<ItemSeriesItem, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public BAD_ItemSeriesItemAppService(
       IRepository<ItemSeriesItem, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemSeriesItemGetAllOutputDto>> Get(BAD_ItemSeriesItemGetAllInputDto input)
        {
            var query = repository.GetAll().Include("DAT_Item")
              .Where(a =>  a.ItemSeriesID == input.Id)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.SizeCode.Contains(input.Text) ).Select(a => new BAD_ItemSeriesItemGetAllOutputDto()
              {
                  Remark = a.Remark,
                  CreateBy = a.CreateBy,
                  CreateID = a.CreateID,
                  CreateOn = a.CreateOn,
                  Id = a.Id,
                  IsActive = a.IsActive,
                  ItemID = a.ItemID,
                  ModifyBy = a.ModifyBy,
                  ItemSeriesID = a.ItemSeriesID,
                  ModifyID = a.ModifyID,
                  ModifyOn = a.ModifyOn,
                  SizeCode = a.SizeCode,
                  //ItemCode = a.DAT_Item.Code,
                  //ItemCodeName = a.DAT_Item.CodeName,
              });
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<BAD_ItemSeriesItemGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ItemSeriesItemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ItemSeriesItemDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ItemSeriesItem>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemSeriesItemDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemSeriesItemDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemSeriesItemDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.SizeCode.Equals(input.SizeCode));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.SizeCode.Equals(input.SizeCode) && a.Id != input.Id.Value);
            }

        }
    }
}

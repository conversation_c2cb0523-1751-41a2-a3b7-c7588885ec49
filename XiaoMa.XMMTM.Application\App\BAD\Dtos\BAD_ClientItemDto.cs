﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientItemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2021/6/30/星期三 16:51:16
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientItemDto新增/修改数据对象Dto
    /// </summary>
    [AutoMapTo(typeof(ClientItem))]
    public class BAD_ClientItemDto : EntityDto<Guid?>
    {
        public Guid ClientID { set; get; }

        public Guid ItemID { set; get; }
    }
}

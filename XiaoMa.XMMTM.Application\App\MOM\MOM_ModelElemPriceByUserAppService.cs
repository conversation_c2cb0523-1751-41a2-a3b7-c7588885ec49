#region << 版 本 注 释 >>
/*----------------------------------------------------------------
* 项目名称 ：XiaoMa.XMMTM.App.MOM
* 类 名 称 ：MOM_ModelElemPriceByUserAppService
* 类 描 述 ：
* 命名空间 ：XiaoMa.XMMTM.App.MOM
* 作    者 ：SundayPC
* 创建时间 ：2023/9/25 15:24:46
* 版 本 号 ：v1.0.0.0
*******************************************************************
* Copyright @ SundayPC 2023. All rights reserved.
*******************************************************************
//----------------------------------------------------------------*/
#endregion
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Domain.MOM;
using XiaoMa.XMMTM.Domain.SYM;

namespace XiaoMa.XMMTM.App.MOM
{
    public class MOM_ModelElemPriceByUserAppService : XMMTMAppServiceBase, IMOM_ModelElemPriceByUserAppService
    {
        private readonly IRepository<ModelElemPriceByUser, Guid> repository;
        private readonly IRepository<ModelElemPrice, Guid> modelElemPriceRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<User, Guid> userRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemPriceByUserAppService(
       IRepository<ModelElemPriceByUser, Guid> repository,
       IRepository<ModelElemPrice, Guid> modelElemPriceRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<User, Guid> userRepository,
       IRepository<Item, Guid> itemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<Client, Guid> clientRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemPriceRepository = modelElemPriceRepository;
            this.modelElemRepository = modelElemRepository;
            this.userRepository = userRepository;
            this.itemRepository = itemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.clientRepository = clientRepository;
            this.objectMapper = objectMapper;
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemPriceByUserGetAllOutputDto>> Get(MOM_ModelElemPriceByUserGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in userRepository.GetAll() on t.UserID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelElemPriceRepository.GetAll() on t.ModelElemPriceID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()

                         join t11x in modelElemRepository.GetAll() on t2.ModelElemID equals t11x.Id into t11xx
                         from t11 in t11xx.DefaultIfEmpty()
                         join t22x in itemRepository.GetAll() on t2.ItemID equals t22x.Id into t22xx
                         from t22 in t22xx.DefaultIfEmpty()
                         join t3x in modelElemListRepository.GetAll() on t11.ModelElemListID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in clientRepository.GetAll() on t2.ClientID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in modelElemRepository.GetAll() on t2.ModelElemID1 equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t6x in modelElemRepository.GetAll() on t2.ModelElemID2 equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                             //where t.IsActive
                         select new MOM_ModelElemPriceByUserGetAllOutputDto()
                         {
                             IsActive = t.IsActive,
                             CreateOn = t.CreateOn,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             Id = t.Id,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             UserName = t1.Name,
                             ModelElemID = t2.ModelElemID,
                             IsShow = t.IsShow,
                             ModelElemPriceID = t.ModelElemPriceID,
                             UserID = t.UserID,
                             ClientName = t4.ShortName,
                             ItemCode = t22.Code,
                             ItemOriginalItemNo = t22.OriginalItemNo,
                             ModelElemCode = t11.Code,
                             ModelElemCode1 = t5.Code,
                             ModelElemCode2 = t6.Code,
                             ModelElemListCode = t3.Code,
                             ModelElemListID = t11.ModelElemListID,
                             ModelElemListName = t3.CodeName,
                             ModelElemName = t11.CodeName,
                             ModelElemName1 = t5.CodeName,
                             SorderTypeText = t2.SorderType.HasValue ? t2.SorderType.Value.GetDescription() : null,
                             SorderType = t2.SorderType,
                             ModelElemName2 = t6.CodeName,
                             IsCommon = !t2.ClientID.HasValue,//没有绑定客户的就是通用的
                             ClientID = t2.ClientID,
                             Price = t2.Price,
                             //Code = t.Code,
                             //CodeName = t.CodeName,
                             //Sort = t.Sort,                         
                         }
                       )
                       .WhereIf(input.UserID.HasValue, a => a.UserID == input.UserID.Value)
                       .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
                       .WhereIf(input.SorderType.HasValue, a => a.SorderType == input.SorderType.Value)
                       .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                       .WhereIf(input.ModelElemID.HasValue, a => a.ModelElemID == input.ModelElemID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.UserName.Contains(input.Text) || a.ModelElemListCode.Contains(input.Text) || a.ModelElemListName.Contains(input.Text) || a.ModelElemCode.Contains(input.Text) || a.ModelElemName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.UserName).ThenBy(a => a.ModelElemCode).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemPriceByUserGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemPriceByUserGetAllOutputDto>> GetByElemPrice(MOM_ModelElemPriceByUserGetAllInputDto input)
        {
            //var PriceByUser = repository.GetAll().Where(a => a.ModelElemPriceID == input.ModelElemPriceID.Value).Select(a => new { a.ModelElemPriceID, a.UserID });
            //var query = (from t in modelElemPriceRepository.GetAll()
            //            join t1x in PriceByUser on t.Id equals t1x.ModelElemPriceID into t1xx
            //            from t1 in t1xx.DefaultIfEmpty()
            //            join t2x in userRepository.GetAll() on t1.UserID equals t2x.Id into t2xx
            //            from t2 in t2xx.DefaultIfEmpty()
            //            select new MOM_ModelElemPriceByUserGetAllOutputDto()
            //            {
            //                IsShow = t1 != null,
            //                IsActive = t1 != null,
            //                UserName=t2.Name,
            //                ModelElemPriceID= input.ModelElemPriceID.Value,
            //                UserID=t1.UserID,
            //            }).WhereIf(!string.IsNullOrEmpty(input.Text), a => a.UserName.Contains(input.Text));

            var query = (from t in userRepository.GetAll()
                         join t1x in repository.GetAll().Where(a => a.ModelElemPriceID == input.ModelElemPriceID.Value) on t.Id equals t1x.UserID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                             //where t1.ModelElemPriceID==input.ModelElemPriceID.Value
                         select new MOM_ModelElemPriceByUserGetAllOutputDto
                         {
                             IsShow = true,
                             IsActive = t1 != null,
                             UserName = t.Name,
                             ModelElemPriceID = input.ModelElemPriceID.Value,
                             UserID = t.Id,
                             CreateBy = t1.CreateBy,
                             CreateID = t1.CreateID,
                             CreateOn = t1.CreateOn,

                         }).WhereIf(!string.IsNullOrEmpty(input.Text), a => a.UserName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemPriceByUserGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemPriceByUserDto> input)
        {
            var userid = input.Select(a => a.UserID).Distinct().ToList();
            var prices = await repository.GetAll().Where(a => userid.Contains(a.UserID)).Select(a => new { a.UserID, a.ModelElemPriceID }).ToListAsync();
            foreach (var entity in input)
            {
                var price = prices.FirstOrDefault(a => a.UserID == entity.UserID && a.ModelElemPriceID == entity.ModelElemPriceID);
                if (price == null)
                {
                    var oldentity = ObjectMapper.Map<ModelElemPriceByUser>(entity);
                    oldentity.IsShow = true;
                    await repository.InsertAsync(oldentity);
                }

            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemPriceByUserDto> input)
        {
            var userid = input.Select(a => a.UserID).Distinct().ToList();
            var prices = await repository.GetAll().Where(a => userid.Contains(a.UserID)).Select(a => new { a.UserID, a.ModelElemPriceID }).ToListAsync();
            foreach (var entity in input)
            {
                var price = prices.FirstOrDefault(a => a.UserID == entity.UserID && a.ModelElemPriceID == entity.ModelElemPriceID);
                if (price == null)
                {
                    var oldentity = ObjectMapper.Map<ModelElemPriceByUser>(entity);
                    await repository.InsertAsync(oldentity);
                }

            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemPriceByUserDto> input)
        {
            var odls = input.Where(a => a.Id.HasValue && a.Id.Value != Guid.Empty).Select(a => a.Id.Value).Distinct().ToList();
            var odllist = await repository.GetAll().Where(a => odls.Contains(a.Id)).ToListAsync();
            await repository.GetDbContext().BulkDeleteAsync(odllist);

        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelElemPriceByUserDto input)
        {

            var any = await repository.GetAll().WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value).AnyAsync(a => a.UserID.Equals(input.UserID) && a.ModelElemPriceID == input.ModelElemPriceID);
            return any;

        }


    }
}

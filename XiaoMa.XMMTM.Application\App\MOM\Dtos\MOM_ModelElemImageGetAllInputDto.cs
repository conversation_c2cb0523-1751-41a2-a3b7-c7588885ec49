﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemImageGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:45
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemImageGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemImageGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid ? GroupID { set; get; }
        public SYS_ModelElemType? ModelElemTypeID { set; get; }
        public Guid ? ModelElemBaseID { set; get; }
        public Guid ? ModelElemListID { set; get; }
        public SYS_Position? PositionID { set; get; }
    }
}

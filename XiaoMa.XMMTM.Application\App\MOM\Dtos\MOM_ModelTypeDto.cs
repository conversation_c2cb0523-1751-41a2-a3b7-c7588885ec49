﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYS_ModelTypeDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/17/星期五 9:57:06
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.SYS.Dtos
{
    /// <summary>
    /// SYS_ModelTypeDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelTypeDto : EntityDto<Guid?>
    {
        public bool IsActive { set; get; }
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }

        /// <summary>
        /// 图片类型
        /// </summary>

        public Guid? ModelImageID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string ImageBac { get; set; }

        /// <summary>
        /// 版型类型 上衣/裤子/马甲
        /// </summary>
        public Guid GroupID { get; set; }
    }
}

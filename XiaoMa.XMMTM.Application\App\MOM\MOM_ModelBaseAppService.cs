﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelBaseAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/30/星期四 15:05:20
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelBaseAppService : XMMTMAppServiceBase, IMOM_ModelBaseAppService
    {
        private readonly IRepository<ModelBase, Guid> repository;
        private readonly IRepository<ModelType, Guid> modelTypeRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelBaseAppService(
       IRepository<ModelBase, Guid> repository,
        IRepository<ModelType, Guid> modelTypeRepository,
        IRepository<Model, Guid> modelRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelTypeRepository = modelTypeRepository;
            this.modelRepository = modelRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelBaseGetAllOutputDto>> Get(MOM_ModelBaseGetAllInputDto input)
        {
            var query = from t in repository.GetAll()
                        join t1x in modelTypeRepository.GetAll() on t.ModelTypeID equals t1x.Id into g
                        from t1 in g.DefaultIfEmpty()
                        orderby t1.CreateOn descending
                        select new MOM_ModelBaseGetAllOutputDto
                        {
                            ModelTypeID = t.ModelTypeID,
                            Code = t.Code,
                            CodeName = t.CodeName,
                            CreateBy = t.CreateBy,
                            CreateID = t.CreateID,
                            CreateOn = t.CreateOn,
                            Id = t.Id,
                            Image = t.Image,
                            IsActive = t.IsActive,
                            ModelType = t1.Code + ":" + t1.CodeName,
                            ModifyBy = t.ModifyBy,
                            ModifyID = t.ModifyID,
                            ModifyOn = t.ModifyOn,
                            Remark = t.Remark
                        };
            var sql = query
              .Where(a => a.IsActive)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text))
              .WhereIf(input.ModelTypeID.HasValue, a => a.ModelTypeID == input.ModelTypeID.Value);
            var count = await sql.CountAsync();
            var result = await sql.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelBaseGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelBaseGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelBase>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await modelRepository.GetAll().AnyAsync(a => a.ModelBaseID == entity.Id))
                {
                    throw new UserFriendlyException("此基础版型下有关联版型,请先删除关联版型");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelBaseDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

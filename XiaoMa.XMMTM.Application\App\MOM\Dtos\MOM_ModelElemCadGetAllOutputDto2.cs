﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemCadGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 21:28:05
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemCadGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemCadGetAllOutputDto2
    {
        public Guid? Id { set; get; }
        public bool Selected { set; get; }
        public bool? Default { set; get; }

        public Guid? CreateID { set; get; }

        public string CreateBy { set; get; }
        public DateTime? CreateOn { set; get; }

        public Guid? ModifyID { set; get; }
        public string ModifyBy { set; get; }
        public DateTime? ModifyOn { set; get; }

        public Guid CadRuleID { set; get; }

        public Guid ModelElemID { set; get; }

        public string Remark { set; get; }

        public string ModelElemCadCode { set; get; }
        public string ModelElemCadName { set; get; }
        public string CadRuleName { set; get; }
        public string CadRuleCode { set; get; }

        public Guid GroupID { set; get; }

        public string Code { set; get; }

        public string CodeName { set; get; }
    }
}

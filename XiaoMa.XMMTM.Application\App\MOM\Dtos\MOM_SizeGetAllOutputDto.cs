﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/3/星期一 16:54:56
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_SizeGetAllOutputDto : Size
    {
        public Guid? ModelId { set; get; }
        public string GroupName { set; get; }
        public string SizeListCode { set; get; }
        public string SizeListName { set; get; }

        public string SizeElemaCode { set; get; }
        public string SizeElembCode { set; get; }
        public string SizeElemcCode { set; get; }
        public string SizeElemdCode { set; get; }
        public int? Sequence1 { get; set; }
        public int? Sequence2 { get; set; }
        public int? Sequence3 { get; set; }
        public int? Sequence4 { get; set; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientShopAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/24/星期五 17:00:27
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientShopAppService : XMMTMAppServiceBase, IBAD_ClientShopAppService
    {
        private readonly IRepository<ClientShop, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientShopAppService(
       IRepository<ClientShop, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientShopGetAllOutputDto>> Get(BAD_ClientShopGetAllInputDto input)
        {
            var query = repository.GetAll()
              .Where(a => a.IsActive && a.ClientID == input.Id)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<BAD_ClientShopGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ClientShopGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ClientShopDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ClientShop>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientShopDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientShopDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ClientShopDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => input.ClientID == a.ClientID && a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => input.ClientID == a.ClientID && a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

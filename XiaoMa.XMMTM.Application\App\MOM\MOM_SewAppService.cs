/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SewAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 15:11:51
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SewAppService : XMMTMAppServiceBase, IMOM_SewAppService
    {
        private readonly IRepository<Sew, Guid> repository;
        private readonly IRepository<SizeElemc, Guid> sizeElemCrepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SewAppService(
       IRepository<Sew, Guid> repository,
       IRepository<SizeElemc, Guid> sizeElemCrepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sizeElemCrepository = sizeElemCrepository;
            this.objectMapper = objectMapper;
        }


        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SewGetAllOutputDto>> Get(MOM_SewGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1 in sizeElemCrepository.GetAll() on t.SizeElemcID equals t1.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         where t.SewListID == input.Id
                         select new MOM_SewGetAllOutputDto()
                         {
                             Id = t.Id,
                             SewListID = t.SewListID,
                             IsActive = t.IsActive,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             SizeElemcID = t.SizeElemcID,
                             SizeElemcText = t1 == null ? "" : t1.Code + ":" + t1.CodeName
                         })
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_SewGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SewDto> input)
        {
            foreach (var entity in input)
            {
                //if (await this.ExistCodeAsync(entity))
                //{
                //    throw new UserFriendlyException("已经存在相同的编码");
                //}
                var oldentity = ObjectMapper.Map<Sew>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SewDto> input)
        {
            foreach (var entity in input)
            {
                //if (await this.ExistCodeAsync(entity))
                //{
                //    throw new UserFriendlyException("已经存在相同的编码");
                //}
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SewDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SewDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

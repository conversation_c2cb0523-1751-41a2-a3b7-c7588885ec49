﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_WashingLabelModelElemListDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2022年10月10日,星期一 14:01:52
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_WashingLabelModelElemListDto新增/修改数据对象Dto
    /// </summary>
    [AutoMapTo(typeof(WashingLabelModelElemList))]
    public class BAD_WashingLabelModelElemListDto : EntityDto<Guid?>
    {
        public Guid ModelElemListID { set; get; }
        public Guid? ModelElemID1 { set; get; }
        public Guid? ModelElemID2 { set; get; }
        /// <summary>
        /// 位置信息
        /// </summary>
        public WashingLabelPositionEnums WashingLabelPosition { set; get; }
        /// <summary>
        /// 类型
        /// </summary>
        public WashingLabelPositionTypeEnums WashingLabelPositionType { set; get; }
        /// <summary>
        /// 显示类型
        /// </summary>
        public SuitsupplyModelElemListShowTypeEnums? ModelElemListShowType { set; get; }
        /// <summary>
        /// 其他位置
        /// </summary>
        public WashingLabelPositionEnums? OtherPosition { set; get; }
        /// <summary>
        /// 固定信息
        /// </summary>
        public string Info { set; get; }
        public int? Sort { set; get; }
        public string Remark { set; get; }
    }
}

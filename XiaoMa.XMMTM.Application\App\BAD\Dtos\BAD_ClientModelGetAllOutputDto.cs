﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/7/星期五 21:02:06
-----------------------------------------------*/

using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientModelGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ClientModelGetAllOutputDto
    {
        public Guid? ClassID { set; get; }
        public Guid ModelID { set; get; }
        public Guid ClientID { set; get; }
        public Guid? Id { set; get; }
        public string ModelName { set; get; }
        public string ModelCode { set; get; }
        public bool Selected { set; get; }
        public DateTime CreateOn { set; get; }

        public string GroupName { set; get; }
    }
}

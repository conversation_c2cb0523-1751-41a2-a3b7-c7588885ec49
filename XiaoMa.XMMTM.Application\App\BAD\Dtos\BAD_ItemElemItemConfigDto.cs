﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemConfigDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/12/28/星期一 10:20:01
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemElemItemConfigDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemElemItemConfigDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 款式明细
        /// </summary>
        public Guid ModelElemID { set; get; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int Seq { set; get; }
        public bool IsActive { set; get; }
        /// <summary>
        /// 物料类别
        /// </summary>
        public BAD_ItemClass ItemClassID { set; get; }
    }
}

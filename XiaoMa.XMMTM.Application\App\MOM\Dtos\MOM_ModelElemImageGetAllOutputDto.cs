﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemImageGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:51
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemImageGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemImageGetAllOutputDto : ModelElemImage
    {
        public string ModelElemCode1 { set; get; }
        public string ModelElemName1 { set; get; }
        public string ModelElemCode2 { set; get; }
        public string ModelElemName2 { set; get; }
        public string ModelImage { set; get; }
        public string ImagePath { set; get; }
        public string GroupText { set; get; }
        public string ImageUrl { set; get; }
        public string Position { set; get; }
        public string ModelElemTypeName { set; get; }
        public string ModelElemBaseCode { set; get; }
        public string ModelElemListName { set; get; }
        public string ModelElemListCode { set; get; }
        public string ModelElemListText { set; get; }
        public string ModelElemName { set; get; }
        public string ModelElemTypeText { set; get; }
        public string ModelElemCode { set; get; }
        public Guid ModelElemListID { set; get; }
        public Guid ModelElemListID1 { set; get; }
        public Guid ModelElemListID2 { set; get; }
        public Guid ModelElemBaseID { set; get; }
        public SYS_ModelElemType ModelElemTypeID { set; get; }
        public Guid? GroupID { set; get; }
        public string ModelElemBaseName { set; get; }
        public string ModelElemBaseText { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemImageGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2021/1/11/星期一 11:13:44
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemImageGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ItemImageGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid ItemID { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelClassAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/3/2/星期二 14:40:44
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientModelClassAppService : XMMTMAppServiceBase, IBAD_ClientModelClassAppService
    {
        private readonly IRepository<ClientModelClass, Guid> repository;
        private readonly IRepository<Class, Guid> classRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<ClientModel, Guid> clientModelRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientModelClassAppService(
       IRepository<ClientModelClass, Guid> repository,
       IRepository<Class, Guid> classRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<ClientModel, Guid> clientModelRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.classRepository = classRepository;
            this.modelRepository = modelRepository;
            this.clientRepository = clientRepository;
            this.clientModelRepository = clientModelRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientModelClassGetAllOutputDto>> Get(BAD_ClientModelClassGetAllInputDto input)
        {
            var clientmodels = repository.GetAll().WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value);
            var query = (from t in classRepository.GetAll().Where(a => a.ClassType == ClassTypeEnums.ModelGroup)
                         join t1x in clientmodels on t.Id equals t1x.ModelClassID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                     
                         select new BAD_ClientModelClassGetAllOutputDto
                         {
                             ClientID = input.ClientID.Value,
                             Id = t1.Id,
                             ModelClassID = t.Id,
                             IsChecked = t1 != null ? true : false,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t1.CreateBy,
                             CreateID = t1.CreateID,
                             CreateOn = t1.CreateOn,
                             IsActive = t1.IsActive,
                             ModifyBy = t1.ModifyBy,
                             ModifyID = t1.ModifyID,
                             ModifyOn = t1.ModifyOn,

                         })
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.Code.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.CodeName.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await query.CountAsync();
            var result =  query.OrderByDescending(a => a.IsChecked).PageBy(input).ToList();
            var modelGroupID = result.Select(a => a.ModelClassID).Distinct().ToList();
            var modelCountQuery = await modelRepository.GetAll().Where(a=>a.IsActive).Where(a => a.ModelGroupID.HasValue && modelGroupID.Contains(a.ModelGroupID.Value)).GroupBy(a => a.ModelGroupID).Select(a => new { ModelGroupID = a.Key, ModelCount = a.Count() }).ToListAsync();
            foreach (var item in result)
            {
                var modelCount = modelCountQuery.FirstOrDefault(a => a.ModelGroupID == item.ModelClassID);
                if (modelCount != null)
                {
                    item.ModelCount = modelCount.ModelCount;
                }
            }
            return new PagedResultDto<BAD_ClientModelClassGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientModelClassDto> input)
        {
            foreach (var entity in input)
            {
                using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
                {

                    if (entity.Id.HasValue && entity.Id != Guid.Empty)
                    {
                        var dels = await (from t in clientModelRepository.GetAll()
                                          join t1 in modelRepository.GetAll() on t.ModelID equals t1.Id
                                          where t.ClientID == entity.ClientID && t1.ModelGroupID == entity.ModelClassID
                                          select t).ToListAsync();
                        //var odlClientModels = await clientModelRepository.GetAll().Where(a => a.ClientID == entity.ClientID && a.).ToListAsync();
                        if (dels.Any())
                        {
                            await clientModelRepository.GetDbContext().BulkDeleteAsync(dels);
                        }
                    }

                    if (entity.IsChecked)
                    {

                        var clientmModels = await modelRepository.GetAll().Where(a => a.ModelGroupID == entity.ModelClassID).Select(a => new ClientModel() { Id = Guid.NewGuid(), ClientID = entity.ClientID, IsActive = true, ModelID = a.Id }).ToListAsync();
                        if (clientmModels.Any())
                        {
                            await clientModelRepository.GetDbContext().BulkInsertAsync(clientmModels);
                        }
                        //foreach (var modelId in modelIDs)
                        //{
                        //    await clientModelRepository.InsertAsync(new ClientModel() { ClientID = entity.ClientID, IsActive = true, ModelID = modelId });
                        //}
                        if (entity.Id.HasValue && entity.Id != Guid.Empty)
                        {
                            var oldentity = repository.Get(entity.Id.Value);
                            ObjectMapper.Map(entity, oldentity);
                            await repository.UpdateAsync(oldentity);
                        }
                        else
                        {
                            var ent = ObjectMapper.Map<ClientModelClass>(entity);
                            await repository.InsertAsync(ent);
                        }
                    }
                    else
                    {
                        if (entity.Id.HasValue && entity.Id != Guid.Empty)
                        {
                            var modelIDs = await modelRepository.GetAll().Where(a => a.ModelGroupID == entity.ModelClassID).Select(a => a.Id).ToListAsync();
                            var clientModels = await clientModelRepository.GetAll().Where(a => a.ClientID == entity.ClientID && modelIDs.Contains(a.ModelID)).Select(a => a).ToListAsync();
                            await clientModelRepository.GetDbContext().BulkDeleteAsync(clientModels);
                            await repository.DeleteAsync(entity.Id.Value);
                        }
                    }

                    await unit.CompleteAsync();
                }

                //版型继承
                await InheritedParentModel(entity.ClientID);

            }
            var clientids = input.Select(a => a.ClientID).Distinct().ToList();
            //修改
            await UpdateByQuery(new UpdatePlusInputDto() { ClientIDs = clientids });
        }
        /// <summary>
        /// MES中的定时任务
        /// 根据客户绑定的版型系列，维护客户绑定的版型
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task UpdatePlus()
        {
            await this.UpdateByQuery();
        }

        private async Task UpdateByQuery(UpdatePlusInputDto input = null)
        {
            var modelquerys = await (from t in modelRepository.GetAll()
                                         //group t by t.ModelGroupID.Value into gg
                                     where t.IsActive && t.ModelGroupID.HasValue
                                     select new
                                     {
                                         t.Id,
                                         t.ModelGroupID
                                     }).ToListAsync();
            var models = (from t in modelquerys
                          group t by t.ModelGroupID into gg
                          select new
                          {
                              ModelGroupID = gg.Key,
                              ModelCount = gg.Count(),
                              ModelIDs = gg.Select(a => a.Id).ToList()
                          }).ToList();



            var query = await (from t in clientModelRepository.GetAll()
                               join t1x in modelRepository.GetAll() on t.ModelID equals t1x.Id into t1xx
                               from t1 in t1xx.DefaultIfEmpty()
                               where t1.ModelGroupID.HasValue
                               select new
                               {
                                   t.ModelID,
                                   t.ClientID,
                                   t1.ModelGroupID,
                               }).WhereIf(input != null && input.ClientIDs.Any(), a => input.ClientIDs.Contains(a.ClientID)).ToListAsync();
            var clinetmodelgroups = (from t in query
                                     group t by t.ClientID into gg
                                     select new
                                     {
                                         ClientID = gg.Key,
                                         ModelGroupIDs = gg.GroupBy(a => a.ModelGroupID).Select(a => new { ModelGroupID = a.Key, ModelCount = a.Count(), ModelIDs = a.Select(a => a.ModelID).Distinct().ToList() }).ToList(),
                                     }).ToList();
            var deleteModelIds = new List<Guid>();
            var insertClientModels = new List<ClientModel>();
            foreach (var clinetGroups in clinetmodelgroups)
            {
                foreach (var modelgroup in clinetGroups.ModelGroupIDs)
                {
                    var clientmodelclass = await repository.FirstOrDefaultAsync(a => a.ClientID == clinetGroups.ClientID && a.ModelClassID == modelgroup.ModelGroupID);
                    if (clientmodelclass == null)
                    {
                        continue;
                    }
                    var group = models.FirstOrDefault(a => a.ModelGroupID == modelgroup.ModelGroupID);
                    if (group == null || group.ModelCount == modelgroup.ModelCount)
                    {
                        continue;
                    }
                    //需要添加的
                    if (group.ModelCount > modelgroup.ModelCount)
                    {
                        var clientmodes = group.ModelIDs.Except(modelgroup.ModelIDs).Select(a => new ClientModel() { IsActive = true, ClientID = clinetGroups.ClientID, ModelID = a, Id = Guid.NewGuid() }).ToList();
                        insertClientModels.AddRange(clientmodes);
                    }
                    //需要删除的多余的（不活动版型以及版型被删除了的）
                    if (modelgroup.ModelCount > group.ModelCount)
                    {
                        var modelids = modelgroup.ModelIDs.Except(group.ModelIDs).ToList();
                        deleteModelIds.AddRange(modelids);
                    }

                }
            }
            //添加数据
            if (insertClientModels.Any())
            {
                await clientModelRepository.GetDbContext().BulkInsertAsync(insertClientModels);
            }

            //删除多余
            deleteModelIds = deleteModelIds.Distinct().ToList();
            if (deleteModelIds.Count > 0)
            {
                var deleteclientmodels = await clientModelRepository.GetAll().Where(a => deleteModelIds.Contains(a.ModelID)).Select(a => a).ToListAsync();
                await clientModelRepository.GetDbContext().BulkDeleteAsync(deleteclientmodels);
            }
            if (input != null)
            {
                await UpdatePlus1(input.ClientIDs);
            }
            else
            {
                await UpdatePlus1();
            }

        }
        /// <summary>
        /// MES中的定时任务
        /// 根据客户绑定的版型系列，维护客户绑定的版型
        /// </summary>
        /// <returns></returns>
        private async Task UpdatePlus1(List<Guid> ClientIDs = null)
        {
            var clientModelClass = await (from t in repository.GetAll()
                                          where t.IsActive
                                          select new
                                          {
                                              t.ClientID,
                                              t.ModelClassID,
                                          }).WhereIf(ClientIDs != null && ClientIDs.Any(), a => ClientIDs.Contains(a.ClientID)).ToListAsync();
            var modelgroupids = clientModelClass.Select(a => a.ModelClassID).Distinct().ToList();
            var modelquerys = await (from t in modelRepository.GetAll()
                                     where t.IsActive && t.ModelGroupID.HasValue && modelgroupids.Contains(t.ModelGroupID.Value)
                                     select new
                                     {
                                         ModelID = t.Id,
                                         ModelGroupID = t.ModelGroupID.Value
                                     }).ToListAsync();
            var insertClientModel = new List<ClientModel>();
            var deleteClientModel = new List<ClientModel>();
            var clientModelClassGroupClientId = clientModelClass.GroupBy(a => a.ClientID).Select(a => new { ClientID = a.Key, ModelGroypIDs = a.Select(b => b.ModelClassID).Distinct().ToList() }).ToList();
            foreach (var client in clientModelClassGroupClientId)
            {
                var insertModelIds = new List<Guid>();
                var deleteModelIds = new List<Guid>();
                var clientModels = await (from t in clientModelRepository.GetAll()
                                          join t1x in modelRepository.GetAll() on t.ModelID equals t1x.Id into t1xx
                                          from t1 in t1xx.DefaultIfEmpty()
                                          where t.ClientID == client.ClientID
                                          select new
                                          {
                                              t.Id,
                                              t.ModelID,
                                              t1.ModelGroupID,
                                          }).ToListAsync();
                //删除的
                var deleteClientModelID = clientModels.Where(a => a.ModelGroupID == null).Select(a => a.ModelID).Distinct().ToList();

                //根据版型系列判断
                var clientModelGroups = clientModels.Where(a => a.ModelGroupID != null).Select(a => a.ModelGroupID.Value).Distinct().ToList();
                var insertModelGroups = client.ModelGroypIDs.Except(clientModelGroups).ToList();
                if (insertModelGroups.Any())
                {
                    var allmodelids = modelquerys.Where(a => insertModelGroups.Contains(a.ModelGroupID)).Select(a => a.ModelID).Distinct().ToList();
                    var clientmodelids = clientModels.Where(a => a.ModelGroupID.HasValue && insertModelGroups.Contains(a.ModelGroupID.Value)).Select(a => a.ModelID).ToList();
                    var insertModels = allmodelids.Except(clientmodelids).ToList();
                    insertModelIds.AddRange(insertModels);
                    var deleteModes = clientmodelids.Except(allmodelids).ToList();
                    deleteModelIds.AddRange(deleteModes);
                }

                var deleteModelGroups = clientModelGroups.Except(client.ModelGroypIDs);
                if (deleteModelGroups.Any())
                {
                    var allmodelids = modelquerys.Where(a => deleteModelGroups.Contains(a.ModelGroupID)).Select(a => a.ModelID).Distinct().ToList();
                    var deleteModes = clientModels.Where(a => allmodelids.Contains(a.ModelID)).Select(a => a.ModelID).ToList();
                    deleteModelIds.AddRange(deleteModes);
                }
                if (insertModelIds.Any())
                {
                    var clientmodels = from t in insertModelIds
                                       select new ClientModel
                                       {
                                           ModelID = t,
                                           ClientID = client.ClientID,
                                           IsActive = true,
                                           Id = Guid.NewGuid(),
                                       };
                    insertClientModel.AddRange(clientmodels);
                }
                if (deleteModelIds.Any())
                {
                    var deleteclientmodels = await (from t in clientModelRepository.GetAll()
                                                    where t.ClientID == client.ClientID
                                                    where deleteModelIds.Contains(t.ModelID)
                                                    select t).ToListAsync();
                    deleteClientModel.AddRange(deleteclientmodels);
                }
            }

            //添加数据
            if (insertClientModel.Any())
            {
                await clientModelRepository.GetDbContext().BulkInsertAsync(insertClientModel);
            }
            //添加数据
            if (deleteClientModel.Any())
            {
                await clientModelRepository.GetDbContext().BulkDeleteAsync(deleteClientModel);
            }
            await ClearPlus();
        }

        /// <summary>
        /// 清除掉失效的客户版型关系
        /// </summary>
        /// <returns></returns>
        private async Task ClearErrorModelClient()
        {
            //失效的版型
            var del = await (from t in clientModelRepository.GetAll()
                             join t1x in modelRepository.GetAll() on t.ModelID equals t1x.Id into t1xx
                             from t1 in t1xx.DefaultIfEmpty()
                             where t1 == null
                             select t).ToListAsync();
            await clientModelRepository.GetDbContext().BulkDeleteAsync(del);
            //没有关联版型系列的
            var dels = await (from t in clientModelRepository.GetAll()
                              join t1x in repository.GetAll() on t.ClientID equals t1x.ClientID into t1xx
                              from t1 in t1xx.DefaultIfEmpty()
                              where t1 == null
                              select t).ToListAsync();
            await clientModelRepository.GetDbContext().BulkDeleteAsync(dels);
        }

        /// <summary>
        /// 清除 没有关联版型系列
        /// 却绑定有关联版型的 客户和版型数据（clientmodel）
        /// </summary>
        /// <returns></returns>
        public async Task ClearPlus()
        {
            var modelquerys = await (from t in modelRepository.GetAll()
                                         //group t by t.ModelGroupID.Value into gg
                                     where t.IsActive && t.ModelGroupID.HasValue
                                     select new
                                     {
                                         t.Id,
                                         ModelGroupID = t.ModelGroupID.Value
                                     }).ToListAsync();
            var models = (from t in modelquerys
                          group t by t.ModelGroupID into gg
                          select new
                          {
                              ModelGroupID = gg.Key,
                              ModelCount = gg.Count(),
                              ModelIDs = gg.Select(a => a.Id).ToList()
                          }).ToList();
            var clientmodelquery = await (from t in repository.GetAll()
                                          select new
                                          {
                                              t.ClientID,
                                              t.ModelClassID
                                          }).ToListAsync();

            var clientModelGroups = (from t in clientmodelquery
                                     group t by t.ClientID into gg
                                     select new
                                     {
                                         ClientID = gg.Key,
                                         ModelGroupIDs = gg
                                     }).ToList();
            var list = new List<ClientModel>();

            foreach (var item in clientModelGroups)
            {
                var modelgroupids = item.ModelGroupIDs.Select(a => a.ModelClassID).Distinct().ToList();
                var expModelIDs = models.Where(a => !modelgroupids.Contains(a.ModelGroupID)).SelectMany(a => a.ModelIDs).ToList();
                var clientmodels = await (clientModelRepository.GetAll().Where(a => a.ClientID == item.ClientID && expModelIDs.Contains(a.ModelID))).ToListAsync();
                list.AddRange(clientmodels);
            }
            await clientModelRepository.GetDbContext().BulkDeleteAsync(list);

            await ClearErrorModelClient();
        }

        /// <summary>
        /// 根据客户信息变动
        /// 子店铺继承总店所有版型
        /// </summary>
        /// <param name="ClientID">客户ID</param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task InheritedParentModel(Guid ClientID)
        {
            using (var unit = UnitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
            {
                var _clientDto = await clientRepository.FirstOrDefaultAsync(ClientID);

                var insertModelGroups = new List<ClientModelClass>();
                var deletemodelgroups = new List<ClientModelClass>();

                if (_clientDto.ParentClientID.HasValue)
                {
                    //--子店铺 

                    //是否继承总店版型
                    if (!_clientDto.InheritedParentModel.HasValue || !_clientDto.InheritedParentModel.Value)
                    {
                        return;
                    }

                    //总店版型系列
                    var pmodelgroupids = await repository.GetAll().Where(a => a.ClientID == _clientDto.ParentClientID.Value && a.IsActive).Select(a => a.ModelClassID).Distinct().ToListAsync();
                    //子店铺版型系列
                    var cmodelgroup = await repository.GetAll().Where(a => a.ClientID == _clientDto.Id).Select(a => a).ToListAsync();
                    var cmodelgroupids = cmodelgroup.Select(a => a.ModelClassID).Distinct().ToList();
                    //新增
                    var _iList = pmodelgroupids.Except(cmodelgroupids).Select(a => new ClientModelClass()
                    {
                        Id = Guid.NewGuid(),
                        ClientID = _clientDto.Id,
                        CreateBy = this.SSOSession.Name,
                        CreateID = this.SSOSession.UserId,
                        CreateOn = DateTime.Now,
                        IsActive = true,
                        ModelClassID = a,
                    });
                    insertModelGroups.AddRange(_iList);
                    //删除
                    var delModelGroupIds = cmodelgroupids.Except(pmodelgroupids);
                    var delModelGroups = cmodelgroup.Where(a => delModelGroupIds.Contains(a.ModelClassID)).ToList();
                    deletemodelgroups.AddRange(delModelGroups);
                }
                else
                {
                    //总店

                    //所有继承总店版型的子店铺的id
                    var ids = await clientRepository.GetAll().Where(a => a.ParentClientID == ClientID && a.InheritedParentModel.HasValue && a.InheritedParentModel.Value).Select(a => a.Id).ToListAsync();
                    if (!ids.Any())
                    {
                        return;
                    }
                    //所有子店铺的版型系列
                    var cmodelgroups = await repository.GetAll().Where(a => ids.Contains(a.ClientID)).Select(a => a).ToListAsync();
                    //总店的版型系列
                    var pmodelgroupids = await repository.GetAll().Where(a => a.ClientID == _clientDto.Id && a.IsActive).Select(a => a.ModelClassID).Distinct().ToListAsync();
                    foreach (var id in ids)
                    {
                        var cmodelgroup = cmodelgroups.Where(a => a.ClientID == id).ToList();
                        var cmodelgroupids = cmodelgroup.Select(a => a.ModelClassID).Distinct().ToList();
                        //新增
                        //新增
                        var _iList = pmodelgroupids.Except(cmodelgroupids).Select(a => new ClientModelClass()
                        {
                            Id = Guid.NewGuid(),
                            ClientID = id,
                            CreateBy = this.SSOSession.Name,
                            CreateID = this.SSOSession.UserId,
                            CreateOn = DateTime.Now,
                            IsActive = true,
                            ModelClassID = a,
                        });
                        insertModelGroups.AddRange(_iList);
                        //删除
                        var delModelGroupIds = cmodelgroupids.Except(pmodelgroupids);
                        var delModelGroups = cmodelgroup.Where(a => delModelGroupIds.Contains(a.ModelClassID)).ToList();
                        deletemodelgroups.AddRange(delModelGroups);
                    }

                }
                await repository.GetDbContext().BulkDeleteAsync(deletemodelgroups);
                await repository.GetDbContext().BulkInsertAsync(insertModelGroups);
                await unit.CompleteAsync();
            }

            await this.UpdatePlus();
        }
    }
}

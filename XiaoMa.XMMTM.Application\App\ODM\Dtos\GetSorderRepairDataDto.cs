﻿using Abp.AutoMapper;
using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.ODM;

namespace XiaoMa.XMMTM.App.ODM.Dtos
{
    [AutoMap(typeof(SorderRepair))]
    public class GetSorderRepairDataDto : SorderRepair
    {
        public GetSorderRepairDataDto()
        {
            this.SorderRepairModels = new List<GetSorderRepairModelDataDto>();
        }
        public string ClientName { set; get; }
        public string ClientPersonName { set; get; }
        public string ItemText { set; get; }
        public string CheckName { set; get; }
        public List<GetSorderRepairModelDataDto> SorderRepairModels { set; get; }
    }
    [AutoMap(typeof(SorderRepairModel))]
    public class GetSorderRepairModelDataDto : SorderRepairModel
    {
        public GetSorderRepairModelDataDto()
        {
            this.SorderRepairSizes = new List<GetSorderRepairSizeDataDto>();
            this.SorderRepairElems = new List<GetSorderRepairElemDataDto>();
            this.SorderRepairBodys = new List<GetSorderRepairBodyDataDto>();
            this.SorderRepairImages = new List<GetSorderRepairImageDataDto>();
            this.SorderRepairPartPrices = new List<GetSorderRepairPartPriceDataDto>();
        }

        public string ModelCode { set; get; }
        public string ModelName { set; get; }
        public string GroupText { set; get; }
        public List<GetSorderRepairSizeDataDto> SorderRepairSizes { set; get; }
        public List<GetSorderRepairElemDataDto> SorderRepairElems { set; get; }
        public List<GetSorderRepairBodyDataDto> SorderRepairBodys { set; get; }
        public List<GetSorderRepairImageDataDto> SorderRepairImages { set; get; }
        public List<GetSorderRepairPartPriceDataDto> SorderRepairPartPrices{ set; get; }
    }
    [AutoMap(typeof(SorderRepairSize))]
    public class GetSorderRepairSizeDataDto : SorderRepairSize
    {
        public string SizeColumnName { set; get; }
        /// <summary>
        /// 返修选项
        /// </summary>
        public bool IsRepair { set; get; }

        /// <summary>
        /// 标准规格现在最小值
        /// </summary>

        public decimal? RepairMin { get; set; }

        /// <summary>
        /// 标准规格限制最大值
        /// </summary>

        public decimal? RepairMax { get; set; }
        public decimal? FinishValue { get; set; }

 
    }
    [AutoMap(typeof(SorderRepairElem))]
    public class GetSorderRepairElemDataDto : SorderRepairElem
    {
        public GetSorderRepairElemDataDto()
        {
            this.ModelElem = new List<GetSorderRepairElemDetailDataDto>();
        }
        public new Guid? ModelElemID { set; get; }
        public Guid ModelElemBaseID { set; get; }
        public string ModelElemBaseCode { set; get; }
        public string ModelElemBaseName { set; get; }
        /// <summary>
        /// 款式类别
        /// </summary>

        public SystemConfig.SYS_ModelElemType ModelElemTypeID { get; set; }

        public string ModelElemListCode { set; get; }
        public string ModelElemListName { set; get; }

        //public string ModelElemCode { set; get; }
        //public string ModelElemCodeName { set; get; }

        public bool ?IsInput { set; get; }
        public bool ?IsInputItem { set; get; }
        public bool ?IsItemAdd { set; get; }

        public List<GetSorderRepairElemDetailDataDto> ModelElem { set; get; }
    }

    public class GetSorderRepairElemDetailDataDto
    {
        public Guid ModelElemListID { set; get; }
        public Guid ModelElemID { set; get; }
        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public Guid? ItemID { set; get; }
        public decimal? Qty { set; get; }
        //public string Input { set; get; }
        public bool IsInput { set; get; }
        public bool IsInputItem { set; get; }
        public bool IsItemAdd { set; get; }
        public string ImagePath { set; get; }
    }
    [AutoMap(typeof(SorderRepairBody))]
    public class GetSorderRepairBodyDataDto : SorderRepairBody
    {
        public GetSorderRepairBodyDataDto()
        {
            this.Bodys = new List<GetSorderRepairBodyDetailsDataDto>();
        }
        public new Guid? Id { set; get; }
        public new Guid? BodyID { set; get; }
        public string BodyListCode { set; get; }
        public string BodyListName { set; get; }
        public decimal? Max { set; get; }
        public decimal? Min { set; get; }
        public decimal? AllowMin { set; get; }
        public decimal? AllowMax { set; get; }
        public List<GetSorderRepairBodyDetailsDataDto> Bodys { set; get; }
    }

    public class GetSorderRepairBodyDetailsDataDto : Body
    {
        public string ImagePath { set; get; }
    }

    public class GetSorderRepairImageDataDto : SorderRepairImage
    {

    }
    [AutoMap(typeof(SorderRepairPartPrice))]
    public class GetSorderRepairPartPriceDataDto : SorderRepairPartPrice
    {
        public string PartPriceCode { set; get; }
        public string PartPriceName { set; get; }
        public bool IsDefault { set; get; }
        public bool Checked { set; get; }
    }
}

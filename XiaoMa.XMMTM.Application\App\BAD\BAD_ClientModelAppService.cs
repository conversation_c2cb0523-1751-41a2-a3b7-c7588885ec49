﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/24/星期五 17:00:49
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientModelAppService : XMMTMAppServiceBase, IBAD_ClientModelAppService
    {
        private readonly IRepository<ClientModel, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientModelAppService(
       IRepository<ClientModel, Guid> repository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientModelGetAllOutputDto>> Get(BAD_ClientModelGetAllInputDto input)
        {
            var query = from t in modelRepository.GetAll()
                        join t1x in repository.GetAll().Where(a => a.ClientID == input.Id) on t.Id equals t1x.ModelID into t1xx
                        from t1 in t1xx.DefaultIfEmpty()
                        join t2 in groupRepository.GetAll() on t.GroupID equals t2.Id
                        orderby t.Code
                        where t.BusinessSubType==SystemConfig.ModelBusinessSubType.CommonModel
                        select new BAD_ClientModelGetAllOutputDto()
                        {
                            ClassID = t.ModelGroupID,
                            ModelID = t.Id,
                            Id = t1 == null ? Guid.Empty : t1.Id,
                            ClientID = input.Id,
                            ModelName = t.CodeName,
                            ModelCode = t.Code,
                            Selected = t1 == null ? false : true,
                            CreateOn = t.CreateOn,
                            GroupName = t2.CodeName,
                        };
            query = query.WhereIf(input.ClassID.HasValue, a => a.ClassID == input.ClassID)
                .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelCode.Trim().ToLower().Contains(input.Text.Trim().ToLower()) || a.ModelName.Trim().ToLower().Contains(input.Text.Trim().ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a=>a.Selected).ThenBy(a=>a.GroupName).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<BAD_ClientModelGetAllOutputDto>(count, result);

        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ClientModelDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ClientModel>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientModelDto> input)
        {
            var selected = input.Where(a => a.Selected).ToList();
            var deleted = input.Where(a => !a.Selected).ToList();
            foreach (var entity in selected)
            {
                var oldentity = ObjectMapper.Map<ClientModel>(entity);
                await repository.InsertAsync(oldentity);
            }
            foreach (var entity in deleted)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientModelDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ClientModelDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.ModelID.Equals(input.ModelID)&&a.ClientID.Equals(input.ClientID));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.ModelID.Equals(input.ModelID) && a.ClientID.Equals(input.ClientID) && a.Id != input.Id.Value);
            }

        }
    }
}

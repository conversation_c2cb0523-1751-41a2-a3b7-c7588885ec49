﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemSeriesItemAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 19:46:41
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemSeriesItemAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemSeriesItemGetAllOutputDto>> Get(BAD_ItemSeriesItemGetAllInputDto input);
        Task Adds(List<BAD_ItemSeriesItemDto> input);
        Task Updates(List<BAD_ItemSeriesItemDto> input);
        Task Deletes(List<BAD_ItemSeriesItemDto> input);
    }
}

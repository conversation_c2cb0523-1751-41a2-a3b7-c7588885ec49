/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemPriceDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2021/6/4/星期五 10:42:10
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemPriceDto新增/修改数据对象Dto
    /// </summary>
    [AutoMapTo(typeof(ModelElemPrice))]
    public class MOM_ModelElemPriceDto : EntityDto<Guid?>
    {
        public Guid ModelElemID { set; get; }

        public Guid? ItemID { set; get; }

        /// <summary>
        /// 使用客户折扣
        /// </summary>
        public bool UseClinetDiscount { set; get; }
        /// <summary>
        ///
        /// </summary>

        public decimal Price { get; set; }

        /// <summary>
        /// 返修价格
        /// </summary>
        public decimal? RepairPrice { set; get; }
        public Guid? ClientID { set; get; }
        public Guid? ModelElemID1 { set; get; }
        public Guid? ModelElemID2 { set; get; }
        /// <summary>
        /// 是否套装
        /// </summary>
        public bool? IsSuit { set; get; } = false;
        /// <summary>
        /// 半成品试衣
        /// </summary>
        public bool? HalfFitting { get; set; } = false;
        /// <summary>
        /// 加急
        /// </summary>
        public bool? IsUrgent { get; set; } = false;

        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }
        public bool IsActive { set; get; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public ODM_SorderType? SorderType { set; get; }

        /// <summary>
        /// 最大数量
        /// </summary>
        public int? MaxQty { set; get; }
        /// <summary>
        /// 最小数量
        /// </summary>
        public int? MinQty { set; get; }
    }
}

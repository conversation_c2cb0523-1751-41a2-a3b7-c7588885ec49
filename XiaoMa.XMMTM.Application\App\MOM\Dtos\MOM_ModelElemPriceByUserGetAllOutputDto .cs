#region << 版 本 注 释 >>
/*----------------------------------------------------------------
* 项目名称 ：XiaoMa.XMMTM.App.MOM
* 类 名 称 ：MOM_ModelElemPriceByUserGetAllOutputDto
* 类 描 述 ：
* 命名空间 ：XiaoMa.XMMTM.App.MOM
* 作    者 ：SundayPC
* 创建时间 ：2023/9/25 15:24:46
* 版 本 号 ：v1.0.0.0
*******************************************************************
* Copyright @ SundayPC 2023. All rights reserved.
*******************************************************************
//----------------------------------------------------------------*/
#endregion
using System;
using XiaoMa.XMMTM.Domain.MOM;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// MOM_ModelElemPriceByUserGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemPriceByUserGetAllOutputDto : ModelElemPriceByUser
    {
        public string UserName { set; get; }
        public Guid ? ClientID { set; get; }
        public Guid ? ModelElemID { set; get; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public ODM_SorderType? SorderType { set; get; }
        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public string ItemCode { set; get; }
        public string ItemOriginalItemNo { set; get; }

        public string ModelElemListCode { set; get; }
        public string ModelElemListName { set; get; }
        public Guid ModelElemListID { set; get; }

        public string ClientName { set; get; }

        public bool IsCommon { set; get; }
        public string ModelElemCode1 { set; get; }
        public string ModelElemName1 { set; get; }
        public string ModelElemCode2 { set; get; }
        public string ModelElemName2 { set; get; }
        public string SorderTypeText { set; get; }
        public decimal? Price { set; get; }
    }
}

# Generate Batch TodoList for Core Module Files
param(
    [int]$BatchSize = 50,
    [string]$OutputFile = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\03_DetailedComparisons\Core_BatchTodoList.md"
)

Write-Host "Generating batch TodoList for Core module files..." -ForegroundColor Green

# Load comparison results
$comparisonFile = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
$comparisonData = Get-Content $comparisonFile | ConvertFrom-Json
$bothExistFiles = $comparisonData.Files | Where-Object { $_.Status -eq "Both Exist" }

Write-Host "Processing $($bothExistFiles.Count) files in batches of $BatchSize..." -ForegroundColor Cyan

# Function to analyze file quickly
function Get-QuickFileAnalysis {
    param($SourceFile, $TargetFile, $RelativePath)
    
    try {
        $sourceContent = Get-Content $SourceFile -Raw -Encoding UTF8
        $targetContent = Get-Content $TargetFile -Raw -Encoding UTF8
        
        # Quick analysis
        $sourceLines = ($sourceContent -split "`n").Count
        $targetLines = ($targetContent -split "`n").Count
        
        # Check if identical except namespace
        $normalizedSource = $sourceContent -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"
        $isIdenticalExceptNamespace = ($normalizedSource -eq $targetContent)
        
        # Get file size
        $sourceSize = (Get-Item $SourceFile).Length
        $targetSize = (Get-Item $TargetFile).Length
        
        # Determine priority based on file path and name
        $priority = "Medium"
        if ($RelativePath -match "(Module|Config|Const|Permission|Authorization)" -or 
            $RelativePath -match "^[^\\]*\.cs$") {  # Root level files
            $priority = "High"
        } elseif ($RelativePath -match "(Test|Demo|Sample)") {
            $priority = "Low"
        }
        
        # Determine risk level
        $riskLevel = if ($isIdenticalExceptNamespace) { "Low" } 
                    elseif ([Math]::Abs($sourceLines - $targetLines) -le 5) { "Medium" }
                    else { "High" }
        
        return [PSCustomObject]@{
            RelativePath = $RelativePath
            SourceLines = $sourceLines
            TargetLines = $targetLines
            SourceSize = $sourceSize
            TargetSize = $targetSize
            IsIdenticalExceptNamespace = $isIdenticalExceptNamespace
            Priority = $priority
            RiskLevel = $riskLevel
            LineDifference = [Math]::Abs($sourceLines - $targetLines)
        }
    } catch {
        return [PSCustomObject]@{
            RelativePath = $RelativePath
            Error = $_.Exception.Message
            Priority = "Unknown"
            RiskLevel = "Unknown"
        }
    }
}

# Process files in batches
$processedFiles = @()
$currentBatch = 0

for ($i = 0; $i -lt $bothExistFiles.Count; $i += $BatchSize) {
    $currentBatch++
    $batchFiles = $bothExistFiles[$i..([Math]::Min($i + $BatchSize - 1, $bothExistFiles.Count - 1))]
    
    Write-Host "Processing batch $currentBatch (files $($i+1) to $($i + $batchFiles.Count))..." -ForegroundColor Yellow
    
    foreach ($file in $batchFiles) {
        $analysis = Get-QuickFileAnalysis -SourceFile $file.SourcePath -TargetFile $file.TargetPath -RelativePath $file.RelativePath
        $processedFiles += $analysis
        
        if ($processedFiles.Count % 10 -eq 0) {
            Write-Host "  Processed $($processedFiles.Count) files..." -ForegroundColor White
        }
    }
}

# Generate statistics
$stats = @{
    Total = $processedFiles.Count
    IdenticalExceptNamespace = ($processedFiles | Where-Object { $_.IsIdenticalExceptNamespace -eq $true }).Count
    HighPriority = ($processedFiles | Where-Object { $_.Priority -eq "High" }).Count
    MediumPriority = ($processedFiles | Where-Object { $_.Priority -eq "Medium" }).Count
    LowPriority = ($processedFiles | Where-Object { $_.Priority -eq "Low" }).Count
    HighRisk = ($processedFiles | Where-Object { $_.RiskLevel -eq "High" }).Count
    MediumRisk = ($processedFiles | Where-Object { $_.RiskLevel -eq "Medium" }).Count
    LowRisk = ($processedFiles | Where-Object { $_.RiskLevel -eq "Low" }).Count
}

Write-Host ""
Write-Host "=== Analysis Complete ===" -ForegroundColor Green
Write-Host "Total files: $($stats.Total)" -ForegroundColor Cyan
Write-Host "Identical except namespace: $($stats.IdenticalExceptNamespace)" -ForegroundColor Green
Write-Host "High priority: $($stats.HighPriority)" -ForegroundColor Red
Write-Host "High risk: $($stats.HighRisk)" -ForegroundColor Red

# Generate markdown TodoList
$markdown = @"
# Core模块批量文件TodoList

## 📊 批量分析统计

- **总文件数**: $($stats.Total)
- **仅命名空间差异**: $($stats.IdenticalExceptNamespace) 个文件 ✅
- **高优先级**: $($stats.HighPriority) 个文件 🔴
- **中优先级**: $($stats.MediumPriority) 个文件 🟡  
- **低优先级**: $($stats.LowPriority) 个文件 🟢
- **高风险**: $($stats.HighRisk) 个文件 ⚠️
- **中风险**: $($stats.MediumRisk) 个文件 ⚠️
- **低风险**: $($stats.LowRisk) 个文件 ✅

---

## 🟢 可以直接覆盖的文件 (仅命名空间差异)

"@

# Add files that are identical except namespace
$identicalFiles = $processedFiles | Where-Object { $_.IsIdenticalExceptNamespace -eq $true } | Sort-Object RelativePath
foreach ($file in $identicalFiles) {
    $markdown += @"

### [ ] **$($file.RelativePath)**
- 📊 源: $($file.SourceLines)行 | 目标: $($file.TargetLines)行
- ✅ 仅命名空间差异 - 可以安全覆盖

"@
}

$markdown += @"

---

## 🔴 高优先级文件 (需要仔细检查)

"@

# Add high priority files
$highPriorityFiles = $processedFiles | Where-Object { $_.Priority -eq "High" -and $_.IsIdenticalExceptNamespace -ne $true } | Sort-Object RiskLevel, LineDifference -Descending
foreach ($file in $highPriorityFiles) {
    $riskIcon = switch ($file.RiskLevel) {
        "High" { "🔴" }
        "Medium" { "🟡" }
        "Low" { "🟢" }
        default { "❓" }
    }
    
    $markdown += @"

### [ ] **$($file.RelativePath)** $riskIcon
- 📊 源: $($file.SourceLines)行 | 目标: $($file.TargetLines)行 | 差异: $($file.LineDifference)行
- 🎯 优先级: 高 | 风险: $($file.RiskLevel)
- 💡 需要详细检查内容差异

"@
}

$markdown += @"

---

## 🟡 中优先级文件 (按需检查)

"@

# Add medium priority files (first 20)
$mediumPriorityFiles = $processedFiles | Where-Object { $_.Priority -eq "Medium" -and $_.IsIdenticalExceptNamespace -ne $true } | Sort-Object RiskLevel, LineDifference -Descending | Select-Object -First 20
foreach ($file in $mediumPriorityFiles) {
    $riskIcon = switch ($file.RiskLevel) {
        "High" { "🔴" }
        "Medium" { "🟡" }
        "Low" { "🟢" }
        default { "❓" }
    }
    
    $markdown += @"

### [ ] **$($file.RelativePath)** $riskIcon
- 📊 源: $($file.SourceLines)行 | 目标: $($file.TargetLines)行 | 差异: $($file.LineDifference)行
- 🎯 优先级: 中 | 风险: $($file.RiskLevel)

"@
}

$markdown += @"

---

## 📝 处理建议

1. **立即处理**: $($stats.IdenticalExceptNamespace) 个仅命名空间差异的文件
2. **优先检查**: $($stats.HighPriority) 个高优先级文件  
3. **按需处理**: 其余文件根据业务需要决定

**生成时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**分析文件数**: $($stats.Total)/365

"@

# Save markdown file
$markdown | Out-File -FilePath $OutputFile -Encoding UTF8
Write-Host ""
Write-Host "Batch TodoList saved to: $OutputFile" -ForegroundColor Green

# Save analysis data
$dataFile = $OutputFile -replace "\.md$", "_Data.json"
$finalResult = @{
    Statistics = $stats
    Files = $processedFiles
    GeneratedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
}
$finalResult | ConvertTo-Json -Depth 10 | Out-File -FilePath $dataFile -Encoding UTF8
Write-Host "Analysis data saved to: $dataFile" -ForegroundColor Green

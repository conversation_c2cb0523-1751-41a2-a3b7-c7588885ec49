/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/12/8/星期二 13:13:24
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemElemItemDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemElemItemDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 面料号
        /// </summary>
        public Guid ItemID { set; get; }
        /// <summary>
        /// 款式明细
        /// </summary>
        public Guid ModelElemID { set; get; }
        /// <summary>
        /// 货号
        /// </summary>
        public Guid? ItemID1 { set; get; }
        /// <summary>
        /// 客户
        /// </summary>
        public Guid? ClientID { set; get; }

        public bool IsActive { set; get; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public ODM_SorderType SorderType { set; get; } = ODM_SorderType.MTMGD;
    }
}

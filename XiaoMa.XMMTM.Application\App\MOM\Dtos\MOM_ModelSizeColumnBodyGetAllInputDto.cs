﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelSizeColumnBodyGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2021/4/3/星期六 14:55:34
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelSizeColumnBodyGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelSizeColumnBodyGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        [Required]
        public Guid Id { set; get; }

        public Guid? BodyID { set; get; }

        public Guid? SizeColumnID { set; get; }
    }
}

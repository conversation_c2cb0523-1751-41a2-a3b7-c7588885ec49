﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:22
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelModelElemGetAllOutputDto1 : ModelModelElem
    {
        //public bool GenderID { set; get; }

        //public string ModelCode { set; get; }

        //public string ModelName { set; get; }

        //public Guid GroupID { set; get; }

        //public string GroupCode { set; get; }

        //public string GroupName { set; get; }

        public string ItemCode { set; get; }
        public string NItemCode { set; get; }
        public string ItemName { set; get; }
        public string NItemName { set; get; }
        public string OriginalItemNo { set; get; }
        public string NOriginalItemNo { set; get; }
        public int? ModelElemListCadSeq { set; get; }
        public string ModelElemTypeText { set; get; }
        public string ModelElemText { set; get; }

        public string ModelElemListText { set; get; }

        public string ModelElemListCode { set; get; }
        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }

        public int ModelElemTypeID { set; get; }

        public Guid ModelElemListID { set; get; }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/12/8/星期二 13:13:03
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ItemElemItemAppService : XMMTMAppServiceBase, IBAD_ItemElemItemAppService
    {
        private readonly IRepository<ItemElemItem, Guid> repository;
        private readonly IRepository<ItemElemItemConfig, Guid> itemElemItemConfigRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IObjectMapper objectMapper;
        public BAD_ItemElemItemAppService(
       IRepository<ItemElemItem, Guid> repository,
       IRepository<ItemElemItemConfig, Guid> itemElemItemConfigRepository,
       IRepository<Item, Guid> itemRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.itemElemItemConfigRepository = itemElemItemConfigRepository;
            this.itemRepository = itemRepository;
            this.modelElemRepository = modelElemRepository;
            this.clientRepository = clientRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.objectMapper = objectMapper;
        }
        /// <summary>
        /// 面料配色方案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemElemItemGetAllOutputDto>> GetML(BAD_ItemElemItemGetAllInputDto input)
        {

            var listIDs = await itemElemItemConfigRepository.GetAll().Where(a => a.IsActive && a.ItemClassID == BAD_ItemClass.ML).OrderBy(a => a.Seq).ThenByDescending(a => a.ModifyOn).Select(a => a.ModelElemID).ToListAsync();
            return await get(input, listIDs, BAD_ItemClass.ML);
        }
        /// <summary>
        /// 辅料配色方案
        /// </summary>
        /// <param name="input"></param>
        /// <param name="listIDs"></param>
        /// <param name="itemClass"></param>
        /// <returns></returns>
        private async Task<PagedResultDto<BAD_ItemElemItemGetAllOutputDto>> get(BAD_ItemElemItemGetAllInputDto input, List<Guid> listIDs, BAD_ItemClass itemClass)
        {

            var query = from t in repository.GetAll()
                        join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                        from t1 in t1xx.DefaultIfEmpty()
                        join t2x in itemRepository.GetAll() on t.ItemID1 equals t2x.Id into t2xx
                        from t2 in t2xx.DefaultIfEmpty()
                        join t3x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t3x.Id into t3xx
                        from t3 in t3xx.DefaultIfEmpty()
                        join t4x in clientRepository.GetAll() on t.ClientID equals t4x.Id into t4xx
                        from t4 in t4xx.DefaultIfEmpty()
                        where t.ItemID == input.ItemID
                        select new BAD_ItemElemItemGetAllOutputDto()
                        {
                            ItemID = input.ItemID,
                            ItemID1 = t.ItemID1,
                            IsActive = t.IsActive,
                            ClientID = t.ClientID,
                            Id = t.Id,
                            CreateBy = t.CreateBy,
                            CreateID = t.CreateID,
                            CreateOn = t.CreateOn,
                            ModelElemID = t.ModelElemID,
                            ModifyBy = t.ModifyBy,
                            ModifyID = t.ModifyID,
                            ModifyOn = t.ModifyOn,
                            SorderType = t.SorderType,
                            SorderTypeText = t.SorderType.GetDescription(),
                            ModelElemText = t1.Code + (string.IsNullOrEmpty(t1.CodeName) ? "" : ":" + t1.CodeName),
                            Item1Text = t2.Code + (string.IsNullOrEmpty(t2.CodeName) ? "" : ":" + t2.CodeName),
                            OriginalItemNo = t2.OriginalItemNo,
                            ModelElemListText = t3.Code + (string.IsNullOrEmpty(t3.CodeName) ? "" : ":" + t3.CodeName),
                            ClientText = t4.Code + (string.IsNullOrEmpty(t4.ShortName) ? "" : ":" + t4.ShortName),
                        };
            var modelElemIds = await query.Select(a => a.ModelElemID).Distinct().ToListAsync();
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();


            var excpIds = listIDs.Except(modelElemIds).ToList();
            var modelElems = await (from t in modelElemRepository.GetAll()
                                    join t1 in modelElemListRepository.GetAll() on t.ModelElemListID equals t1.Id
                                    join t2x in itemElemItemConfigRepository.GetAll() on t.Id equals t2x.ModelElemID into t2xx
                                    from t2 in t2xx.DefaultIfEmpty()
                                    where excpIds.Contains(t.Id) && t2.ItemClassID == itemClass
                                    orderby t2.Seq
                                    //orderby t2.ModifyOn descending
                                    select new BAD_ItemElemItemGetAllOutputDto()
                                    {
                                        ItemID = input.ItemID,
                                        IsActive = false,
                                        ModelElemID = t.Id,
                                        ModelElemText = t.Code + (string.IsNullOrEmpty(t.CodeName) ? "" : ":" + t.CodeName),
                                        SorderType = ODM_SorderType.MTMGD,
                                        SorderTypeText = ODM_SorderType.MTMGD.GetDescription(),
                                        ModelElemListText = t1.Code + (string.IsNullOrEmpty(t1.CodeName) ? "" : ":" + t1.CodeName),
                                    }).ToListAsync();
            result.AddRange(modelElems);
            return new PagedResultDto<BAD_ItemElemItemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemElemItemGetAllOutputDto>> GetFL(BAD_ItemElemItemGetAllInputDto input)
        {

            var listIDs = await itemElemItemConfigRepository.GetAll().Where(a => a.IsActive && a.ItemClassID == BAD_ItemClass.FL).OrderBy(a => a.Seq).ThenByDescending(a => a.ModifyOn).Select(a => a.ModelElemID).ToListAsync();
            return await get(input, listIDs, BAD_ItemClass.FL);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemElemItemDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同项,请检查后重新选择！");
                }
                if ((!entity.Id.HasValue || Guid.Empty == entity.Id.Value))
                {
                    //if (entity.ItemID1.HasValue)
                    //{


                    //}
                    var dto = ObjectMapper.Map<ItemElemItem>(entity);
                    dto.Id = new Guid();
                    await repository.InsertAsync(dto);
                }
                else
                {
                    //if (!entity.ItemID1.HasValue)
                    //{
                    //    var oldentity = repository.Get(entity.Id.Value);
                    //    if (oldentity != null)
                    //    {
                    //        await repository.DeleteAsync(oldentity);

                    //    }

                    //}
                    //else
                    //{
                    //    var oldentity = repository.Get(entity.Id.Value);
                    //    ObjectMapper.Map(entity, oldentity);
                    //    await repository.UpdateAsync(oldentity);
                    //}

                    var oldentity = repository.Get(entity.Id.Value);
                    ObjectMapper.Map(entity, oldentity);
                    await repository.UpdateAsync(oldentity);
                }

            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemElemItemDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                if (oldentity == null)
                {
                    throw new UserFriendlyException("数据不存在,请刷新后重试");
                }
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemElemItemDto input)
        {
            //throw new UserFriendlyException();

            var query = await repository.GetAll()
                .WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.ClientID.HasValue, a => a.ClientID.Value == input.ClientID.Value)
                .WhereIf(input.ItemID1.HasValue, a => a.ItemID1.Value == input.ItemID1.Value)//货号
                .Where(a => a.ModelElemID == input.ModelElemID && a.SorderType == input.SorderType&&a.ItemID==input.ItemID).AnyAsync();
            return query;
            //if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            //{
            //    var query = repository.GetAll().Where(a => a.ItemID == input.ItemID && a.ModelElemID == input.ModelElemID && a.SorderType == input.SorderType);
            //    if (input.ClientID.HasValue)
            //    {
            //        query = query.Where(a => a.ClientID.HasValue && a.ClientID.Value == input.ClientID.Value);
            //    }
            //    else
            //    {
            //        query = query.Where(a => !a.ClientID.HasValue);
            //    }
            //    return await query.AnyAsync();
            //}
            //else
            //{
            //    var query = repository.GetAll().Where(a => a.ItemID == input.ItemID && a.ModelElemID == input.ModelElemID && a.SorderType == input.SorderType && a.Id != input.Id.Value);
            //    if (input.ClientID.HasValue)
            //    {
            //        query = query.Where(a => a.ClientID.HasValue && a.ClientID.Value == input.ClientID.Value);
            //    }
            //    else
            //    {
            //        query = query.Where(a => !a.ClientID.HasValue);
            //    }
            //    return await query.AnyAsync();
            //}

        }
    }
}

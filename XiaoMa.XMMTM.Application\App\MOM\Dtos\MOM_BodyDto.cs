﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_BodyDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 13:35:30
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_BodyDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_BodyDto : EntityDto<Guid?>
    {
        public decimal? Min { set; get; }
        public decimal? AllowMin { set; get; }
        public decimal? Max { set; get; }
        public decimal? AllowMax { set; get; }
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }

        public decimal Value { get; set; }

        public Guid BodyListID { set; get; }
        public virtual bool IsActive { set; get; }
    }
}

# Core Module File Comparison Script - Fixed Version
param(
    [string]$SourcePath = "e:\workgit\xm\XiaoMa.MTM\XiaoMa.XMMTM.Core",
    [string]$TargetPath = "e:\workgit\Shop\XiaoMa.MTM\XiaoMa.Shop.XMMTM.Core"
)

Write-Host "Starting Core module comparison..." -ForegroundColor Green
Write-Host "Source: $SourcePath" -ForegroundColor Yellow
Write-Host "Target: $TargetPath" -ForegroundColor Yellow

# Check paths
if (-not (Test-Path $SourcePath)) {
    Write-Host "Error: Source path not found - $SourcePath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $TargetPath)) {
    Write-Host "Error: Target path not found - $TargetPath" -ForegroundColor Red
    exit 1
}

# Get source files
Write-Host "Scanning source files..." -ForegroundColor Cyan
$sourceFiles = @()
Get-ChildItem -Path $SourcePath -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*" 
} | ForEach-Object {
    # Fix relative path calculation
    $relativePath = $_.FullName.Substring($SourcePath.Length).TrimStart('\')
    $sourceFiles += [PSCustomObject]@{
        Name = $_.Name
        RelativePath = $relativePath
        FullPath = $_.FullName
        Directory = if ($relativePath.Contains('\')) { Split-Path $relativePath -Parent } else { "" }
    }
}

Write-Host "Sample source files:" -ForegroundColor Yellow
$sourceFiles | Select-Object -First 5 | ForEach-Object {
    Write-Host "  $($_.RelativePath)" -ForegroundColor White
}
Write-Host "Source files: $($sourceFiles.Count)" -ForegroundColor Green

# Get target files
Write-Host "Scanning target files..." -ForegroundColor Cyan
$targetFiles = @()
Get-ChildItem -Path $TargetPath -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*" 
} | ForEach-Object {
    # Fix relative path calculation
    $relativePath = $_.FullName.Substring($TargetPath.Length).TrimStart('\')
    $targetFiles += [PSCustomObject]@{
        Name = $_.Name
        RelativePath = $relativePath
        FullPath = $_.FullName
        Directory = if ($relativePath.Contains('\')) { Split-Path $relativePath -Parent } else { "" }
    }
}

Write-Host "Sample target files:" -ForegroundColor Yellow
$targetFiles | Select-Object -First 5 | ForEach-Object {
    Write-Host "  $($_.RelativePath)" -ForegroundColor White
}
Write-Host "Target files: $($targetFiles.Count)" -ForegroundColor Green

# Compare files
Write-Host "Comparing files..." -ForegroundColor Cyan
$results = @()

foreach ($sourceFile in $sourceFiles) {
    $matchingTarget = $targetFiles | Where-Object { $_.RelativePath -eq $sourceFile.RelativePath }
    
    if ($matchingTarget) {
        $results += [PSCustomObject]@{
            FileName = $sourceFile.Name
            RelativePath = $sourceFile.RelativePath
            Directory = $sourceFile.Directory
            Status = "Both Exist"
            SourcePath = $sourceFile.FullPath
            TargetPath = $matchingTarget.FullPath
            Action = "Need Content Check"
        }
    } else {
        $results += [PSCustomObject]@{
            FileName = $sourceFile.Name
            RelativePath = $sourceFile.RelativePath
            Directory = $sourceFile.Directory
            Status = "Source Only"
            SourcePath = $sourceFile.FullPath
            TargetPath = "Not Exist"
            Action = "Copy to Target"
        }
    }
}

# Check target-only files
foreach ($targetFile in $targetFiles) {
    $matchingSource = $sourceFiles | Where-Object { $_.RelativePath -eq $targetFile.RelativePath }
    
    if (-not $matchingSource) {
        $results += [PSCustomObject]@{
            FileName = $targetFile.Name
            RelativePath = $targetFile.RelativePath
            Directory = $targetFile.Directory
            Status = "Target Only"
            SourcePath = "Not Exist"
            TargetPath = $targetFile.FullPath
            Action = "Target Specific"
        }
    }
}

# Statistics
$bothExist = ($results | Where-Object { $_.Status -eq "Both Exist" }).Count
$sourceOnly = ($results | Where-Object { $_.Status -eq "Source Only" }).Count
$targetOnly = ($results | Where-Object { $_.Status -eq "Target Only" }).Count

Write-Host ""
Write-Host "=== Core Module Comparison Results ===" -ForegroundColor Green
Write-Host "Both projects: $bothExist files" -ForegroundColor Cyan
Write-Host "Source only: $sourceOnly files" -ForegroundColor Yellow
Write-Host "Target only: $targetOnly files" -ForegroundColor Magenta
Write-Host "Total: $($results.Count) files" -ForegroundColor Green

# Show some matching files
if ($bothExist -gt 0) {
    Write-Host ""
    Write-Host "Sample matching files:" -ForegroundColor Green
    $results | Where-Object { $_.Status -eq "Both Exist" } | Select-Object -First 10 | ForEach-Object {
        Write-Host "  $($_.RelativePath)" -ForegroundColor Cyan
    }
}

# Save results
$outputPath = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
$finalResult = @{
    Summary = @{
        TotalFiles = $results.Count
        BothExist = $bothExist
        SourceOnly = $sourceOnly
        TargetOnly = $targetOnly
        GeneratedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    Files = $results
}

$finalResult | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8
Write-Host ""
Write-Host "Results saved to: $outputPath" -ForegroundColor Green

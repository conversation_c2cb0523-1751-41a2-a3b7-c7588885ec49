﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleCheckGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2022年11月1日,星期二 13:40:19
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeRuleCheckGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_SizeRuleCheckGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? Id { set; get; }
        public SYS_SorderSizeType? SorderSizeType { set; get; }
        public ODM_SorderType? SorderType { set; get; }

        public Guid? SizeColumnID { set; get; }
        public SizeRuleCheckTypeEnums? SizeRuleCheckType { set; get; }

        public Guid? GroupID { set; get; }
    }
}

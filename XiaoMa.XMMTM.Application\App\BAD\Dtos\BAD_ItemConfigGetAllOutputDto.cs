﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemConfigGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/10/26/星期一 9:10:50
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemConfigGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ItemConfigGetAllOutputDto : ItemConfig
    {
        public string ItemConfigBaseText { set; get; }
    }
}

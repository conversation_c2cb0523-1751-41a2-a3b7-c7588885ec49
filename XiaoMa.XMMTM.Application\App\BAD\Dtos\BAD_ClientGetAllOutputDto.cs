/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYM_ClientGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/7/10/星期五 21:51:37
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// SYM_ClientGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ClientGetAllOutputDto : Client
    {
        //public string ClientGroupText { set; get; }

        //public string ClientGradeText { set; get; }

        /// <summary>
        /// 客户分类
        /// </summary>
        public string ClientGroupText { set; get; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public string ClientGradeText { set; get; }
        public string AccountPaymentGroupText { set; get; }
        /// <summary>
        /// 货币
        /// </summary>
        public string CurrencyTypeText { set; get; }
        /// <summary>
        ///  业务员
        /// </summary>
        public string SalesmanName { set; get; }
        public string SalesmanName1 { set; get; }
        public BAD_ClientGetAllOutputDto()
        {
            this.Childs = new List<BAD_ClientGetAllOutputDto>();
        }
        public List<BAD_ClientGetAllOutputDto> Childs { set; get; }
    }
}

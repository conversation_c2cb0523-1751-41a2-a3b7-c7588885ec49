﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ElemRuleInputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/11/25/星期三 17:22:59
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class ElemRuleInputDto
    {

        public ElemRuleInputDto()
        {
            this.ModelElems = new List<ModelElemDto>();
            this.SizeColumns = new List<SizeColumnDto>();
            this.OtherElemRules = new List<OtherElemRule>();
        }

        public Guid ModelElemRuleID { set; get; }
        public RuleTypeEnum BomRuleType { set; get; }

        public List<ModelElemDto> ModelElems { set; get; }

        public List<SizeColumnDto> SizeColumns { set; get; }

        public List<OtherElemRule> OtherElemRules { set; get; }
    }
    /// <summary>
    /// 物料和订单类型
    /// </summary>
    public class OtherElemRule
    {
        public Guid? ItemID { set; get; }
        public ODM_SorderType? SorderType { set; get; }
    }
    /// <summary>
    /// 款式明细
    /// </summary>
    public class ModelElemDto
    {
        public Guid ModelElemListID { set; get; }
        public Guid? ModelElemID { set; get; }
        public Guid? ItemID { set; get; }
        public decimal? ItemWidth { set; get; }
        public decimal? Qty { set; get; }

    }
    /// <summary>
    /// 规格数据
    /// </summary>
    public class SizeColumnDto
    {
        public Guid SizeColumnID { set; get; }
        public decimal? Finish { set; get; }
    }
}

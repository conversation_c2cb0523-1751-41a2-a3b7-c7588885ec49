/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/28/星期二 16:20:17
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using System.Collections.Generic;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        /// <summary>
        ///
        /// </summary>

        public bool Default { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal Price { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal Qty { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelElemListID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? ItemID { get; set; }

        /// <summary>
        /// 是否输入内容
        /// </summary>

        public bool IsInputItem { get; set; }


        /// <summary>
        ///
        /// </summary>

        public bool IsInput { get; set; }
        public virtual bool IsActive { set; get; }


        /// <summary>
        /// 是否允许添加物料
        /// </summary>

        public bool IsItemAdd { get; set; } = false;

        /// <summary>
        ///是否允许添加物料图片
        /// </summary>

        public bool IsItemImageAdd { get; set; } = false;
        /// <summary>
        /// 是否支持配色方案
        /// </summary>
        public bool? IsItemElem { get; set; } = false;


        public int? Sort { set; get; }

        public List<Guid> ItemGroupIDs { set; get; }

        /// <summary>
        /// 衣拿吊挂款式编码生成规则编码
        /// </summary>
        public InaStyleCodeEnums? InaStyleCode { set; get; }
    }
}

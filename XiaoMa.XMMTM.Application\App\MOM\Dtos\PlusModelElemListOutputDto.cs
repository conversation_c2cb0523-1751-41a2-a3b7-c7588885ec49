﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    PlusModelElemListOutputDto
// 功能描述：    
// 作者    zhangby
// 时间    2021/5/25/星期二 16:02:48
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;


namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class PlusModelElemListOutputDto
    {
        public Guid? ItemID { set; get; }
        public Guid ModelElemListID { set; get; }
        public Guid ModelElemBaseID { set; get; }
        public Guid ModelElemID { set; get; }

        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public string ItemName { set; get; }
        public string ItemCode { set; get; }
        public string ItemOriginalItemNo { set; get; }
        public string ModelElemListCode { set; get; }
        public string ModelElemListName { set; get; }
        public string ModelElemTypeText { set; get; }
        public string ModelElemBaseCode { set; get; }
        public string ModelElemBaseName { set; get; }
        public int ModelElemBaseSequence { set; get; }
        public int? ModelElemListSequence { set; get; }
        public int? ModelElemSequence { set; get; }
        public decimal? ItemWidth { set; get; }
        public decimal? Qty { set; get; }
        public string Input { set; get; }
    }
}

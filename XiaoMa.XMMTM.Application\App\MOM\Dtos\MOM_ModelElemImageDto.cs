﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:40
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemImageDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemImageDto : EntityDto<Guid?>
    {
        /// <summary>
        ///合成图
        /// </summary>

        public bool Mix { get; set; } = false;

        /// <summary>
        ///
        /// </summary>

        public int? ImageSeq { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelElemID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? ModelElemID1 { get; set; }
        public Guid? ModelElemID2 { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelImageID { get; set; }

        /// <summary>
        ///
        /// </summary>
        public SYS_Position? PositionID { get; set; }
        public virtual bool IsActive { set; get; }

    }
}

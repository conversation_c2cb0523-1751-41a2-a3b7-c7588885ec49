﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_Profile
// 功能描述：    
// 作者    zhangby
// 时间    2020/7/15/星期三 13:19:03
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;
using AutoMapper;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    public class BAD_Profile : Profile
    {
        public BAD_Profile()
        {
            #region BAD

          
            CreateMap<Client, BAD_ClientDto>();
            CreateMap<ClientPerson, BAD_ClientPersonDto>();
            CreateMap<ClientShop, BAD_ClientShopDto>();
            CreateMap<ClientModel, BAD_ClientModelDto>();
            CreateMap<ClientAddress, BAD_ClientAddressDto>();
            CreateMap<Item, BAD_ItemDto>();
            CreateMap<ItemSeries, BAD_ItemSeriesDto>();
            CreateMap<ItemSeriesItem, BAD_ItemSeriesItemDto>();
            CreateMap<ItemConfig, BAD_ItemConfigDto>();
            CreateMap<ItemElemItemConfig, BAD_ItemElemItemConfigDto>();
            CreateMap<BAD_ItemImageDto,ItemImage>();
            CreateMap<Client, BAD_ClientGetAllOutputDto>();
            CreateMap<ClientPerson, BAD_ClientPersonGetAllOutputDto>();
            CreateMap<ClientShop, BAD_ClientShopGetAllOutputDto>();
            CreateMap<ClientAddress, BAD_ClientAddressGetAllOutputDto>();
            CreateMap<ItemSeries, BAD_ItemSeriesGetAllOutputDto>();
            CreateMap<ItemSeriesItem, BAD_ItemSeriesItemGetAllOutputDto>();
            CreateMap<ItemConfig, BAD_ItemConfigGetAllOutputDto>();
            CreateMap<ItemImage, BAD_ItemImageDto>();
            CreateMap<ClientModelClass, BAD_ClientModelClassDto>();

            CreateMap<BAD_ItemDto,Item>();
            CreateMap<BAD_ClientDto, Client>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<BAD_ClientPersonDto, ClientPerson>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ClientShopDto, ClientShop>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ClientModelDto, ClientModel>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ClientAddressDto, ClientAddress>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ItemSeriesDto, ItemSeries>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ItemSeriesItemDto, ItemSeriesItem>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ItemConfigDto, ItemConfig>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值

            CreateMap<BAD_ItemElemItemDto, ItemElemItem>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值

            CreateMap<BAD_ItemElemItemConfigDto, ItemElemItemConfig>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值
            CreateMap<BAD_ClientModelClassDto, ClientModelClass>().ForMember(dest => dest.CreateID, source => source.Ignore());//忽略空值


            #endregion
        }
    }
}

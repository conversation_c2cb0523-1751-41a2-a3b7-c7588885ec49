/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemListStatusDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/10/15/星期四 14:30:00
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemListStatusDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemListStatusDto : EntityDto<Guid?>
    {
        public virtual bool IsActive { set; get; }
        /// <summary>
        /// 
        /// </summary>

        public string Remark { get; set; }


        /// <summary>
        /// 
        /// </summary>

        public bool ItemRequired { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public bool QtyRequired { get; set; }


        /// <summary>
        /// 
        /// </summary>

        public bool InputRequired { get; set; }
        public bool ImageRequired { get; set; } = false;


        /// <summary>
        ///  订单状态
        /// </summary>
        public ODM_SorderStatus StatusID { get; set; }

        /// <summary>
        /// 款式
        /// </summary>

        public Guid ModelElemListID { get; set; }

        /// <summary>
        /// 款式明细
        /// </summary>
        public bool ModelElemRequired { get; set; }
    }
}

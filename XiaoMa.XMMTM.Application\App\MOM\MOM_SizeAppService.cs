/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/3/星期一 16:54:13
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeAppService : XMMTMAppServiceBase, IMOM_SizeAppService
    {
        private readonly IRepository<Size, Guid> repository;
        private readonly IRepository<SizeList, Guid> sizeListRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<SorderDetailModel, Guid> sorderDetailModelRepository;
        private readonly IRepository<Domain.Group, Guid> groupRepository;
        private readonly IRepository<SizeElema, Guid> sizeElemaRepository;
        private readonly IRepository<SizeElemb, Guid> sizeElembRepository;
        private readonly IRepository<SizeElemc, Guid> sizeElemcRepository;
        private readonly IRepository<SizeElemd, Guid> sizeElemdRepository;
        private readonly IRepository<SizeListSizeElema, Guid> sizeListSizeElemARepository;
        private readonly IRepository<SizeListSizeElemb, Guid> sizeListSizeElemBRepository;
        private readonly IRepository<SizeListSizeElemc, Guid> sizeListSizeElemCRepository;
        private readonly IRepository<SizeListSizeElemd, Guid> sizeListSizeElemDRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeAppService(
       IRepository<Size, Guid> repository,
       IRepository<SizeList, Guid> sizeListRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Sorder, Guid> sorderRepository,
       IRepository<SorderDetail, Guid> sorderDetailRepository,
       IRepository<SorderDetailModel, Guid> sorderDetailModelRepository,
       IRepository<Domain.Group, Guid> groupRepository,
              IRepository<SizeElema, Guid> sizeElemaRepository,
       IRepository<SizeElemb, Guid> sizeElembRepository,
       IRepository<SizeElemc, Guid> sizeElemcRepository,
       IRepository<SizeElemd, Guid> sizeElemdRepository,
       IRepository<SizeListSizeElema, Guid> sizeListSizeElemARepository,
       IRepository<SizeListSizeElemb, Guid> sizeListSizeElemBRepository,
       IRepository<SizeListSizeElemc, Guid> sizeListSizeElemCRepository,
       IRepository<SizeListSizeElemd, Guid> sizeListSizeElemDRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sizeListRepository = sizeListRepository;
            this.modelRepository = modelRepository;
            this.sorderRepository = sorderRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.sorderDetailModelRepository = sorderDetailModelRepository;
            this.groupRepository = groupRepository;
            this.sizeElemaRepository = sizeElemaRepository;
            this.sizeElembRepository = sizeElembRepository;
            this.sizeElemcRepository = sizeElemcRepository;
            this.sizeElemdRepository = sizeElemdRepository;
            this.sizeListSizeElemARepository = sizeListSizeElemARepository;
            this.sizeListSizeElemBRepository = sizeListSizeElemBRepository;
            this.sizeListSizeElemCRepository = sizeListSizeElemCRepository;
            this.sizeListSizeElemDRepository = sizeListSizeElemDRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeGetAllOutputDto>> Get(MOM_SizeGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                             //join t1x in modelRepository.GetAll() on t.SizeListID equals t1x.SizeListID into gg
                             //from t1 in gg.DefaultIfEmpty()
                         join t2 in sizeListRepository.GetAll() on t.SizeListID equals t2.Id
                         join t3x in groupRepository.GetAll() on t2.GroupID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in sizeElemaRepository.GetAll() on t.SizeElemaID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in sizeElembRepository.GetAll() on t.SizeElembID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t6x in sizeElemcRepository.GetAll() on t.SizeElemcID equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                         join t7x in sizeElemdRepository.GetAll() on t.SizeElemdID equals t7x.Id into t7xx
                         from t7 in t7xx.DefaultIfEmpty()
                         select new MOM_SizeGetAllOutputDto
                         {
                             BackMiddleLength = t.BackMiddleLength,
                             BackWaveLength = t.BackWaveLength,
                             BackWidth = t.BackWidth,
                             Bust = t.Bust,
                             CadModelCode = t.CadModelCode,
                             CadSizeCode = t.CadSizeCode,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             Collar = t.Collar,
                             CrossCrotch = t.CrossCrotch,
                             Cuff = t.Cuff,
                             FrontCrotch = t.FrontCrotch,
                             FrontLength = t.FrontLength,
                             FrontWaveLength = t.FrontWaveLength,
                             HalfBackWidth = t.HalfBackWidth,
                             HalfCrossCrotch = t.HalfCrossCrotch,
                             HalfCuff = t.HalfCuff,
                             HalfKneeGirth = t.HalfKneeGirth,
                             HalfSleeveWidth = t.HalfSleeveWidth,
                             HalfSmallCrossCrotch = t.HalfSmallCrossCrotch,
                             HalfTrouserBottom = t.HalfTrouserBottom,
                             Height = t.Height,
                             Hem = t.Hem,
                             Hipline = t.Hipline,
                             Id = t.Id,
                             InnerTrouserLong = t.InnerTrouserLong,
                             IsActive = t.IsActive,
                             JacketHipline = t.JacketHipline,
                             KneeGirth = t.KneeGirth,
                             MiddleWaist = t.MiddleWaist,
                             NetInnerTrouserLong = t.NetInnerTrouserLong,
                             NetTrouserLong = t.NetTrouserLong,
                             Remark = t.Remark,
                             SharpFrontLength = t.SharpFrontLength,
                             ShoulderWidth = t.ShoulderWidth,
                             SizeElemaID = t.SizeElemaID,
                             SizeElembID = t.SizeElembID,
                             SizeElemcID = t.SizeElemcID,
                             SizeElemdID = t.SizeElemdID,
                             SizeListID = t.SizeListID,
                             SizeName = t.SizeName,
                             SleeveLength = t.SleeveLength,
                             SleeveWidth = t.SleeveWidth,
                             SmallCrossCrotch = t.SmallCrossCrotch,
                             SmallShoulderWidth = t.SmallShoulderWidth,
                             StandCrotch = t.StandCrotch,
                             ThroughCrotch = t.ThroughCrotch,
                             TrouserBottom = t.TrouserBottom,
                             TrouserLong = t.TrouserLong,
                             Waist = t.Waist,
                             Waist1 = t.Waist1,
                             Waist2 = t.Waist2,
                             Yaosheng = t.Yaosheng,
                             ZipperLength = t.ZipperLength,
                             //ModelId = t1.Id,
                             SizeListCode = t2.Code,
                             GroupName = t3.CodeName,
                             SizeListName = t2.CodeName,
                             CreateOn = t.CreateOn,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             Half15CrossCrotch = t.Half15CrossCrotch,
                             Half44CrossCrotch = t.Half44CrossCrotch,
                             HalfBust = t.HalfBust,
                             HalfHem = t.HalfHem,
                             HalfHipline = t.HalfHipline,
                             HalfJacketHipline = t.HalfJacketHipline,
                             HalfMiddleWaist = t.HalfMiddleWaist,
                             HalfWaist = t.HalfWaist,
                             LeftinSleeveLength = t.LeftinSleeveLength,
                             LNetInnerTrouserLong = t.LNetInnerTrouserLong,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             RightinSleeveLength = t.RightinSleeveLength,
                             RNetInnerTrouserLong = t.RNetInnerTrouserLong,
                             LeftOutSleeveLength = t.LeftOutSleeveLength,
                             LeftOutTrouserLong = t.LeftOutTrouserLong,
                             RightOutSleeveLength = t.RightOutSleeveLength,
                             RightOutTrouserLong = t.RightOutTrouserLong,
                             Skirtlength = t.Skirtlength,
                             Sequence1 = t4.Sequence,
                             Sequence2 = t5.Sequence,
                             Sequence3 = t6.Sequence,
                             Sequence4 = t7.Sequence,
                             SizeElemaCode = t4.Code,
                             SizeElembCode = t5.Code,
                             SizeElemcCode = t6.Code,
                             SizeElemdCode = t7.Code,
                             HalfMiddleWaistOpen = t.HalfMiddleWaistOpen,
                         })
              //.Where(a => a.IsActive)
              .WhereIf(input.SizeListID.HasValue, a => a.SizeListID == input.SizeListID)
              //.WhereIf(input.ModelId.HasValue, a => a.ModelId == input.ModelId)
              .WhereIf(input.Id.HasValue, a => a.Id == input.Id)
              .WhereIf(input.SizeElemaID.HasValue, a => a.SizeElemaID == input.SizeElemaID.Value)
              .WhereIf(input.SizeElembID.HasValue, a => a.SizeElembID == input.SizeElembID.Value)
              .WhereIf(input.SizeElemcID.HasValue, a => a.SizeElemcID == input.SizeElemcID.Value)
              .WhereIf(input.SizeElemdID.HasValue, a => a.SizeElemdID == input.SizeElemdID.Value)
              .WhereIf(input.SizeElemaIDs != null && input.SizeElemaIDs.Any(), a => input.SizeElemaIDs.Contains(a.SizeElemaID.Value))
              .WhereIf(input.SizeElembIDs != null && input.SizeElembIDs.Any(), a => input.SizeElembIDs.Contains(a.SizeElembID.Value))
              .WhereIf(input.SizeElemcIDs != null && input.SizeElemcIDs.Any(), a => input.SizeElemcIDs.Contains(a.SizeElemcID.Value))
              .WhereIf(input.SizeElemdIDs != null && input.SizeElemdIDs.Any(), a => input.SizeElemdIDs.Contains(a.SizeElemdID.Value))
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            if (input.ModelId.HasValue)
            {
                var model = await modelRepository.FirstOrDefaultAsync(a => a.Id == input.ModelId.Value);
                if (model != null)
                {
                    query = query.Where(a => a.SizeListID == model.SizeListID);
                }
            }
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive)
                .ThenByDescending(a => a.Sequence1)
                .ThenByDescending(a => a.Sequence2)
                .ThenBy(a => a.Sequence3)
                .ThenByDescending(a => a.Sequence4)
                .ThenBy(a => a.Height)
                .ThenBy(a => a.FrontLength)
                .ThenBy(a => a.TrouserLong)
                .ThenBy(a => a.Bust)
                .ThenBy(a => a.Hipline)
                .ThenBy(a => a.Waist)
                .PageBy(input).ToListAsync();
            //var res = query.AsEnumerable().OrderBy(a => PadNumbers(a.Code)).ThenBy(a => a.Height);
            //var result = await res.AsQueryable().PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SizeGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeGetAllOutputDto>(count, result);
        }

        //private string PadNumbers(string input)
        //{
        //    return Regex.Replace(input, "[0-9]+", match => match.Value.PadLeft(10, '0'));
        //}
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeDto> input)
        {
            foreach (var entity in input)
            {
                //var dto=
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<Size>(entity);
                oldentity.CreateOn = DateTime.Now;
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeDto> input)
        {
            foreach (var entity in input)
            {
                //if (await this.ExistCodeAsync(entity))
                //{
                //    throw new UserFriendlyException("已经存在相同的编码");
                //}
                if (entity.Id.HasValue)
                {
                    //var oldentity = repository.Get(entity.Id.Value);
                    var oldentity = await repository.FirstOrDefaultAsync(a => a.SizeListID == entity.SizeListID && a.Id == entity.Id);
                    if (oldentity != null)
                    {
                        ObjectMapper.Map(entity, oldentity);
                        oldentity.CreateOn = DateTime.Now;
                        await repository.UpdateAsync(oldentity);
                    }
                    else
                    {
                        var old = await repository.GetAll().FirstOrDefaultAsync(a => a.Code.Equals(entity.Code) && a.SizeListID == entity.SizeListID);
                        if (old != null)
                        {
                            ObjectMapper.Map(entity, old);
                            old.CreateOn = DateTime.Now;
                            await repository.UpdateAsync(old);
                        }
                        else
                        {
                            var dto = ObjectMapper.Map<Size>(entity);
                            dto.CreateOn = DateTime.Now;
                            await repository.InsertAsync(dto);
                        }
                    }
                }
                else
                {
                    var oldentity = ObjectMapper.Map<Size>(entity);
                    await repository.InsertAsync(oldentity);
                }
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeDto> input)
        {
            foreach (var entity in input)
            {

                var sorders = await (from t in sorderDetailModelRepository.GetAll()
                                     join t1x in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1x.Id into t1xx
                                     from t1 in t1xx.DefaultIfEmpty()
                                         //join t2x in sorderRepository.GetAll() on t1.SorderID equals t2x.Id into t2xx
                                         //from t2 in t2xx.DefaultIfEmpty()
                                     where t.SizeID1 == entity.Id || t.SizeID == entity.Id
                                     select new
                                     {
                                         SorderID = t1.SorderID,
                                         SorderDetailModelID = t.Id,
                                         SorderDetailID = t.SorderDetailID,
                                     }
                                         ).ToListAsync();
                var sorderIDs = sorders.Select(a => a.SorderID).Distinct().ToList();
                var sorderList = await sorderRepository.GetAll().AsNoTracking().Where(a => sorderIDs.Contains(a.Id)).Select(a => new { a.Id, a.Code, a.IsDeleted }).ToListAsync();
                if (sorderList.Any())
                {
                    var sordercodes = sorderList.Where(a => a.Id != Guid.Empty).Where(a => !a.IsDeleted).Select(a => a.Code).Distinct().ToList();
                    if (sordercodes.Any())
                    {
                        var str = string.Join(',', sordercodes);
                        throw new UserFriendlyException($"订单{str}中已使用此规格号型,请先更换订单中引用的规格号型!");
                    }
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SizeDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.SizeListID == input.SizeListID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value && a.SizeListID == input.SizeListID);
            }

        }
        [HttpPost]
        [AbpAuthorize]
        public async Task UpdateSizeElem(UpdateSizeElemInputDto input)
        {
            var SizeQuery = repository.GetAll().Where(a => a.IsActive).Where(a => a.SizeListID == input.SizeListID);
            if (input.SizeIDs.Any())
            {
                SizeQuery = SizeQuery.Where(a => input.SizeIDs.Contains(a.Id));
            }
            var sizes = await SizeQuery.ToListAsync();
            if (input.SizeElema == "A")
            {
                await UpdateSizeElemA(sizes, input.SizeElemaID);
            }
            if (input.SizeElemb == "B")
            {
                await UpdateSizeElemB(sizes, input.SizeElembID);
            }
            if (input.SizeElemc == "C")
            {
                await UpdateSizeElemC(sizes, input.SizeElemcID);
            }
            if (input.SizeElemd == "D")
            {
                await UpdateSizeElemD(sizes, input.SizeElemdID);
            }
            await repository.GetDbContext().BulkUpdateAsync<Size>(sizes);
            await UpdateSIzeListSizeElem(sizes, input.SizeListID);
        }


        private async Task UpdateSizeElemA(List<Size> list, Guid? SizeElemID)
        {
            var heights = list.Select(a => ((int)a.Height).ToString()).Distinct().ToList();
            var sizeElemAs = await sizeElemaRepository.GetAll().Where(a => heights.Contains(a.Code)).ToListAsync();
            if (!sizeElemAs.Any())
            {
                return;
            }
            foreach (var item in list)
            {
                if (!SizeElemID.HasValue || SizeElemID == Guid.Empty)
                {
                    var a = sizeElemAs.FirstOrDefault(a => a.Code == ((int)item.Height).ToString());
                    if (a != null)
                    {
                        item.SizeElemaID = a.Id;
                    }
                    else
                    {
                        item.SizeElemaID = null;
                    }
                }
                else
                {
                    item.SizeElemaID = SizeElemID.Value;
                }
                item.ModifyOn = DateTime.Now;
                item.ModifyID = this.SSOSession.UserId;
                item.ModifyBy = this.SSOSession.Name;
            }
        }

        private async Task UpdateSIzeListSizeElem(List<Size> list, Guid SizeListID)
        {
            var aa = list.Where(a => a.SizeElemaID.HasValue).Select(a => a.SizeElemaID).Distinct().ToList();
            var sizeListSizeElema = await sizeListSizeElemARepository.GetAll().Where(a => SizeListID == (a.SizeListID)).Where(a => aa.Contains(a.SizeElemaID)).ToListAsync();
            await sizeListSizeElemARepository.GetDbContext().BulkDeleteAsync(sizeListSizeElema);
            var sizeElemA = await sizeElemaRepository.GetAll().Where(a => aa.Contains(a.Id)).Select(a => new SizeListSizeElema() { Id = Guid.NewGuid(), CreateBy = this.SSOSession.Name, CreateID = this.SSOSession.UserId, CreateOn = DateTime.Now, IsActive = true, Sequence = a.Sequence, SizeElemaID = a.Id, SizeListID = SizeListID }).ToListAsync();
            await sizeListSizeElemARepository.GetDbContext().BulkInsertAsync(sizeElemA);


            var bb = list.Where(a => a.SizeElembID.HasValue).Select(a => a.SizeElembID).Distinct().ToList();
            var sizeListSizeElemb = await sizeListSizeElemBRepository.GetAll().Where(a => SizeListID == (a.SizeListID)).Where(a => bb.Contains(a.SizeElembID)).ToListAsync();
            await sizeListSizeElemBRepository.GetDbContext().BulkDeleteAsync(sizeListSizeElemb);
            var sizeElemB = await sizeElembRepository.GetAll().Where(a => bb.Contains(a.Id)).Select(a => new SizeListSizeElemb() { Id = Guid.NewGuid(), CreateBy = this.SSOSession.Name, CreateID = this.SSOSession.UserId, CreateOn = DateTime.Now, IsActive = true, Sequence = a.Sequence, SizeElembID = a.Id, SizeListID = SizeListID }).ToListAsync();
            await sizeListSizeElemBRepository.GetDbContext().BulkInsertAsync(sizeElemB);

            var cc = list.Where(a => a.SizeElemcID.HasValue).Select(a => a.SizeElemcID).Distinct().ToList();
            var sizeListSizeElemc = await sizeListSizeElemCRepository.GetAll().Where(a => SizeListID == (a.SizeListID)).Where(a => cc.Contains(a.SizeElemcID)).ToListAsync();
            await sizeListSizeElemCRepository.GetDbContext().BulkDeleteAsync(sizeListSizeElemc);
            var sizeElemC = await sizeElemcRepository.GetAll().Where(a => cc.Contains(a.Id)).Select(a => new SizeListSizeElemc() { Id = Guid.NewGuid(), CreateBy = this.SSOSession.Name, CreateID = this.SSOSession.UserId, CreateOn = DateTime.Now, IsActive = true, Sequence = a.Sequence, SizeElemcID = a.Id, SizeListID = SizeListID }).ToListAsync();
            await sizeListSizeElemCRepository.GetDbContext().BulkInsertAsync(sizeElemC);

            var dd = list.Where(a => a.SizeElemdID.HasValue).Select(a => a.SizeElemdID).Distinct().ToList();
            var sizeListSizeElemd = await sizeListSizeElemDRepository.GetAll().Where(a => SizeListID == (a.SizeListID)).Where(a => dd.Contains(a.SizeElemdID)).ToListAsync();
            await sizeListSizeElemDRepository.GetDbContext().BulkDeleteAsync(sizeListSizeElemd);
            var sizeElemD = await sizeElemdRepository.GetAll().Where(a => dd.Contains(a.Id)).Select(a => new SizeListSizeElemd() { Id = Guid.NewGuid(), CreateBy = this.SSOSession.Name, CreateID = this.SSOSession.UserId, CreateOn = DateTime.Now, IsActive = true, Sequence = a.Sequence, SizeElemdID = a.Id, SizeListID = SizeListID }).ToListAsync();
            await sizeListSizeElemDRepository.GetDbContext().BulkInsertAsync(sizeElemD);
        }
        private async Task UpdateSizeElemB(List<Size> list, Guid? SizeElemID)
        {
            var busts = list.Select(a => a.Code).Distinct().ToList();
            busts = busts.Where(a => a.IndexOf("/") > 0).Select(a => new { Code = a.Substring(a.LastIndexOf('/'), a.Length - 1 - a.LastIndexOf('/')) }).Select(a => a.Code.Replace("/", "").Trim()).Distinct().ToList();
            if (!busts.Any())
            {
                busts = list.Select(a => a.Code).Distinct().ToList();
                busts = busts.Where(a => a.IndexOf("-") > 0).Select(a => new { Code = a.Substring(0, a.IndexOf('-')) }).Select(a => int.Parse(a.Code)).Select(a => (a * 2).ToString()).Distinct().ToList();
            }
            var sizeElemb = await sizeElembRepository.GetAll().Where(a => busts.Contains(a.Code)).ToListAsync();
            if ((!busts.Any() || !sizeElemb.Any()) && !SizeElemID.HasValue)
            {
                return;
            }
            foreach (var item in list)
            {
                if (!SizeElemID.HasValue || SizeElemID == Guid.Empty)
                {
                    var code = "";
                    if (item.Code.IndexOf("/") > 0)
                    {
                        code = item.Code.Substring(item.Code.LastIndexOf('/'), item.Code.Length - 1 - item.Code.LastIndexOf('/')).Replace("/", "").Trim();
                    }
                    if (item.Code.IndexOf("-") > 0)
                    {
                        code = item.Code.Substring(0, item.Code.IndexOf("-"));
                        code = (int.Parse(code) * 2).ToString();
                    }
                    var a = sizeElemb.FirstOrDefault(a => a.Code == code);
                    if (a != null)
                    {
                        item.SizeElembID = a.Id;
                    }
                    else
                    {
                        item.SizeElembID = null;
                    }
                }
                else
                {
                    item.SizeElembID = SizeElemID.Value;
                }
                item.ModifyOn = DateTime.Now;
                item.ModifyID = this.SSOSession.UserId;
                item.ModifyBy = this.SSOSession.Name;
            }

        }
        private async Task UpdateSizeElemC(List<Size> list, Guid? SizeElemID)
        {
            var cc = list.Select(a => a.Code.Substring(a.Code.Length - 1, 1)).Distinct().ToList();
            var sizeElemc = await sizeElemcRepository.GetAll().Where(a => cc.Contains(a.Code)).ToListAsync();
            if (!sizeElemc.Any())
            {
                return;
            }
            foreach (var item in list)
            {
                if (!SizeElemID.HasValue || SizeElemID == Guid.Empty)
                {
                    var code = item.Code.Substring(item.Code.Length - 1, 1);
                    var a = sizeElemc.FirstOrDefault(a => a.Code == code);
                    if (a != null)
                    {
                        item.SizeElemcID = a.Id;
                    }
                    else
                    {
                        item.SizeElemcID = null;
                    }
                }
                else
                {
                    item.SizeElemcID = SizeElemID.Value;
                }
                item.ModifyOn = DateTime.Now;
                item.ModifyID = this.SSOSession.UserId;
                item.ModifyBy = this.SSOSession.Name;
            }
        }
        private async Task UpdateSizeElemD(List<Size> list, Guid? SizeElemID)
        {

            var aa = list.Select(a => a.Code).Distinct().ToList();
            aa = aa.Where(a => a.IndexOf("/") > 0).Select(a => new { Code = a.Substring(a.LastIndexOf('/'), a.Length - 1 - a.LastIndexOf('/')) }).Select(a => a.Code.Replace("/", "").Trim()).Distinct().ToList();
            if (!aa.Any())
            {
                aa = list.Select(a => a.Code).Distinct().ToList();
                aa = aa.Where(a => a.IndexOf("-") > 0).Select(a => new { Code = a.Substring(0, a.IndexOf('-')) }).Select(a => int.Parse(a.Code)).Select(a => (a).ToString()).Distinct().ToList();
            }
            var sizeElemd = await sizeElemdRepository.GetAll().Where(a => aa.Contains(a.Code)).ToListAsync();
            if ((!aa.Any() || !sizeElemd.Any()) && !SizeElemID.HasValue)
            {
                return;
            }
            foreach (var item in list)
            {
                if (!SizeElemID.HasValue || SizeElemID == Guid.Empty)
                {
                    var code = "";
                    if (item.Code.IndexOf("/") > 0)
                    {
                        code = item.Code.Substring(item.Code.LastIndexOf('/'), item.Code.Length - 1 - item.Code.LastIndexOf('/')).Replace("/", "").Trim();
                    }
                    if (item.Code.IndexOf("-") > 0)
                    {
                        code = item.Code.Substring(0, item.Code.IndexOf("-"));
                        code = (int.Parse(code)).ToString();
                    }
                    var a = sizeElemd.FirstOrDefault(a => a.Code == code);
                    if (a != null)
                    {
                        item.SizeElemdID = a.Id;
                    }
                    else
                    {
                        item.SizeElemdID = null;
                    }
                }
                else
                {
                    item.SizeElemdID = SizeElemID.Value;
                }
                item.ModifyOn = DateTime.Now;
                item.ModifyID = this.SSOSession.UserId;
                item.ModifyBy = this.SSOSession.Name;
            }
        }
    }
}

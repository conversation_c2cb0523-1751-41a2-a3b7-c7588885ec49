# 简单统计脚本
$source = "e:\workgit\xm\XiaoMa.MTM"
$target = "e:\workgit\Shop\XiaoMa.MTM"

Write-Host "开始统计..." -ForegroundColor Green

# 定义模块映射
$moduleMapping = @{
    "XiaoMa.XMMTM.Core" = "XiaoMa.Shop.XMMTM.Core"
    "XiaoMa.XMMTM.Application" = "XiaoMa.Shop.XMMTM.Application"
    "XiaoMa.XMMTM.EntityFrameworkCore" = "XiaoMa.Shop.XMMTM.EntityFrameworkCore"
    "XiaoMa.XMMTM.Web.Core" = "XiaoMa.Shop.XMMTM.Web.Core"
    "XiaoMa.XMMTM.Web.Host" = "XiaoMa.Shop.XMMTM.Web.Host"
    "XiaoMa.XMMTM.Migrator" = "XiaoMa.Shop.XMMTM.Migrator"
}

$stats = @()

foreach ($sourceModuleName in $moduleMapping.Keys) {
    $targetModuleName = $moduleMapping[$sourceModuleName]
    
    $sourceModulePath = Join-Path $source $sourceModuleName
    $targetModulePath = Join-Path $target $targetModuleName
    
    if ((Test-Path $sourceModulePath) -and (Test-Path $targetModulePath)) {
        $sourceFiles = Get-ChildItem -Path $sourceModulePath -Recurse -Filter "*.cs" | Where-Object { 
            $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*" 
        }
        
        $targetFiles = Get-ChildItem -Path $targetModulePath -Recurse -Filter "*.cs" | Where-Object { 
            $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*" 
        }
        
        $stats += [PSCustomObject]@{
            SourceModule = $sourceModuleName
            TargetModule = $targetModuleName
            SourceFileCount = $sourceFiles.Count
            TargetFileCount = $targetFiles.Count
            Status = if (Test-Path $sourceModulePath) { if (Test-Path $targetModulePath) { "Both Exist" } else { "Target Missing" } } else { "Source Missing" }
        }
        
        Write-Host "$sourceModuleName -> $targetModuleName" -ForegroundColor Cyan
        Write-Host "  源文件: $($sourceFiles.Count), 目标文件: $($targetFiles.Count)" -ForegroundColor White
    }
    else {
        Write-Host "$sourceModuleName -> $targetModuleName [路径不存在]" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 统计完成 ===" -ForegroundColor Green

# 保存统计结果
$outputPath = Join-Path $target "CodeMigrationTodoLists\ModuleStats.json"
$stats | ConvertTo-Json -Depth 5 | Out-File -FilePath $outputPath -Encoding UTF8
Write-Host "统计结果已保存到: $outputPath" -ForegroundColor Green

return $stats

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelBaseImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:19:29
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelBaseImageDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelBaseImageDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 合成图
        /// </summary>

        public bool Mix { get; set; } = false;

        /// <summary>
        ///
        /// </summary>

        public int? ImageSeq { get; set; }

        /// <summary>
        ///基础版型
        /// </summary>

        public Guid ModelBaseID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? ModelElemID1 { get; set; }

        /// <summary>
        /// 版型图片
        /// </summary>

        public Guid ModelImageID { get; set; }


        /// <summary>
        ///
        /// </summary>
        public SYS_Position? PositionID { get; set; }
        public virtual bool IsActive { set; get; }
    }
}

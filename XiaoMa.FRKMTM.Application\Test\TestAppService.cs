﻿using Abp.Domain.Repositories;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using XiaoMa.FRKMTM;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.FRKMTM.Test
{
    /// <summary>
    /// 
    /// </summary>
    public class TestAppService : FRKMTMAppServiceBase, ITestAppService
    {
        //private readonly IRepository<ModelBodyList, Guid> modelBodyListRepository;
        //private readonly IRepository<Model, Guid> modelRepository;
        //private readonly IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository;
        //private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        //private readonly IRepository<BodyList, Guid> bodyListRepository;
        /// <summary>
        /// 
        /// </summary>
        public TestAppService(
       //     IRepository<ModelBodyList, Guid> modelBodyListRepository,
       //IRepository<Model, Guid> modelRepository,
       //IRepository<ModelBodyList, Guid> modelMoRepository,
       //IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository,
       //IRepository<SizeColumn, Guid> sizeColumnRepository,
       //IRepository<BodyList, Guid> bodyListRepository
            )
        {
            //this.modelBodyListRepository = modelBodyListRepository;
            //this.modelRepository = modelRepository;
            //this.modelSizeColumnRepository = modelSizeColumnRepository;
            //this.sizeColumnRepository = sizeColumnRepository;
            //this.bodyListRepository = bodyListRepository;
        }

        #region MyRegion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Abp.Authorization.AbpAllowAnonymous]
        [HttpPost]
        public async Task<string> Test(string input)
        {
            return await Task.Run(() =>
            {
                return "IndLike";
            });
          

        }

        #endregion

        /// <summary>
        /// 版型绑定规格 和特体
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Abp.Authorization.AbpAllowAnonymous]
        [HttpPost]
        public async Task<string> Test1(string input)
        {
            return await Task.Run(() =>
            {
                return "IndLike";
            });
        }
    }
}

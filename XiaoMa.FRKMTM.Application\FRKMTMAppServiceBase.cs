﻿using Abp.Application.Services;
using Abp.Linq;
using System;
using System.Linq.Expressions;
using XiaoMa.Runtime.Session.SSO;

namespace XiaoMa.FRKMTM
{
    /// <summary>
    /// Derive your application services from this class.
    /// </summary>
    public abstract class FRKMTMAppServiceBase : ApplicationService
    {
        /// <summary>
        /// 
        /// </summary>
        public ISSOSession SSOSession { set; get; }
        /// <summary>
        /// 
        /// </summary>
        public IAsyncQueryableExecuter AsyncQueryableExecuter { get; set; }
       //public  bool Exist<T>(Expression<Func<T, bool>> anyLambda) where T : class;
       /// <summary>
       /// 
       /// </summary>
        protected FRKMTMAppServiceBase()
        {
           
        }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ClientPersonAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/7/24/星期五 17:06:48
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ClientPersonAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ClientPersonGetAllOutputDto>> Get(BAD_ClientPersonGetAllInputDto input);
        Task<List<BAD_ClientPersonGetAllOutputDto>> Adds(List<BAD_ClientPersonDto> input);
        Task Updates(List<BAD_ClientPersonDto> input);
        Task Deletes(List<BAD_ClientPersonDto> input);
    }
}

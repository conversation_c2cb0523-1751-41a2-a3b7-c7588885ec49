﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemVariantDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/6/星期四 15:59:08
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemVariantDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemVariantDto : EntityDto<Guid?>
    {
        public virtual bool IsActive { set; get; }
        /// <summary>
        ///
        /// </summary>

        public string Variant { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? Value { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelElemID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? ModelElemID1 { get; set; }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemListGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/28/星期二 16:19:47
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemListGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemListGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        //[Required]
        //public Guid Id { set; get; }
        /// <summary>
        /// CAD顺序
        /// </summary>
        public bool? CadSeq { set; get; }
        /// <summary>
        /// 市场顺序
        /// </summary>

        public bool? MarketSeq { set; get; }

        /// <summary>
        /// 报表顺序
        /// </summary>
        public bool ? ElemSeq { set; get; }

        public bool ? IsPlanShow { set; get; }

        public bool ? IsItem { set; get; }

        public bool ? IsInput { set; get; }

        public bool ? GenderID { set; get; }

        public Guid ? GroupID { set; get; }
        public Guid ? ModelElemBaseID { set; get; }
        public SystemConfig.SYS_ModelElemType? ModelElemTypeID { set; get; }
        public InaStyleIndexEnums? InaStyleIndex { set; get; }

        /// <summary>
        /// 深定制简定制
        /// </summary>
        public bool? IsPlus { set; get; }

    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemPriceGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2021/6/4/星期五 10:42:23
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemPriceGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemPriceGetAllOutputDto : ModelElemPrice
    {
        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public string ItemCode { set; get; }
        public string ItemOriginalItemNo { set; get; }

        public string ModelElemListCode { set; get; }
        public string ModelElemListName { set; get; }
        public Guid ModelElemListID { set; get; }

        public string ClientName { set; get; }

        public bool IsCommon { set; get; }
        public string ModelElemCode1 { set; get; }
        public string ModelElemName1 { set; get; }
        public string ModelElemCode2 { set; get; }
        public string ModelElemName2 { set; get; }
        public string SorderTypeText { set; get; }
    }
}

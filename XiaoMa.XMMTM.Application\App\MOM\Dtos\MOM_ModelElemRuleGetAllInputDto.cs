﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemRuleGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/11/17/星期二 20:34:25
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemRuleGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemRuleGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        //[Required]
        //public Guid Id { set; get; }

        /// <summary>
        /// 公式类别
        /// </summary>

        public RuleTypeEnum? BomRuleType { get; set; }

        
        /// <summary>
        /// 支持算法 MTM 大货
        /// </summary>
        public ModelRuleTypeEnums? ModelRuleType { get; set; }

        public Guid? ModelElemListID { set; get; }
        public Guid? GroupID { set; get; }

        public bool? GenderID { set; get; }

        public bool ? IsActive { set; get; }
    }
}

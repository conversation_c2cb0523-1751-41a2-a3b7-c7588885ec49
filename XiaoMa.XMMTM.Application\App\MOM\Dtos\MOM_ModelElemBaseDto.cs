﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemBaseDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/28/星期二 16:19:01
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemBaseDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemBaseDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        /// <summary>
        /// 顺序
        /// </summary>

        public int Sequence { get; set; }

        /// <summary>
        ///
        /// </summary>
        public Guid? GroupID { get; set; }

        /// <summary>
        /// 性别
        /// </summary>

        public bool GenderID { get; set; }
        public virtual bool IsActive { set; get; }
    }
}

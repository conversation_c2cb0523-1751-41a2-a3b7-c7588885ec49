﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IMOM_SizeRuleAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 9:44:13
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.MOM.Dtos;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IMOM_SizeRuleAppService : IApplicationService
    {
        Task<PagedResultDto<MOM_SizeRuleGetAllOutputDto>> Get(MOM_SizeRuleGetAllInputDto input);
        Task Adds(List<MOM_SizeRuleDto> input);
        Task Updates(List<MOM_SizeRuleDto> input);
        Task Deletes(List<MOM_SizeRuleDto> input);
    }
}

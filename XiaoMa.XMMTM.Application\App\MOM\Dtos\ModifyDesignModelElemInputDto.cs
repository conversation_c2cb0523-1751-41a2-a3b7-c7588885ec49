﻿using System;
using System.Collections.Generic;
using System.Text;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
   public class ModifyDesignModelElemInputDto
    {
        public Guid ModelID { set; get; }
        public Guid ModelElemListID { set; get; }
        public Guid ModelElemID { set; get; }
        public decimal? ItemWidth { set; get; }
        public Guid? ItemID { set; get; }
        public decimal? Qty { set; get; }
        public Guid? Id { set; get; }
        public string Input { set; get; }

        public string ItemText { set; get; }

        public int? Sort { set; get; }
        public bool IsClientShow { set; get; }
        public bool IscustomerEdit { set; get; }
        public bool IsCustomerShow { set; get; }
        public bool IsInput { set; get; }
        public bool IsPlus { set; get; }
        public bool IsInputItem { set; get; }
        public bool IsPlanEdit { set; get; }
        public bool IsPlanShow { set; get; }
        public bool IsItemAdd { set; get; }
        public bool IsItemImageAdd { set; get; }

        public string ItemImageUrl { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/7/30/星期四 15:04:45
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelGetAllOutputDto : Model
    {
        public string BusinessSubTypeText { set; get; }
        public string ModelBaseText { set; get; }
        public string GroupText { set; get; }
        public string GenderText { set; get; }
        public string SizeListText { set; get; }
        public string SewBaseText { set; get; }

        public Guid? ClinetID { set; get; }
        public string ImagePath { set; get; }

        public string ModelGroupText { set; get; }

        public string OriginalModelCode { set; get; }
        public string OriginalModelName { set; get; }
        public string SoderNumber { set; get; }

        public string ItemCode { set; get; }
        public string ItemName { set; get; }
        public string ItemOriginalItemNo { set; get; }

        public Guid ? SorderID { set; get; }

    }
}

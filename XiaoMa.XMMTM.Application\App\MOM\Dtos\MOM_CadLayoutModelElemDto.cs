﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_CadLayoutModelElemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/6/星期四 16:00:22
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_CadLayoutModelElemDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_CadLayoutModelElemDto : EntityDto<Guid?>
    {
        /// <summary>
        ///
        /// </summary>

        public bool Default { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid CadLayoutID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelElemID { get; set; }
        public virtual bool IsActive { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:58
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemImageAppService : XMMTMAppServiceBase, IMOM_ModelElemImageAppService
    {
        private readonly IRepository<ModelElemImage, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ModelElemBase, Guid> modelElemBaseRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly FileServer.FileServer fileServer;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemImageAppService(
       IRepository<ModelElemImage, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ModelElemBase, Guid> modelElemBaseRepository,
       IRepository<Group, Guid> groupRepository,
       IRepository<ModelImage, Guid> modelImageRepository,
       FileServer.FileServer fileServer,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.modelElemBaseRepository = modelElemBaseRepository;
            this.groupRepository = groupRepository;
            this.modelImageRepository = modelImageRepository;
            this.fileServer = fileServer;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemImageGetAllOutputDto>> Get(MOM_ModelElemImageGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t2x in modelElemRepository.GetAll() on t.ModelElemID1 equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelElemRepository.GetAll() on t.ModelElemID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in modelElemListRepository.GetAll() on t3.ModelElemListID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in modelElemBaseRepository.GetAll() on t4.ModelElemBaseID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t7x in groupRepository.GetAll() on t4.GroupID equals t7x.Id into t7xx
                         from t7 in t7xx.DefaultIfEmpty()
                         join mx in modelImageRepository.GetAll() on t.ModelImageID equals mx.Id into mxx
                         from m in mxx.DefaultIfEmpty()
                         join t6x in modelElemRepository.GetAll() on t.ModelElemID2 equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                         select new MOM_ModelElemImageGetAllOutputDto()
                         {
                             Id = t.Id,
                             Mix = t.Mix,
                             ImageSeq = t.ImageSeq,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelElemID = t.ModelElemID,
                             ModelElemID1 = t.ModelElemID1,
                             ModelElemID2 = t.ModelElemID2,
                             PositionID = m.PositionID,
                             ModelImageID = t.ModelImageID, //
                             //ImageBac = t.ImageBac,
                             //PositionBac = t.PositionBac,
                             //t1.PositionCode,
                             //t1.PositionName,
                             ModelElemCode1 = t2.Code,
                             ModelElemName1 = t2.CodeName,
                             ModelElemCode2 = t6.Code,
                             ModelElemName2 = t6.CodeName,
                             ModelElemCode = t3.Code,
                             ModelElemName = t3.CodeName,
                             ModelElemListID = t3.ModelElemListID,
                             ModelElemListCode = t4.Code,
                             ModelElemListName = t4.CodeName,
                             ModelElemListText = t4.Code + ":" + t4.CodeName,
                             ModelElemBaseID = t4.ModelElemBaseID,
                             ModelElemTypeID = t4.ModelElemTypeID,
                             ModelElemBaseCode = t5.Code,
                             ModelElemBaseName = t5.CodeName,
                             ModelElemBaseText = t5.Code + ":" + t5.CodeName,
                             ModelElemTypeText = t4.ModelElemTypeID.GetDescription(),
                             GroupID = t4.GroupID,
                             GroupText = t7.Code + ":" + t7.CodeName,
                             ModelImage = m.Code + ": " + m.CodeName,//
                             ImagePath = m.ImagePath, //
                             Position = m.PositionID.GetDescription(), // 
                             ImageUrl = fileServer.GetImageUrl(m.ImagePath),
                             ModelElemListID1=t2.ModelElemListID,
                             ModelElemListID2=t6.ModelElemListID,
                         })
                        .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                        .WhereIf(input.ModelElemTypeID.HasValue, a => a.ModelElemTypeID == input.ModelElemTypeID.Value)
                        .WhereIf(input.PositionID.HasValue, a => a.PositionID == input.PositionID.Value)
                        .WhereIf(input.ModelElemBaseID.HasValue, a => a.ModelElemBaseID == input.ModelElemBaseID.Value)
                        .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value|| a.ModelElemListID1 == input.ModelElemListID.Value|| a.ModelElemListID2 == input.ModelElemListID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemCode.ToLower().Contains(input.Text.ToLower())
              || a.ModelElemName.ToLower().Contains(input.Text.ToLower())
              || a.ModelElemCode1.ToLower().Contains(input.Text.ToLower())
              || a.ModelElemName1.ToLower().Contains(input.Text.ToLower())
               || a.ModelElemCode2.ToLower().Contains(input.Text.ToLower())
              || a.ModelElemName2.ToLower().Contains(input.Text.ToLower())
              );
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemImageGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemImageDto> input)
        {
            foreach (var entity in input)
            {
                var b = await repository.GetAll().Where(a => a.ModelElemID == entity.ModelElemID && entity.ModelImageID == a.ModelImageID).WhereIf(entity.ModelElemID1.HasValue, a => a.ModelElemID1 == entity.ModelElemID1).WhereIf(entity.ModelElemID2.HasValue, a => a.ModelElemID2 == entity.ModelElemID2).AnyAsync();
                if (b)
                {
                    throw new UserFriendlyException("已存在重复项,请检查");
                }
                var oldentity = ObjectMapper.Map<ModelElemImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Clones(List<MOM_ModelElemImageDto> input)
        {
            foreach (var entity in input)
            {
                entity.Id = Guid.Empty;
                var dto = await repository.GetAll().Where(a => a.ModelImageID == entity.ModelImageID && a.ModelElemID == entity.ModelElemID).WhereIf(entity.ModelElemID1.HasValue, a => a.ModelElemID1.Value == entity.ModelElemID1.Value).WhereIf(entity.ModelElemID2.HasValue, a => a.ModelElemID2 == entity.ModelElemID2).FirstOrDefaultAsync();
                if (dto != null)
                {
                    continue;
                }

                var oldentity = ObjectMapper.Map<ModelElemImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemImageDto> input)
        {
            foreach (var entity in input)
            {
                var b = await repository.GetAll().Where(a => a.ModelElemID == entity.ModelElemID && entity.ModelImageID == a.ModelImageID).WhereIf(entity.Id.HasValue,a=>a.Id!=entity.Id.Value).WhereIf(entity.ModelElemID1.HasValue, a => a.ModelElemID1 == entity.ModelElemID1).WhereIf(entity.ModelElemID2.HasValue, a => a.ModelElemID2 == entity.ModelElemID2).AnyAsync();
                if (b)
                {
                    throw new UserFriendlyException("已存在重复项,请检查");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

# Simple Batch Analysis for Core Module Files
param(
    [int]$MaxFiles = 100
)

Write-Host "Starting simple batch analysis..." -ForegroundColor Green

# Load comparison results
$comparisonFile = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
$comparisonData = Get-Content $comparisonFile | ConvertFrom-Json
$bothExistFiles = $comparisonData.Files | Where-Object { $_.Status -eq "Both Exist" }

Write-Host "Found $($bothExistFiles.Count) files to analyze" -ForegroundColor Cyan
Write-Host "Processing first $MaxFiles files..." -ForegroundColor Yellow

$results = @()
$processed = 0

foreach ($file in $bothExistFiles) {
    if ($processed -ge $MaxFiles) { break }
    
    try {
        $sourceContent = Get-Content $file.SourcePath -Raw -Encoding UTF8
        $targetContent = Get-Content $file.TargetPath -Raw -Encoding UTF8
        
        $sourceLines = ($sourceContent -split "`n").Count
        $targetLines = ($targetContent -split "`n").Count
        
        # Check if identical except namespace
        $normalizedSource = $sourceContent -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"
        $isIdentical = ($normalizedSource -eq $targetContent)
        
        # Determine priority
        $priority = "Medium"
        if ($file.RelativePath -match "(Module|Config|Const|Permission|Authorization)" -or 
            $file.RelativePath -notmatch "\\") {
            $priority = "High"
        }
        
        $results += [PSCustomObject]@{
            FileName = $file.FileName
            RelativePath = $file.RelativePath
            SourceLines = $sourceLines
            TargetLines = $targetLines
            LineDiff = [Math]::Abs($sourceLines - $targetLines)
            IsIdentical = $isIdentical
            Priority = $priority
            Category = if ($isIdentical) { "NamespaceOnly" } 
                      elseif ($priority -eq "High") { "HighPriority" }
                      else { "NeedsReview" }
        }
        
        $processed++
        if ($processed % 20 -eq 0) {
            Write-Host "Processed $processed files..." -ForegroundColor White
        }
        
    } catch {
        Write-Host "Error processing $($file.RelativePath): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Generate statistics
$namespaceOnly = $results | Where-Object { $_.IsIdentical -eq $true }
$highPriority = $results | Where-Object { $_.Priority -eq "High" -and $_.IsIdentical -eq $false }
$needsReview = $results | Where-Object { $_.Priority -eq "Medium" -and $_.IsIdentical -eq $false }

Write-Host ""
Write-Host "=== Analysis Results ===" -ForegroundColor Green
Write-Host "Total processed: $($results.Count)" -ForegroundColor Cyan
Write-Host "Namespace only: $($namespaceOnly.Count)" -ForegroundColor Green
Write-Host "High priority: $($highPriority.Count)" -ForegroundColor Red
Write-Host "Needs review: $($needsReview.Count)" -ForegroundColor Yellow

# Save results
$outputData = @{
    Summary = @{
        TotalProcessed = $results.Count
        NamespaceOnly = $namespaceOnly.Count
        HighPriority = $highPriority.Count
        NeedsReview = $needsReview.Count
        GeneratedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    NamespaceOnlyFiles = $namespaceOnly | Sort-Object RelativePath
    HighPriorityFiles = $highPriority | Sort-Object LineDiff -Descending
    NeedsReviewFiles = $needsReview | Sort-Object LineDiff -Descending | Select-Object -First 30
}

$outputPath = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\SimpleBatchAnalysis.json"
$outputData | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host ""
Write-Host "Results saved to: $outputPath" -ForegroundColor Green

# Show some examples
Write-Host ""
Write-Host "Sample namespace-only files:" -ForegroundColor Green
$namespaceOnly | Select-Object -First 5 | ForEach-Object {
    Write-Host "  $($_.RelativePath)" -ForegroundColor White
}

Write-Host ""
Write-Host "Sample high-priority files:" -ForegroundColor Red
$highPriority | Select-Object -First 5 | ForEach-Object {
    Write-Host "  $($_.RelativePath) (Line diff: $($_.LineDiff))" -ForegroundColor White
}

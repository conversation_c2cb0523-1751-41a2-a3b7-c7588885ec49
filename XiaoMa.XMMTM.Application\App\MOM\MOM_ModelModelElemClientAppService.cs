﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemClientAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/12/2/星期三 9:52:20
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelModelElemClientAppService : XMMTMAppServiceBase, IMOM_ModelModelElemClientAppService
    {
        private readonly IRepository<ModelModelElemClient, Guid> repository;
        private readonly IRepository<ModelModelElem, Guid> modelModelElemRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ClientModel, Guid> clientModelRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelModelElemClientAppService(
       IRepository<ModelModelElemClient, Guid> repository,
       IRepository<ModelModelElem, Guid> modelModelElemRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ClientModel, Guid> clientModelRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Group, Guid> groupRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<Item, Guid> itemRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelModelElemRepository = modelModelElemRepository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.clientModelRepository = clientModelRepository;
            this.modelRepository = modelRepository;
            this.groupRepository = groupRepository;
            this.clientRepository = clientRepository;
            this.itemRepository = itemRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelModelElemClientGetAllOutputDto>> Get(MOM_ModelModelElemClientGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelRepository.GetAll() on t.ModelID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in groupRepository.GetAll() on t2.GroupID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in clientRepository.GetAll() on t.ClientID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t6x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                         join t7x in itemRepository.GetAll() on t.ItemID equals t7x.Id into t7xx
                         from t7 in t7xx.DefaultIfEmpty()
                         select new MOM_ModelModelElemClientGetAllOutputDto
                         {
                             IsActive = t.IsActive,
                             ClientID = t.ClientID,
                             ModelID = t.ModelID,
                             ModelElemID = t.ModelElemID,
                             State = t.State,
                             ClientText = t4.ShortName,
                             GroupID = t2.GroupID,
                             GroupText = t3.CodeName,
                             ModelText = t2.CodeName,
                             Id = t.Id,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             //CreateOn1 = t.CreateOn,
                             ModelElemName = t1.CodeName,
                             ModelElemCode = t1.Code,
                             ModelElemListCodeName = t6.CodeName,
                             ModelElemListCode = t6.Code,
                             ModelElemListID = t6.Id,
                             StateText = t.State.GetDescription(),
                             ItemID = t.ItemID,
                             ItemCode = t7.Code,
                             ItemName = t7.CodeName,
                             ItemOriginalItemNo = t7.OriginalItemNo,
                         }).WhereIf(input.ModelElemID.HasValue, a => a.ModelElemID == input.ModelElemID.Value)
                                 .WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID.Value)
                                 .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
                                 .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                                 .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ClientText.ToLower().Contains(input.Text.ToLower().Trim()) || a.ModelText.ToLower().Contains(input.Text.ToLower().Trim()) || a.ModelElemCode.ToLower().Contains(input.Text.ToLower()) || a.ModelElemName.ToLower().Contains(input.Text.ToLower()));

            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive).ThenBy(a => a.GroupText).ThenBy(a => a.ModelText).ThenBy(a => a.ModelElemListCode).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();

            return new PagedResultDto<MOM_ModelModelElemClientGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelModelElemClientDto> input)
        {

            foreach (var entity in input)
            {
                await ExistCodeAsync(entity);
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }


        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelModelElemClientDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelModelElemClientDto> input)
        {

            foreach (var entity in input)
            {
                await ExistCodeAsync(entity);
                var oldentity = ObjectMapper.Map<ModelModelElemClient>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        protected async Task ExistCodeAsync(MOM_ModelModelElemClientDto input)
        {
            var modeElemDto =await modelElemRepository.GetAsync(input.ModelElemID);
            if (modeElemDto == null)
            {
                throw new UserFriendlyException("款式明细不存在");
            }
            if(input.ItemID.HasValue&& !modeElemDto.IsInputItem)
            {
                throw new UserFriendlyException("款式明细不允许添加物料");
            }
            var modelelemlistid =await modelElemRepository.GetAll().Where(a => a.Id == input.ModelElemID).Select(a=>a.ModelElemListID).FirstOrDefaultAsync();
            var a = await (from t in repository.GetAll()
                           join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                           from t1 in t1xx.DefaultIfEmpty()
                           join t2x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t2x.Id into t2xx
                           from t2 in t2xx.DefaultIfEmpty()
                           where t1.ModelElemListID== modelelemlistid&&t.State== input .State&& t.ClientID == input.ClientID
                           select new
                           {
                               t.Id,
                               t.ModelID,
                               t.ClientID,
                               t.ModelElemID,
                               t.State,
                               t1.ModelElemListID
                           })
                           .WhereIf(input.Id.HasValue,a=>a.Id!=input.Id)
                          .WhereIf(input.ModelID.HasValue, a => a.ModelID.HasValue&&a.ModelID.Value == input.ModelID.Value)
                          .WhereIf(!input.ModelID.HasValue, a =>! a.ModelID.HasValue)
                          .CountAsync();

            if (a>=1)
            {
                throw new UserFriendlyException("同一款式下的款式明细已有默认项");
            }
            var b = await repository.GetAll().WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value).WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID).WhereIf(!input.ModelID.HasValue, a => !a.ModelID.HasValue).Where(a => a.ClientID == input.ClientID && a.ModelElemID == input.ModelElemID).AnyAsync();
            if (b)
            {
                throw new UserFriendlyException("已经存在相同项");
            }


        }
    }
}

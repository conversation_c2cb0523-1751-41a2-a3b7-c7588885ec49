﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:11
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelModelElemDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 选择的
        /// </summary>

        public bool Selected { get; set; }

        /// <summary>
        /// 版型ID
        /// </summary>

        public Guid ModelID { get; set; }

        /// <summary>
        /// 款式明细ID
        /// </summary>

        public Guid ModelElemID { get; set; }

        /// <summary>
        /// 默认
        /// </summary>

        public bool Default { get; set; }
        public virtual bool IsActive { set; get; } = true;
    }
}

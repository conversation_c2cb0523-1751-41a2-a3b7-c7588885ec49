/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    CAD_ETUpdateInputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/12/10/星期四 14:40:36
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;


namespace XiaoMa.XMMTM.App.CAD.Dtos
{
    public class CAD_ETUpdateInputDto: ETCADBase
    {
        /// <summary>
        /// SorderCadState ID
        /// </summary>
        public string Id { set; get; }
        /// <summary>
        /// 耗量
        /// </summary>
        public List<ConsumptionDto> Consumptions { set; get; }

    }

    public class ConsumptionDto
    {
        /// <summary>
        /// 床 MTM中当作面料ID$SorderDetailID拼接
        /// </summary>
        public Guid? Id { set; get; }
        /// <summary>
        /// 耗量
        /// </summary>
        public decimal Len { set; get; }
        public string Name { set; get; }
    }
}

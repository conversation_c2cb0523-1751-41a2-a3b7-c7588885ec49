﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelSizeColumnGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/7/星期五 17:10:21
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelSizeColumnGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelSizeColumnGetAllOutputDto 
    {

        public Guid? Id { set; get; }
        /// <summary>
        ///
        /// </summary>

        public Guid SizeColumnID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public bool IsRequired { get; set; }
        public bool IsActive { set; get; }
        /// <summary>
        ///
        /// </summary>

        public Guid ModelID { get; set; }
        public bool Selected { set; get; }

        public string SizeColumnName { set; get; }

        public string SizeColumnCode { set; get; }

        public DateTime CreateOn { set; get; }
        public int Sequence { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelBaseImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/8/星期六 10:19:14
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelBaseImageAppService : XMMTMAppServiceBase, IMOM_ModelBaseImageAppService
    {
        private readonly IRepository<ModelBaseImage, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelBase, Guid> modelBaseRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly FileServer.FileServer fileServer;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelBaseImageAppService(
       IRepository<ModelBaseImage, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelBase, Guid> modelBaseRepository,
       IRepository<ModelImage, Guid> modelImageRepository, FileServer.FileServer fileServer,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelBaseRepository = modelBaseRepository;
            this.modelImageRepository = modelImageRepository;
            this.fileServer = fileServer;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelBaseImageGetAllOutputDto>> Get(MOM_ModelBaseImageGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t2x in modelElemRepository.GetAll() on t.ModelElemID1 equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3 in modelBaseRepository.GetAll() on t.ModelBaseID equals t3.Id
                         join m in modelImageRepository.GetAll() on t.ModelImageID equals m.Id
                         select new MOM_ModelBaseImageGetAllOutputDto()
                         {
                             Id = t.Id,
                             Mix = t.Mix,
                             ImageSeq = t.ImageSeq,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelBaseID = t.ModelBaseID,
                             ModelElemID1 = t.ModelElemID1,
                             PositionID = t.PositionID,
                             ModelImageID = t.ModelImageID, //
#pragma warning disable CS0612 // “ModelBaseImage.ImageBac”已过时
#pragma warning disable CS0612 // “ModelBaseImage.ImageBac”已过时
                             ImageBac = t.ImageBac,
#pragma warning restore CS0612 // “ModelBaseImage.ImageBac”已过时
#pragma warning restore CS0612 // “ModelBaseImage.ImageBac”已过时
#pragma warning disable CS0612 // “ModelBaseImage.PositionBac”已过时
#pragma warning disable CS0612 // “ModelBaseImage.PositionBac”已过时
                             PositionBac = t.PositionBac,
#pragma warning restore CS0612 // “ModelBaseImage.PositionBac”已过时
#pragma warning restore CS0612 // “ModelBaseImage.PositionBac”已过时
                             //t1.PositionCode,
                             //t1.PositionName,
                             ModelElemCode1 = t2.Code,
                             ModelElemName1 = t2.CodeName,
                             ModelBaseCode = t3.Code,
                             ModelBaseName = t3.CodeName,
                             ModelImage = m.Code + ": " + m.CodeName,//
                             ImagePath = m.ImagePath, //
                             Position = m.PositionID.GetDescription(), // 
                             ImageUrl = fileServer.GetImageUrl(m.ImagePath),
                         })
                      .WhereIf(input.ModelBaseID.HasValue, a => a.ModelBaseID == input.ModelBaseID.Value)
                      .WhereIf(input.PositionID.HasValue, a => a.PositionID == input.PositionID.Value)
                      .WhereIf(input.ModelElemID1.HasValue, a => a.ModelElemID1 == input.ModelElemID1.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelBaseCode.ToLower().Contains(input.Text.ToLower()) || a.ModelBaseName.ToLower().Contains(input.Text.ToLower()) || a.ModelElemCode1.ToLower().Contains(input.Text.ToLower()) || a.ModelElemName1.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelBaseImageGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelBaseImageDto> input)
        {
            foreach (var entity in input)
            {

                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的数据");
                }
                var oldentity = ObjectMapper.Map<ModelBaseImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Clones(List<MOM_ModelBaseImageDto> input)
        {
            foreach (var entity in input)
            {
                entity.Id = Guid.Empty;
                var dto = await repository.GetAll().Where(a => a.ModelBaseID == entity.ModelBaseID && a.ModelImageID == entity.ModelImageID).WhereIf(entity.ModelElemID1.HasValue, a => a.ModelElemID1.Value == entity.ModelElemID1.Value).FirstOrDefaultAsync();
                if (dto != null)
                {
                    continue;
                }

                var oldentity = ObjectMapper.Map<ModelBaseImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelBaseImageDto> input)
        {
            foreach (var entity in input)
            {

                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelBaseImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        protected async Task<bool> ExistCodeAsync(MOM_ModelBaseImageDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().Where(a=>a.ModelBaseID == input.ModelBaseID && a.ModelImageID == input.ModelImageID).WhereIf(input.ModelElemID1.HasValue, a => a.ModelElemID1.Value == input.ModelElemID1.Value).AnyAsync();
            }
            else
            {
                return await repository.GetAll().Where(a => a.ModelBaseID == input.ModelBaseID && a.ModelImageID == input.ModelImageID && a.Id != input.Id.Value).WhereIf(input.ModelElemID1.HasValue, a => a.ModelElemID1.Value == input.ModelElemID1.Value).AnyAsync();
            }

        }

    }
}

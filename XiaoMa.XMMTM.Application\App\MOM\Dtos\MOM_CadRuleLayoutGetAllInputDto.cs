﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_CadRuleLayoutGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/6/星期四 15:59:35
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_CadRuleLayoutGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_CadRuleLayoutGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? CadRuleID { set; get; }
        public Guid? CadLayoutID { set; get; }
        public Guid? ModelElemID { set; get; }
        public Guid? GroupID { set; get; }

    }
}

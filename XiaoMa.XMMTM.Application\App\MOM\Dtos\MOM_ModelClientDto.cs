﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelClientDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/7/星期五 20:29:39
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelClientDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelClientDto : EntityDto<Guid?>
    {
        public Guid ModelID { set; get; }

        public Guid ClientID { set; get; }
        public string ClientName { set; get; }
        public string ClientCode { set; get; }
        public bool Selected { set; get; }
        public DateTime CreateOn { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/1/11/星期一 11:12:10
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.FileServer;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ItemImageAppService : XMMTMAppServiceBase, IBAD_ItemImageAppService
    {
        private readonly IRepository<ItemImage, Guid> repository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IObjectMapper objectMapper;
        private readonly IFileServer fileServer;

        public IRepository<Item, Guid> ItemRepository { get; }

        public BAD_ItemImageAppService(
       IRepository<ItemImage, Guid> repository,
       IRepository<Item, Guid> itemRepository,
       IObjectMapper objectMapper, IFileServer fileServer
         )
        {
            this.repository = repository;
            this.itemRepository = itemRepository;
            this.objectMapper = objectMapper;
            this.fileServer = fileServer;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemImageGetAllOutputDto>> Get(BAD_ItemImageGetAllInputDto input)
        {
            var query = from t in repository.GetAll()
                        where t.ItemID == input.ItemID
                        select new BAD_ItemImageGetAllOutputDto()
                        {
                            ItemID = t.ItemID,
                            Remark = t.Remark,
                            IsActive = t.IsActive,
                            CreateBy = t.CreateBy,
                            CreateID = t.CreateID,
                            CreateOn = t.CreateOn,
                            Id = t.Id,
                            ImageUrl = t.ImageUrl,
                            Url = fileServer.GetImageUrl(t.ImageUrl),
                            Name = fileServer.GetImageUrl(t.ImageUrl),
                        };
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<BAD_ItemImageGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                fileServer.RemoveImage(oldentity.ImageUrl);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemImageDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.ItemID.Equals(input.ItemID));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.ItemID.Equals(input.ItemID) && a.Id != input.Id.Value);
            }
        }

        public async Task Adds(IFormFile file, Guid ItemID)
        {
            //删除旧的图片
            var oldentitys = await repository.GetAll().Where(a => a.ItemID == ItemID).ToListAsync();
            if (oldentitys.Any())
            {
                foreach (var oldentity in oldentitys)
                {
                    fileServer.RemoveImage(oldentity.ImageUrl);
                    await repository.DeleteAsync(oldentity);
                }
            }
            var imagesId = Guid.NewGuid();
            var path = await fileServer.SaveImage(file, SystemConfig.SYS_ModelImageType.ItemImage, null, imagesId);
            await repository.InsertAsync(new ItemImage() { ItemID = ItemID, Id = imagesId, IsActive = true, Url = path, ImageUrl = path });
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<List<ItemImagesByIdOutputDto>> GetByItemIDs(ItemImagesByIdGetInputDto input)
        {
            if (!input.IDs.Any())
            {
                throw new UserFriendlyException("物料ID不存在");
            }
            var ids = input.IDs.Distinct().ToList();
            var query = (from t in repository.GetAll()
                         join t1x in itemRepository.GetAll() on t.ItemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         where ids.Contains(t.ItemID) && t.IsActive
                         select new ItemImagesByIdOutputDto
                         {
                             ItemID = t.ItemID,
                             ItemCode = t1.Code,
                             ItemName = t1.CodeName,
                             ImageUrl = fileServer.GetImageUrl(t.ImageUrl),
                         });
            var list = await query.ToListAsync();
            return list;
        }
    }
}

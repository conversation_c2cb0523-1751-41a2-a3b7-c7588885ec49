﻿#region << 版 本 注 释 >>
/*----------------------------------------------------------------
* 项目名称 ：XiaoMa.XMMTM.App.MOM
* 类 名 称 ：MOM_SizeColumnPriceAppService
* 类 描 述 ：
* 命名空间 ：XiaoMa.XMMTM.App.MOM
* 作    者 ：SundayPC
* 创建时间 ：2023/2/22 14:20:14
* 版 本 号 ：v1.0.0.0
*******************************************************************
* Copyright @ SundayPC 2023. All rights reserved.
*******************************************************************
//----------------------------------------------------------------*/
#endregion
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.MOM
{
    public class MOM_SizeColumnPriceAppService : XMMTMAppServiceBase, IMOM_SizeColumnPriceAppService
    {
        private readonly IRepository<SizeColumnPrice, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeColumnPriceAppService(
       IRepository<SizeColumnPrice, Guid> repository,
           IRepository<Model, Guid> modelRepository,
           IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.clientRepository = clientRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeColumnPriceGetAllOutputDto>> Get(MOM_SizeColumnPriceGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in sizeColumnRepository.GetAll() on t.SizeColumnID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()

                         join t3x in modelRepository.GetAll() on t.ModelID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in clientRepository.GetAll() on t.ClientID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in groupRepository.GetAll() on t.GroupID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         select new MOM_SizeColumnPriceGetAllOutputDto()
                         {
                             IsActive = t.IsActive,
                             CreateOn = t.CreateOn,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             Id = t.Id,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             SizeColumnID = t.SizeColumnID,
                             ClientID = t.ClientID,
                             GroupID = t.GroupID,
                             ModelID = t.ModelID,
                             RepairPrice = t.RepairPrice,
                             Sort = t.Sort,
                             SizeColumnCode = t1.Code,
                             SizeColumnName = t1.CodeName,
                             ClientName = t4.ShortName,
                             ModelName = t3.CodeName,
                             GroupName = t5.CodeName

                         }
                       )
                       .WhereIf(input.ClientID.HasValue,a=>a.ClientID==input.ClientID.Value)
                       .WhereIf(input.ModelID.HasValue,a=>a.ModelID == input.ModelID.Value)
                       .WhereIf(input.GroupID.HasValue,a=>a.GroupID == input.GroupID.Value)
                       .WhereIf(input.SizeColumnID.HasValue,a=>a.SizeColumnID == input.SizeColumnID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.SizeColumnCode.Contains(input.Text) || a.SizeColumnName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sort).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_SizeColumnPriceGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeColumnPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<SizeColumnPrice>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeColumnPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeColumnPriceDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = await (from t in repository.GetAll()/*.Include(a => a.BAD_ProductionSeries)*/
                                       where t.Id == entity.Id.Value
                                       select t).FirstOrDefaultAsync();
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SizeColumnPriceDto input)
        {

            var any = await repository.GetAll()
                .WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID.Value)
                .WhereIf(!input.ModelID.HasValue, a => !a.ModelID.HasValue)
                .WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
                .WhereIf(!input.ClientID.HasValue, a => !a.ClientID.HasValue)
                .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                .WhereIf(!input.GroupID.HasValue, a => !a.GroupID.HasValue)
                .AnyAsync(a => a.SizeColumnID.Equals(input.SizeColumnID));
            return any;

        }
    }
}

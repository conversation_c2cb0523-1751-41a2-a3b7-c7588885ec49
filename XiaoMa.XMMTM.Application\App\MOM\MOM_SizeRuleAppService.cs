﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 9:44:04
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeRuleAppService : XMMTMAppServiceBase, IMOM_SizeRuleAppService
    {
        private readonly IRepository<SizeRule, Guid> repository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeRuleAppService(
       IRepository<SizeRule, Guid> repository,
       IRepository<Group, Guid> groupRepository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.groupRepository = groupRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeRuleGetAllOutputDto>> Get(MOM_SizeRuleGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in groupRepository.GetAll() on t.GroupID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in sizeColumnRepository.GetAll() on t.SizeColumnID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         //where t.IsActive
                         select new MOM_SizeRuleGetAllOutputDto()
                         {
                             GroupID = t.GroupID,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             FormulaGroupID = t.FormulaGroupID,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             MaxOperator = t.MaxOperator,
                             MaxValue = t.MaxValue,
                             MinOperator = t.MinOperator,
                             MinValue = t.MinValue,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             SizeColumnID = t.SizeColumnID,
                             FormulaText = t.FormulaGroupID.GetDescription(),
                             GroupText = t1.Code + ":" + t1.CodeName,
                             SizeColumnText = t2.Code + ":" + t2.CodeName,
                             SizeColumnCode = t2.Code,
                         })
                        .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                        .WhereIf(input.SizeColumnID.HasValue,a=>a.SizeColumnID==input.SizeColumnID.Value)
               .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.SizeColumnCode).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SizeRuleGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeRuleGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeRuleDto> input)
        {
            foreach (var entity in input)
            {
                //if (await this.ExistCodeAsync(entity))
                //{
                //    throw new UserFriendlyException("已经存在相同的编码");
                //}
                var oldentity = ObjectMapper.Map<SizeRule>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeRuleDto> input)
        {
            foreach (var entity in input)
            {
                //if (await this.ExistCodeAsync(entity))
                //{
                //    throw new UserFriendlyException("已经存在相同的编码");
                //}
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeRuleDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SizeRuleDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

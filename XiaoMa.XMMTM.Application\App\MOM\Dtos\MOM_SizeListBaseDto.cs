﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeListBaseDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/1/星期六 10:52:18
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeListBaseDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_SizeListBaseDto : EntityDto<Guid?>
    {
        public virtual bool IsActive { set; get; }
        /// <summary>
        ///
        /// </summary>

        public decimal? Stepa { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? Stepb { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? Stepc { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? Stepd { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? Value { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? MinValue { get; set; }

        /// <summary>
        ///
        /// </summary>

        public decimal? MaxValue { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid SizeListID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid SizeColumnID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? SizeElemaID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? SizeElembID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? SizeElemcID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid? SizeElemdID { get; set; }
        public AlgorithmTypeEnums? AlgorithmType { set; get; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientItemGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2021/6/30/星期三 16:51:31
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientItemGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ClientItemGetAllOutputDto : ClientItem
    {
        public string ItemCode { set; get; }
        public string OriginalItemNo { set; get; }
    }
}

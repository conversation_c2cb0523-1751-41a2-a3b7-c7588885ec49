/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/28/星期二 16:20:00
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemListAppService : XMMTMAppServiceBase, IMOM_ModelElemListAppService
    {
        private readonly IRepository<ModelElemList, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemImage, Guid> modelElemImageRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<ModelElemBase, Guid> modelElemBaseRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemListAppService(
       IRepository<ModelElemList, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemImage, Guid> modelElemImageRepository,
          IRepository<Group, Guid> groupRepository,
          IRepository<ModelElemBase, Guid> modelElemBaseRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemImageRepository = modelElemImageRepository;
            this.groupRepository = groupRepository;
            this.modelElemBaseRepository = modelElemBaseRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemListGetAllOutputDto>> Get(MOM_ModelElemListGetAllInputDto input)
        {
            var sql = from t in repository.GetAll().AsNoTracking()
                      join t1x in groupRepository.GetAll().AsNoTracking() on t.GroupID equals t1x.Id into t1xx
                      from t1 in t1xx.DefaultIfEmpty()
                      join t3x in modelElemBaseRepository.GetAll().AsNoTracking() on t.ModelElemBaseID equals t3x.Id into t3xx
                      from t3 in t3xx.DefaultIfEmpty()
                          //join t4x in db.CFG_ModelElemType on t.ModelElemTypeID equals t4x.ID into t4xx
                          //from t4 in t4xx.DefaultIfEmpty()
                          //join t5x in db.CFG_Gender on t.GenderID equals t5x.ID into t5xx
                          //from t5 in t5xx.DefaultIfEmpty()
                          //join t5x in db.CFG_Column on t.ColumnID equals t5x.ID into t5xx
                          //from t5 in t5xx.DefaultIfEmpty()
                      select new MOM_ModelElemListGetAllOutputDto
                      {
                          Id = t.Id,
                          Code = t.Code,
                          CodeName = t.CodeName,
                          CadSeq = t.CadSeq,
                          MarketSeq = t.MarketSeq,
                          ElemSeq = t.ElemSeq,
                          ElemShow = t.ElemShow,
                          ReportShow = t.ReportShow,//报表显示2
                          IsPlanShow = t.IsPlanShow,
                          IsPlanEdit = t.IsPlanEdit,
                          IsItem = t.IsItem,
                          IsInput = t.IsInput,
                          Remark = t.Remark,
                          CreateID = t.CreateID,
                          CreateBy = t.CreateBy,
                          CreateOn = t.CreateOn,
                          ModifyID = t.ModifyID,
                          ModifyBy = t.ModifyBy,
                          ModifyOn = t.ModifyOn,
                          ModelElemBaseID = t.ModelElemBaseID,
                          ModelElemTypeID = t.ModelElemTypeID,
                          GroupID = t.GroupID,
                          ReportShowName = (t.ReportShow == 0 ? "不显示" : (t.ReportShow == 1 ? "需要时显示" : "永远显示")),
                          //t.ColumnID,
                          GroupCode = t1.Code,
                          GroupName = t1.CodeName,
                          ModelElemBaseCode = t3.Code,
                          ModelElemBaseName = t3.CodeName,
                          ModelElemTypeCode = t.ModelElemTypeID.ToString(),
                          ModelElemTypeName = t.ModelElemTypeID.GetDescription(),
                          IsClientShow = t.IsClientShow,
                          SapPartsCode = t.SapPartsCode,
                          IscustomerEdit = t.IscustomerEdit,
                          IsCustomerShow = t.IsCustomerShow,
                          GenderID = t.GenderID,
                          Gender = t.GenderID ? "男" : "女",
                          IsPlus = t.IsPlus,
                          IsActive = t.IsActive,
                          IsRepair = t.IsRepair,
                          InaStyleIndex = t.InaStyleIndex,
                          InaStyleIndexText=t.InaStyleIndex.HasValue?t.InaStyleIndex.GetDescription():"",

                      };
            if (input.CadSeq.HasValue)
            {
                if (input.CadSeq.Value)
                {
                    sql = sql.Where(a => a.CadSeq >= 0);
                }
                else
                {
                    sql = sql.Where(a => a.CadSeq == null);
                }
            }
            if (input.MarketSeq.HasValue)
            {
                if (input.MarketSeq.Value)
                {
                    sql = sql.Where(t => t.MarketSeq >= 0);
                }
                else
                {
                    sql = sql.Where(t => t.MarketSeq == null);
                }
            }
            if (input.ElemSeq.HasValue)
            {
                if (input.ElemSeq.Value)
                {
                    sql = sql.Where(t => t.ElemSeq >= 0);
                }
                else
                {
                    sql = sql.Where(t => t.ElemSeq == null);
                }
            }
            if (input.IsPlanShow.HasValue)
            {
                if (input.IsPlanShow.Value)
                {
                    sql = sql.Where(t => t.IsPlanShow == true);
                }
                else
                {
                    sql = sql.Where(t => t.IsPlanShow == false);
                }
            }



            if (input.IsItem.HasValue)
            {
                if (input.IsItem.Value)
                {
                    sql = sql.Where(t => t.IsItem == true);
                }
                else
                {
                    sql = sql.Where(t => t.IsItem == false);
                }
            }

            if (input.IsInput.HasValue)
            {
                if (input.IsInput.Value)
                {
                    sql = sql.Where(t => t.IsInput == true);
                }
                else
                {
                    sql = sql.Where(t => t.IsInput == false);
                }
            }
            sql = sql.WhereIf(input.GenderID.HasValue, a => a.GenderID == input.GenderID.Value)
                .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                .WhereIf(input.ModelElemBaseID.HasValue, a => a.ModelElemBaseID == input.ModelElemBaseID.Value)
                .WhereIf(input.ModelElemTypeID.HasValue, a => a.ModelElemTypeID == input.ModelElemTypeID.Value)
                .WhereIf(input.IsPlus.HasValue, a => a.IsPlus == input.IsPlus.Value)
                .WhereIf(input.InaStyleIndex.HasValue, a => a.InaStyleIndex == input.InaStyleIndex.Value)
                .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.Code.ToLower().Contains(input.Text) || a.CodeName.ToLower().Contains(input.Text));

            //var query = repository.GetAll()
            //  .Where(a => a.IsActive)
            //  .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await sql.CountAsync();
            var result = await sql.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelElemListGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemListGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelElemList>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemListDto> input)
        {
            foreach (var entity in input)
            {
                await checkModelElemImage(entity);
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelElemListDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }

        private async Task checkModelElemImage(MOM_ModelElemListDto input)
        {
            var modelElemIds = await modelElemRepository.GetAll().Where(a => a.ModelElemListID == input.Id).Select(a => a.Id).Distinct().ToListAsync();
            var query = await modelElemImageRepository.GetAll().Where(a => modelElemIds.Contains(a.ModelElemID)).Select(a => a.ModelElemID).ToListAsync();
            var query1 = await modelElemImageRepository.GetAll().Where(a => a.ModelElemID1.HasValue && modelElemIds.Contains(a.ModelElemID1.Value)).Select(a => a.ModelElemID1.Value).ToListAsync();
            var query2 = await modelElemImageRepository.GetAll().Where(a => a.ModelElemID2.HasValue && modelElemIds.Contains(a.ModelElemID2.Value)).Select(a => a.ModelElemID2.Value).ToListAsync();
            var elemIDs = (query.Union(query1).Union(query2)).Distinct();
            var anyElem = await modelElemRepository.GetAll().Where(a => elemIDs.Contains(a.Id)).Select(a => new { text = a.Code + ":" + a.CodeName }).Select(a => a.text).ToListAsync();
            if (anyElem.Count > 0)
            {
                var str = string.Join(",", anyElem);
                throw new UserFriendlyException($"删除失败,款式图中有引用此款式[{input.Code}:{input.CodeName}]下的款式明细:" + str);
            }
        }
    }
}

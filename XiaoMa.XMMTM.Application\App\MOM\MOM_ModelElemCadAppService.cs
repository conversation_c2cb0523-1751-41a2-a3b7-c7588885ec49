﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemCadAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:42
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.Timing;
using Abp.UI;
using Castle.MicroKernel.Registration;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 款式明细和CAD
    /// </summary>
    public class MOM_ModelElemCadAppService : XMMTMAppServiceBase, IMOM_ModelElemCadAppService
    {
        private readonly IRepository<ModelElemCad, Guid> repository;
        private readonly IObjectMapper objectMapper;

        private readonly IRepository<CadRule, Guid> cadRulerepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<Group, Guid> groupRepository;

        public MOM_ModelElemCadAppService(
       IRepository<ModelElemCad, Guid> repository,
       IRepository<CadRule, Guid> cadRulerepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<Group, Guid> groupRepository,

       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.cadRulerepository = cadRulerepository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemCadGetAllOutputDto>> Get(MOM_ModelElemCadGetAllInputDto input)
        {
            var query = (from t in repository.GetAll().AsNoTracking()
                         join t1 in cadRulerepository.GetAll().AsNoTracking() on t.CadRuleID equals t1.Id
                         join t2 in modelElemRepository.GetAll().AsNoTracking() on t.ModelElemID equals t2.Id
                         join t3 in modelElemListRepository.GetAll().AsNoTracking() on t2.ModelElemListID equals t3.Id
                         join t4x in groupRepository.GetAll().AsNoTracking() on t3.GroupID equals t4x.Id into t4xx 
                         from t4 in t4xx.DefaultIfEmpty()
                         where t1.IsActive
                         select new MOM_ModelElemCadGetAllOutputDto()
                         {
                             IsActive = t.IsActive,
                             Id = t.Id,
                             CadRuleID = t.CadRuleID,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             ModelElemID = t.ModelElemID,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Remark = t.Remark,
                             CadRuleText = t1.Code + ": " + t1.CodeName,
                             ModelElemText = t2.Code + ": " + t2.CodeName,
                             ModelElemListText = t3.Code + ": " + t3.CodeName,
                             CadSeq = t3.CadSeq,
                             GroupID = t3.GroupID,
                             GroupText = t4.Code + ":" + t4.CodeName,
                              ModelElemListID=t2.ModelElemListID

                         })
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                         .WhereIf(input.ModelElemID.HasValue, a => a.ModelElemID == input.ModelElemID.Value)
                         .WhereIf(input.CadRuleID.HasValue, a => a.CadRuleID == input.CadRuleID.Value)
                              .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text)||a.ModelElemText.ToLower().Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CadSeq).ThenBy(a => a.Code).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemCadGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemCadGetAllOutputDto2>> GetByModelElemID(MOM_ModelElemCadGetAllInputDto2 input)
        {
            Guid? modelElemGroupID = Guid.Empty;
            // 根据款式明细ID，获取该款式的物料组
            if (input.Id != Guid.Empty)
            {
                modelElemGroupID = modelElemRepository.GetAll().Where(p => p.Id == input.Id).Include("MOM_ModelElemList").Select(p => p.MOM_ModelElemList).FirstOrDefault().GroupID; 
            }
            var query = (from t in cadRulerepository.GetAll()
                         join t1x in repository.GetAll().Where(a => a.ModelElemID == input.Id) on t.Id equals t1x.CadRuleID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         where t.IsActive
                         select new MOM_ModelElemCadGetAllOutputDto2()
                         {
                             GroupID = t.GroupID,
                             CadRuleCode = t.Code,
                             CadRuleName = t.CodeName,
                             Selected = t1 == null ? false : true, //虚拟字段， 用于指定是否被选中
                             Id = t1 == null ? Guid.Empty : t1.Id,
                             Code = t1.Code,
                             CodeName = t1.CodeName,
                             Remark = t1.Remark,
                             CreateID = t1 == null ? null : t1.CreateID,
                             CreateBy = t1 == null ? null : t1.CreateBy,
                             CreateOn = t1 == null ? Clock.Now : t1.CreateOn,
                             ModifyID = t1 == null ? null : t1.ModifyID,
                             ModifyBy = t1 == null ? null : t1.ModifyBy,
                             ModifyOn = t1 == null ? null : t1.ModifyOn,
                             CadRuleID = t.Id,
                             ModelElemID = input.Id,
                             ModelElemCadCode = t1.Code,
                             ModelElemCadName = t1.CodeName
                         })
                           .WhereIf(modelElemGroupID.HasValue, a => a.GroupID == modelElemGroupID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemCadCode.Contains(input.Text) || a.ModelElemCadName.Contains(input.Text) || a.CadRuleCode.Contains(input.Text) || a.CadRuleName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CadRuleCode).ThenByDescending(a => a.Selected).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemCadGetAllOutputDto2>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemCadDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelElemCad>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task UpdatesByModelElemID(List<MOM_ModelElemCadDto> input)
        {
            var adds = input.Where(a => a.Id == Guid.Empty && a.Selected).ToList();
            var edits = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected).ToList();
            var dels = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected).ToList();
            foreach (var item in adds)
            {
                if (!string.IsNullOrEmpty(item.Code))
                {
                    var oldentity = ObjectMapper.Map<ModelElemCad>(item);
                    await repository.InsertAsync(oldentity);
                }
       
            }
            foreach (var entity in edits)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
            foreach (var item in dels)
            {
                await repository.DeleteAsync(item.Id.Value);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemCadDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelElemCadDto input)  
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code)&&a.CadRuleID==input.CadRuleID&&a.ModelElemID==input.ModelElemID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value && a.CadRuleID == input.CadRuleID && a.ModelElemID == input.ModelElemID);
            }

        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemCadDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task DeepCloneByModelElemID(DeepCloneByModelElemInputDto input)
        {
            var oldModelElemCads = await repository.GetAll().Where(a => a.ModelElemID == input.OldModelElemID).ToListAsync();
            if (input.NewModelElemID == Guid.Empty)
            {
                throw new UserFriendlyException("款式明细ID为空");
            }
            foreach (var item in oldModelElemCads)
            {
                item.Id = Guid.NewGuid();
                item.ModelElemID = input.NewModelElemID;
                item.CreateOn = DateTime.Now;
                item.CreateBy = this.SSOSession.Name;
                item.CreateID = this.SSOSession.UserId;
                await repository.InsertAsync(item);
            }
        }
    }
}

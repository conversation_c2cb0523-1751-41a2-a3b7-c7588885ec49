﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelImageGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:28
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelImageGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelImageGetAllOutputDto : ModelImage
    {
        public string ImageUrl { set; get; }
        public string ModelImageTypeText { set; get; }
        public string PositionText { set; get; }
        public string ModelText { set; get; }

#pragma warning disable CS0108 // “MOM_ModelImageGetAllOutputDto.xid”隐藏继承的成员“ModelImage.xid”。如果是有意隐藏，请使用关键字 new。
        public Guid xid { set; get; }
#pragma warning restore CS0108 // “MOM_ModelImageGetAllOutputDto.xid”隐藏继承的成员“ModelImage.xid”。如果是有意隐藏，请使用关键字 new。
    }
}

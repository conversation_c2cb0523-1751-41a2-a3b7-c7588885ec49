# Core Module File Comparison Script
param(
    [string]$SourcePath = "e:\workgit\xm\XiaoMa.MTM\XiaoMa.XMMTM.Core",
    [string]$TargetPath = "e:\workgit\Shop\XiaoMa.MTM\XiaoMa.Shop.XMMTM.Core"
)

Write-Host "Starting Core module comparison..." -ForegroundColor Green
Write-Host "Source path: $SourcePath" -ForegroundColor Yellow
Write-Host "Target path: $TargetPath" -ForegroundColor Yellow

# Check if paths exist
if (-not (Test-Path $SourcePath)) {
    Write-Host "Error: Source path does not exist - $SourcePath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $TargetPath)) {
    Write-Host "Error: Target path does not exist - $TargetPath" -ForegroundColor Red
    exit 1
}

# Get source files
Write-Host "Scanning source files..." -ForegroundColor Cyan
$sourceFiles = @()
Get-ChildItem -Path $SourcePath -Recurse -Filter "*.cs" | Where-Object {
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*"
} | ForEach-Object {
    $relativePath = $_.FullName.Replace($SourcePath, "").TrimStart('\')
    $sourceFiles += [PSCustomObject]@{
        Name         = $_.Name
        RelativePath = $relativePath
        FullPath     = $_.FullName
        Directory    = Split-Path $relativePath -Parent
    }
}

Write-Host "Source files count: $($sourceFiles.Count)" -ForegroundColor Green

# Get target files
Write-Host "Scanning target files..." -ForegroundColor Cyan
$targetFiles = @()
Get-ChildItem -Path $TargetPath -Recurse -Filter "*.cs" | Where-Object {
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*"
} | ForEach-Object {
    $relativePath = $_.FullName.Replace($TargetPath, "").TrimStart('\')
    $targetFiles += [PSCustomObject]@{
        Name         = $_.Name
        RelativePath = $relativePath
        FullPath     = $_.FullName
        Directory    = Split-Path $relativePath -Parent
    }
}

Write-Host "Target files count: $($targetFiles.Count)" -ForegroundColor Green

# 对比文件
Write-Host "开始对比文件..." -ForegroundColor Cyan
$comparisonResults = @()

foreach ($sourceFile in $sourceFiles) {
    $matchingTarget = $targetFiles | Where-Object { $_.RelativePath -eq $sourceFile.RelativePath }
    
    if ($matchingTarget) {
        # 文件存在于两个项目中
        $comparisonResults += [PSCustomObject]@{
            FileName     = $sourceFile.Name
            RelativePath = $sourceFile.RelativePath
            Directory    = $sourceFile.Directory
            Status       = "Both Exist"
            SourcePath   = $sourceFile.FullPath
            TargetPath   = $matchingTarget.FullPath
            Action       = "需要内容对比"
        }
    }
    else {
        # 文件只存在于源项目
        $comparisonResults += [PSCustomObject]@{
            FileName     = $sourceFile.Name
            RelativePath = $sourceFile.RelativePath
            Directory    = $sourceFile.Directory
            Status       = "Source Only"
            SourcePath   = $sourceFile.FullPath
            TargetPath   = "不存在"
            Action       = "需要复制到目标"
        }
    }
}

# 检查只存在于目标项目的文件
foreach ($targetFile in $targetFiles) {
    $matchingSource = $sourceFiles | Where-Object { $_.RelativePath -eq $targetFile.RelativePath }
    
    if (-not $matchingSource) {
        $comparisonResults += [PSCustomObject]@{
            FileName     = $targetFile.Name
            RelativePath = $targetFile.RelativePath
            Directory    = $targetFile.Directory
            Status       = "Target Only"
            SourcePath   = "不存在"
            TargetPath   = $targetFile.FullPath
            Action       = "目标项目独有"
        }
    }
}

# 统计结果
$bothExist = ($comparisonResults | Where-Object { $_.Status -eq "Both Exist" }).Count
$sourceOnly = ($comparisonResults | Where-Object { $_.Status -eq "Source Only" }).Count
$targetOnly = ($comparisonResults | Where-Object { $_.Status -eq "Target Only" }).Count

Write-Host ""
Write-Host "=== Core模块对比结果 ===" -ForegroundColor Green
Write-Host "两个项目都存在: $bothExist 个文件" -ForegroundColor Cyan
Write-Host "仅源项目存在: $sourceOnly 个文件" -ForegroundColor Yellow
Write-Host "仅目标项目存在: $targetOnly 个文件" -ForegroundColor Magenta
Write-Host "总计: $($comparisonResults.Count) 个文件" -ForegroundColor Green

# 保存结果
$outputPath = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
$result = @{
    Summary = @{
        TotalFiles  = $comparisonResults.Count
        BothExist   = $bothExist
        SourceOnly  = $sourceOnly
        TargetOnly  = $targetOnly
        GeneratedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    Files   = $comparisonResults
}

$result | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8
Write-Host ""
Write-Host "详细结果已保存到: $outputPath" -ForegroundColor Green

return $result

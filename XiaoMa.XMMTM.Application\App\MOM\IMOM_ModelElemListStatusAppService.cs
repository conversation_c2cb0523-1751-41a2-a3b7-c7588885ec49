﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IMOM_ModelElemListStatusAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/10/15/星期四 14:30:30
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.MOM.Dtos;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IMOM_ModelElemListStatusAppService : IApplicationService
    {
        Task<PagedResultDto<MOM_ModelElemListStatusGetAllOutputDto>> Get(MOM_ModelElemListStatusGetAllInputDto input);
        Task Adds(List<MOM_ModelElemListStatusDto> input);
        Task Updates(List<MOM_ModelElemListStatusDto> input);
        Task Deletes(List<MOM_ModelElemListStatusDto> input);
        /// <summary>
        /// 更加款式ID以及订单SorderDetailModelID过去款式明细验证必输集合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ModelElemListStatusOutputDto>> GetModelElemListStatus(ModelElemListStatusInputDto input);
    }
}

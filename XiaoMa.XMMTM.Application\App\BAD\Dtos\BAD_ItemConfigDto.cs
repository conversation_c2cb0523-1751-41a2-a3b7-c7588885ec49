/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemConfigDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/10/26/星期一 9:10:37
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemConfigDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemConfigDto : EntityDto<Guid?>
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Sequence { set; get; }
        public bool? HasImages { set; get; }
        /// <summary>
        /// 幅宽必填
        /// </summary>
        public bool? WidthIsRequired { set; get; }

        public BAD_ItemConfigBase ItemConfigBase { set; get; }

        public bool IsActive { set; get; }
    }
}

{"RelativePath": "XMMTMCoreModule.cs", "SourceFile": "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\XMMTMCoreModule.cs", "TargetFile": "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\XMMTMCoreModule.cs", "SourceLines": 72, "TargetLines": 102, "TotalDifferences": 90, "NamespaceOnlyDiffs": null, "ContentDiffs": 89, "SourceNamespace": "namespace XiaoMa.XMMTM", "TargetNamespace": "namespace XiaoMa.Shop.XMMTM\r", "MissingUsingsInTarget": ["using XiaoMa.Zero.Configuration;", "using XiaoMa.XMMTM.Authorization.Users;", "using XiaoMa.XMMTM.Configuration;", "using XiaoMa.XMMTM.Localization;", "using XiaoMa.XMMTM.Timing;", "using XiaoMa.XMMTM.SorderManager;", "using XiaoMa.XMMTM.CAD;", "using XiaoMa.XMMTM.SizeCheck;", "using XiaoMa.XMMTM.Http.Api;", "using XiaoMa.XMMTM.ModelElemRuleManager;", "using XiaoMa.XMMTM.UPS;", "using XiaoMa.XMMTM.SystemConfig;", "using XiaoMa.XMMTM.SizeCheck.New;", "using XiaoMa.XMMTM.SizeCheck.Plus;", "using XiaoMa.XMMTM.Pdf;"], "ExtraUsingsInTarget": ["using Abp.Localization;", "using Abp<PERSON>;", "using Abp.Quartz.Configuration;", "using Microsoft.Extensions.Hosting;", "using Quartz;", "using Quartz.Impl;", "using XiaoMa.Shop.XMMTM.CAD;", "using XiaoMa.Shop.XMMTM.Configuration;", "using XiaoMa.Shop.XMMTM.Excel;", "using XiaoMa.Shop.XMMTM.Http.Api;", "using XiaoMa.Shop.XMMTM.Localization;", "using XiaoMa.Shop.XMMTM.Manager.BAD;", "using XiaoMa.Shop.XMMTM.Manager.ODM;", "using XiaoMa.Shop.XMMTM.ModelElemRuleManager;", "using XiaoMa.Shop.XMMTM.Pdf;", "using XiaoMa.Shop.XMMTM.SizeCheck;", "using XiaoMa.Shop.XMMTM.SorderManager;", "using XiaoMa.Shop.XMMTM.SuitSupply;", "using XiaoMa.Shop.XMMTM.SystemConfig;", "using XiaoMa.Shop.XMMTM.Timing;", "using XiaoMa.Shop.XMMTM.UPS;", "using XiaoMa.ZeroCore.Redis;"], "Differences": [{"LineNumber": 1, "SourceLine": "using Abp.<PERSON>;", "TargetLine": "using Abp.Dependency;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 2, "SourceLine": "using Abp.Reflection.Extensions;", "TargetLine": "using Abp.Localization;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 3, "SourceLine": "using Abp.Tim<PERSON>;", "TargetLine": "using Abp.<PERSON>;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 4, "SourceLine": "using XiaoMa.Zero;", "TargetLine": "using Abp<PERSON>;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 5, "SourceLine": "using XiaoMa.Zero.Configuration;", "TargetLine": "using Abp.Quartz.Configuration;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 6, "SourceLine": "using XiaoMa.XMMTM.Authorization.Users;", "TargetLine": "using Abp.Reflection.Extensions;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 7, "SourceLine": "using XiaoMa.XMMTM.Configuration;", "TargetLine": "using Abp.Tim<PERSON>;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 8, "SourceLine": "using XiaoMa.XMMTM.Localization;", "TargetLine": "using Microsoft.Extensions.Hosting;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 9, "SourceLine": "using XiaoMa.XMMTM.Timing;", "TargetLine": "using Quartz;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 10, "SourceLine": "using XiaoMa.XMMTM.SorderManager;", "TargetLine": "using Quartz.Impl;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 11, "SourceLine": "using XiaoMa.XMMTM.CAD;", "TargetLine": "using XiaoMa.Shop.XMMTM.CAD;", "IsNamespaceOnly": true, "DiffType": "Namespace"}, {"LineNumber": 12, "SourceLine": "using XiaoMa.XMMTM.SizeCheck;", "TargetLine": "using XiaoMa.Shop.XMMTM.Configuration;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 13, "SourceLine": "using XiaoMa.XMMTM.Http.Api;", "TargetLine": "using XiaoMa.Shop.XMMTM.Excel;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 14, "SourceLine": "using XiaoMa.XMMTM.ModelElemRuleManager;", "TargetLine": "using XiaoMa.Shop.XMMTM.Http.Api;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 15, "SourceLine": "using XiaoMa.XMMTM.Excel;", "TargetLine": "using XiaoMa.Shop.XMMTM.Localization;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 16, "SourceLine": "using XiaoMa.XMMTM.UPS;", "TargetLine": "using XiaoMa.Shop.XMMTM.Manager.BAD;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 17, "SourceLine": "using XiaoMa.XMMTM.SystemConfig;", "TargetLine": "using XiaoMa.Shop.XMMTM.Manager.ODM;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 18, "SourceLine": "using XiaoMa.XMMTM.SizeCheck.New;", "TargetLine": "using XiaoMa.Shop.XMMTM.ModelElemRuleManager;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 19, "SourceLine": "using XiaoMa.XMMTM.SizeCheck.Plus;", "TargetLine": "using XiaoMa.Shop.XMMTM.Pdf;", "IsNamespaceOnly": false, "DiffType": "Content"}, {"LineNumber": 20, "SourceLine": "using XiaoMa.XMMTM.Pdf;", "TargetLine": "using XiaoMa.Shop.XMMTM.SizeCheck;", "IsNamespaceOnly": false, "DiffType": "Content"}], "IsIdenticalExceptNamespace": false}
﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientModelClassGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2021/3/2/星期二 14:41:18
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientModelClassGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ClientModelClassGetAllOutputDto : ClientModelClass
    {
        public bool IsChecked { set; get; }
        public string Code { set; get; }
        public string CodeName { set; get; }

        public int? ModelCount { set;get; }
    }
}

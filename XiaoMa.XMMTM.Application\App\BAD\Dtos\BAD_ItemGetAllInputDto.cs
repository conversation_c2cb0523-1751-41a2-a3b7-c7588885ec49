/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ItemGetAllOutputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/7/23/星期四 10:36:11
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    public class BAD_ItemGetAllInputDto : PagedInput
    {
        /// <summary>
        /// 面辅料类型
        /// </summary>
        public BAD_ItemClass? ItemClassID{ set;get;}
        /// <summary>
        /// 业务归属
        /// </summary>
        public Guid ? BusinessGroupID { set; get; }
        /// <summary>
        /// 工艺属性
        /// </summary>
        public Guid ? TechnologyGroupID { set; get; }
        /// <summary>
        /// 单位分类
        /// </summary>
        public Guid ? UnitGroupID { set; get; }
        /// <summary>
        /// 纹理分类
        /// </summary>
        public Guid ? TextureGroupID { set; get; }
        /// <summary>
        /// 物料分类名称
        /// </summary>
        public Guid ? ItemGroupID { set; get; }
        /// <summary>
        /// 季节分类
        /// </summary>
        public Guid? SeasonGroupID { set; get; }
        public Guid? ClientID { set; get; }
        public bool? IsClientItem { set; get; }
        public bool? NotImage { set; get; }


    }
}

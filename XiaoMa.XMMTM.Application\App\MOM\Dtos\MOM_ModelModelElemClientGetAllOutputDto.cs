﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemClientGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/12/2/星期三 9:52:55
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemClientGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelModelElemClientGetAllOutputDto : ModelModelElemClient
    {
        public Guid GroupID { set; get; }
        public string ModelText { set; get; }
        public string ClientText { set; get; }
        public string GroupText { set; get; }

        public string ItemCode { set; get; }
        public string ItemName { set; get; }
        public string ItemOriginalItemNo { set; get; }
        public string ModelElemListCode { set; get; }

        public Guid ModelElemListID { set; get; }
        public string ModelElemListCodeName { set; get; }
        public new Guid? Id { set; get; }
        public DateTime CreateOn1 { set; get; }

        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public string StateText { set; get; }

    }
}

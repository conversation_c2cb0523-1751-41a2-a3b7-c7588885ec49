﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemRuleDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/11/17/星期二 20:34:18
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemRuleDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelElemRuleDto : EntityDto<Guid?>
    {
        /// <summary>
        /// 款式ID
        /// </summary>

        public Guid ModelElemListID { get; set; }

        /// <summary>
        /// 条件字符串
        /// </summary>

        public string ConditionStr { get; set; }

        /// <summary>
        /// 运算公式字符串
        /// </summary>

        public string OperationString { get; set; }

        /// <summary>
        /// 备注
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        /// 公式类别
        /// </summary>

        public RuleTypeEnum BomRuleType { get; set; }

        /// <summary>
        /// 排序 内部顺序  同一个款式不同公司
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 外部排序   大的外部顺序
        /// </summary>
        public int ExternalSort { get; set; }

        /// <summary>
        /// 关联款式明细
        /// </summary>
        public Guid? OtherElemID { get; set; }
        public bool IsActive { set; get; }

        /// <summary>
        /// 支持算法 MTM 大货
        /// </summary>
        public ModelRuleTypeEnums ModelRuleType { get; set; }
    }
}

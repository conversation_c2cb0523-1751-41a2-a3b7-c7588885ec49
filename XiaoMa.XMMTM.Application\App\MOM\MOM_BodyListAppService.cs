﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_BodyListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 13:34:55
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_BodyListAppService : XMMTMAppServiceBase, IMOM_BodyListAppService
    {
        private readonly IRepository<BodyList, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public MOM_BodyListAppService(
       IRepository<BodyList, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_BodyListGetAllOutputDto>> Get(MOM_BodyListGetAllInputDto input)
        {
            var query = repository.GetAll()
              //.Where(a => a.IsActive)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sequence).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<MOM_BodyListGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_BodyListGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_BodyListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<BodyList>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_BodyListDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_BodyListDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_BodyListDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

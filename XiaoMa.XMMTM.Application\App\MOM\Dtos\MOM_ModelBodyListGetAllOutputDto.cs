﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelBodyListGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/7/星期五 17:10:42
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelBodyListGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelBodyListGetAllOutputDto 
    {
        public string BodyListName { set; get; }
        public string BodyListCode { set; get; }

        public bool Selected { set; get; }
        public bool IsActive { set; get; }

        public Guid BodyListID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public Guid ModelID { get; set; }

        public Guid ? Id { set; get; }

        public DateTime CreateOn { set; get; }
    }
}

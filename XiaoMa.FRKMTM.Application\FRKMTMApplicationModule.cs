﻿using Abp.AutoMapper;
using Abp.Modules;
using Abp.Reflection.Extensions;
using XiaoMa.XMMTM;
using XiaoMa.XMMTM.Authorization;

namespace XiaoMa.FRKMTM
{
    /// <summary>
    /// 业务
    /// </summary>
    [DependsOn(
        typeof(XMMTMCoreModule), 
        typeof(AbpAutoMapperModule))]
    public class FRKMTMApplicationModule : AbpModule
    {
        /// <summary>
        /// 
        /// </summary>
        public override void PreInitialize()
        {
            Configuration.Authorization.Providers.Add<XMMTMAuthorizationProvider>();
        }
        /// <summary>
        /// 
        /// </summary>
        public override void Initialize()
        {
            var thisAssembly = typeof(FRKMTMApplicationModule).GetAssembly();

            IocManager.RegisterAssemblyByConvention(thisAssembly);

            Configuration.Modules.AbpAutoMapper().Configurators.Add(
                // Scan the assembly for classes which inherit from AutoMapper.Profile
                cfg => cfg.AddMaps(thisAssembly)
            );
        }
    }
}

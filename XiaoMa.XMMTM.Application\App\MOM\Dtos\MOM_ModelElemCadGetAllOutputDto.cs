﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemCadGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 21:28:05
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemCadGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelElemCadGetAllOutputDto: ModelElemCad
    {
        //public bool Selected { set; get; }
        //public bool? Default { set; get; }


        //public string ModelElemCadCode { set; get; }
        //public string ModelElemCadName { set; get; }
        //public string CadRuleName { set; get; }
        public string CadRuleText { set; get; }
        public string ModelElemText { set; get; }
        public string ModelElemListText { set; get; }

        public int? CadSeq { set; get; }
        public Guid? GroupID { set; get; }
        public Guid? ModelElemListID { set; get; }
        public string  GroupText { set; get; }

    }
}

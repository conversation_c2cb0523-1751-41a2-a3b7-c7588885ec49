/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemConfigAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/10/26/星期一 9:10:22
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// Item配置项
    /// </summary>
    public class BAD_ItemConfigAppService : XMMTMAppServiceBase, IBAD_ItemConfigAppService
    {
        private readonly IRepository<ItemConfig, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public BAD_ItemConfigAppService(
       IRepository<ItemConfig, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemConfigGetAllOutputDto>> Get(BAD_ItemConfigGetAllInputDto input)
        {
            var query = repository.GetAll()
              //.Where(a => a.IsActive)
              .Select(a => new BAD_ItemConfigGetAllOutputDto()
              {
                  ItemConfigBase = a.ItemConfigBase,
                  Code = a.Code,
                  CodeName = a.CodeName,
                  CreateBy = a.CreateBy,
                  CreateID = a.CreateID,
                  CreateOn = a.CreateOn,
                  IsActive = a.IsActive,
                  Id = a.Id,
                  ModifyBy = a.ModifyBy,
                  ModifyID = a.ModifyID,
                  ModifyOn = a.ModifyOn,
                  Remark = a.Remark,
                  Sequence = a.Sequence,
                  HasImages = a.HasImages,
                  ItemConfigBaseText = a.ItemConfigBase.GetDescription(),
                  WidthIsRequired = a.WidthIsRequired,
              })
              .WhereIf(input.ItemConfigBaseID.HasValue, a => a.ItemConfigBase == input.ItemConfigBaseID)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.ItemConfigBase).ThenBy(a => a.Sequence).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<BAD_ItemConfigGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ItemConfigGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ItemConfig>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemConfigDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemConfigDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemConfigGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/12/28/星期一 10:20:15
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemElemItemConfigGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_ItemElemItemConfigGetAllOutputDto : ItemElemItemConfig
    {
        public string ItemClassText { set; get; }

        public string ModelElemCode { set; get; }

        public string ModelElemCodeName { set; get; }

        public string ModelElemListCode { set; get; }
        public string ModelElemListName { set; get; }

        public Guid? GroupID { set; get; }
        public string GroupText { set; get; }
    }
}

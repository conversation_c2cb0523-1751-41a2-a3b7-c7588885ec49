/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/27/星期一 14:06:13
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ItemDto : EntityDto<Guid?>
    {
        public bool IsActive { set; get; }
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        public int? Number { set; get; }
        /// <summary>
        /// 物料类别
        /// </summary>
        public BAD_ItemClass ItemClassID { set; get; }
        /// <summary>
        /// 物料类别
        /// </summary>
        public Guid? ItemTypeID { set; get; }
        /// <summary>
        /// 物料分类
        /// </summary>
        public Guid? ItemGroupID { set; get; }

        /// <summary>
        /// 业务归属
        /// </summary>
        public Guid? BusinessGroup { set; get; }

        /// <summary>
        /// 原始货号
        /// </summary>
        public string OriginalItemNo { set; get; }
        /// <summary>
        /// 色号
        /// </summary>
        public string ColorNo { set; get; }

        /// <summary>
        /// 年份
        /// </summary>
        public int? YearNo { set; get; }
        /// <summary>
        /// 透胶/光性
        /// </summary>
        public string Transparency { set; get; }

        /// <summary>
        /// 工艺属性
        /// </summary>
        public Guid? TechnologyGroupID { set; get; }


        /// <summary>
        /// 单位分类
        /// </summary>
        public Guid? UnitGroupID { set; get; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string ItemSize { set; get; }

        /// <summary>
        /// 纹理(CM)
        /// </summary>
        public Guid? TextureGroupID { set; get; }

        /// <summary>
        /// 门幅单位
        /// </summary>
        public decimal? Width { get; set; }

        /// <summary>
        /// 面料成分
        /// </summary>
        public string ItemComp { set; get; }


        /// <summary>
        /// 纱织
        /// </summary>

        public string Yarn { get; set; }


        /// <summary>
        /// 克重
        /// </summary>

        public decimal? Weight { get; set; }
        /// <summary>
        /// 纬向缩率
        /// </summary>

        public decimal? Shrink { get; set; } = 99;
        /// <summary>
        /// 经向缩率
        /// </summary>
        public decimal? LenShrink { set; get; }

        ///// <summary>
        ///// 供应商编码
        ///// </summary>
        //public string SupplierItemCode { get; set; }

        ///// <summary>
        ///// 供应商编码
        ///// </summary>
        //public string SupplierItemName { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public Guid? SupplierItemID { get; set; }

        /// <summary>
        /// 物料品牌
        /// </summary>
        public string ItemBrand { set; get; }

        /// <summary>
        /// 零售价格
        /// </summary>
        public decimal? RetailPrice { set; get; }

        /// <summary>
        /// 批发价格
        /// </summary>
        public decimal? WholesalePrice { set; get; }

        /// <summary>
        /// 库存数
        /// </summary>
        public decimal? InventoryQty { set; get; }

        /// <summary>
        /// 客户面料
        /// </summary>
        public Guid? ClientID { set; get; }

        /// <summary>
        /// 客户创建面料
        /// </summary>
        public Guid? CreateByClientID { set; get; }

        /// <summary>
        /// 图片是否在工艺单展示
        /// </summary>
        public bool IsElemShow { set; get; } = false;
        /// <summary>
        /// 销售单价
        /// </summary>
        public decimal? UnitSellingPrice { set; get; }

        public string PlaceOfOrigin { set; get; }
        /// <summary>
        /// 是否指定客户
        /// </summary>
        public bool? IsClientItem { set; get; }
        /// <summary>
        /// 物料裁剪属性,对折/平铺
        /// </summary>
        public ItemFoldTypeEnums? ItemFold { set; get; }

        /// <summary>
        /// 季节分类
        /// </summary>
        public Guid? SeasonGroupID { set; get; }
        /// <summary>
        /// 是否毛坯样面料
        /// </summary>
        public bool? IsRollItem { set; get; }
        /// <summary>
        /// 不参与账单
        /// </summary>
        public bool? NotInBill { set; get; }
    }
}

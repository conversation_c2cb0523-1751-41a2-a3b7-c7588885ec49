﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleDetailAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 9:46:20
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeRuleDetailAppService : XMMTMAppServiceBase, IMOM_SizeRuleDetailAppService
    {
        private readonly IRepository<SizeRuleDetail, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeRuleDetailAppService(
       IRepository<SizeRuleDetail, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeRuleDetailGetAllOutputDto>> Get(MOM_SizeRuleDetailGetAllInputDto input)
        {
            var query = repository.GetAll()
                .WhereIf(input.Id.HasValue,a=>a.SizeRuleID==input.Id.Value);
              //.Where(a => a.IsActive);
              //.WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<MOM_SizeRuleDetailGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeRuleDetailGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeRuleDetailDto> input)
        {
            foreach (var entity in input)
            {
               
                var oldentity = ObjectMapper.Map<SizeRuleDetail>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeRuleDetailDto> input)
        {
            foreach (var entity in input)
            {
                
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeRuleDetailDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        
    }
}

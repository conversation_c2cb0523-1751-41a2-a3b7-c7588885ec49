﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AssemblyName>XiaoMa.FRKMTM.Application</AssemblyName>
    <RootNamespace>XiaoMa.FRKMTM.Application</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>obj\Debug\netcoreapp3.1\XiaoMa.FRKMTM.Application.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\XiaoMa.XMMTM.Core\XiaoMa.XMMTM.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.EntityFrameworkCore" Version="5.5.0" />
    <PackageReference Include="Abp.AutoMapper" Version="5.5.0" />
    <PackageReference Include="EFCore.BulkExtensions" Version="3.1.6" />
  </ItemGroup>
</Project>

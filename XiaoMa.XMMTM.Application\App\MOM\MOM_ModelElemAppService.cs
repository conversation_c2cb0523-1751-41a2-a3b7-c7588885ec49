/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/28/星期二 16:20:38
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Domain.SYM;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemAppService : XMMTMAppServiceBase, IMOM_ModelElemAppService
    {
        private readonly IRepository<ModelElem, Guid> repository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<ItemConfig, Guid> itemConfigRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<ModelModelElem, Guid> modelModelElemRepository;
        private readonly IRepository<ModelModelElemClient, Guid> modelModelElemClientRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ModelElemBase, Guid> modelElemBaseRepository;
        private readonly IRepository<ModelElemItemGroup, Guid> modelElemItemGroupRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<ImportSorderModelElemDetail, Guid> importSorderModelElemDetailRepository;
        private readonly IObjectMapper objectMapper;


        public MOM_ModelElemAppService(
       IRepository<ModelElem, Guid> repository,
       IRepository<Item, Guid> itemRepository,
              IRepository<ItemConfig, Guid> itemConfigRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<ModelModelElem, Guid> modelModelElemRepository,
       IRepository<ModelModelElemClient, Guid> modelModelElemClientRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ModelElemBase, Guid> modelElemBaseRepository,
       IRepository<ModelElemItemGroup, Guid> modelElemItemGroupRepository,
       IRepository<Group, Guid> groupRepository,
       IRepository<ImportSorderModelElemDetail, Guid> importSorderModelElemDetailRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.itemRepository = itemRepository;
            this.itemConfigRepository = itemConfigRepository;
            this.modelRepository = modelRepository;
            this.modelModelElemRepository = modelModelElemRepository;
            this.modelModelElemClientRepository = modelModelElemClientRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.modelElemBaseRepository = modelElemBaseRepository;
            this.modelElemItemGroupRepository = modelElemItemGroupRepository;
            this.groupRepository = groupRepository;
            this.importSorderModelElemDetailRepository = importSorderModelElemDetailRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemGetAllOutputDto2>> Get(MOM_ModelElemGetAllInputDto2 input)
        {

            var query = (from t in repository.GetAll()
                         join t1 in modelElemListRepository.GetAll() on t.ModelElemListID equals t1.Id
                         join t2 in modelElemBaseRepository.GetAll() on t1.ModelElemBaseID equals t2.Id
                         join t4x in groupRepository.GetAll() on t1.GroupID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in itemRepository.GetAll() on t.ItemID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         select new MOM_ModelElemGetAllOutputDto2()
                         {
                             GroupID = t4.Id,
                             GroupText = t4.CodeName,
                             GenderID = t1.GenderID,
                             GenderText = t1.GenderID ? "男" : "女",
                             ModelElemTypeID = (int)t1.ModelElemTypeID,
                             ModelElemTypeText = t1.ModelElemTypeID.GetDescription(),
                             ModelElemBaseID = t2.Id,
                             ModelElemBaseText = t2.Code + ": " + t2.CodeName,
                             ModelElemListText = t1.Code + ": " + t1.CodeName,
                             Id = t.Id,
                             Code = t.Code,
                             CodeName = t.CodeName,
                             Default = t.Default,
                             Price = t.Price,
                             Qty = t.Qty,
                             Remark = t.Remark,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelElemListID = t.ModelElemListID,
                             //Count = t5 == null ? 0 : t5.Count,
                             IsItemAdd = t.IsItemAdd,
                             IsItemImageAdd = t.IsItemImageAdd,
                             IsActive = t.IsActive,
                             IsInput = t.IsInput,
                             IsInputItem = t.IsInputItem,
                             ItemID = t.ItemID,
                             Sort = t.Sort,
                             ItemCode = t5.Code,
                             ItemName = t5.CodeName,
                             OriginalItemNo = t5.OriginalItemNo,
                             IsItemElem = t.IsItemElem,
                             InaStyleCode = t.InaStyleCode,
                             InaStyleCodeText = t.InaStyleCode.HasValue ? t.InaStyleCode.Value.GetDescription() : null,

                         }
                   )
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                         .WhereIf(input.GenderID.HasValue, a => a.GenderID == input.GenderID.Value)
                         .WhereIf(input.ModelElemBaseID.HasValue, a => a.ModelElemBaseID == input.ModelElemBaseID.Value)
                         .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                         .WhereIf(input.InaStyleCode.HasValue, a => a.InaStyleCode == input.InaStyleCode.Value)
                         .WhereIf(input.ModelElemType.HasValue, a => a.ModelElemTypeID == input.ModelElemType.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.Sort).OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var result = await query.OrderByDescending(a => a.Sort).OrderByDescending(a => a.CreateOn).Skip(input.SkipCount).Take(input.MaxResultCount).ToListAsync();
            var modelelemids = result.Select(a => a.Id).Distinct().ToList();
            var _counts = await (from t in modelModelElemRepository.GetAll().AsNoTracking()
                                 join t1x in modelRepository.GetAll().AsNoTracking() on t.ModelID equals t1x.Id into t1xx
                                 from t1 in t1xx.DefaultIfEmpty()
                                 where t1 != null && modelelemids.Contains(t.ModelElemID)
                                 group t by t.ModelElemID into g
                                 select new
                                 {
                                     ModelElemID = g.Key,
                                     Count = g.Count(),
                                 }).ToListAsync();
            var modelElemItemGroups = await (from t in modelElemItemGroupRepository.GetAll()
                                             join t1x in itemConfigRepository.GetAll() on t.ItemGroupID equals t1x.Id into t1xx
                                             from t1 in t1xx.DefaultIfEmpty()
                                             where modelelemids.Contains(t.ModelElemID)
                                             select new
                                             {
                                                 t.ModelElemID,
                                                 t1.CodeName,
                                             }).ToListAsync();
            foreach (var item in result)
            {
                var modelelem = _counts.FirstOrDefault(a => a.ModelElemID == item.Id);
                if (modelelem != null)
                {
                    item.Count = modelelem.Count;
                }
                var itemGroup = modelElemItemGroups.Where(a => a.ModelElemID == item.Id).Select(a => a.CodeName).ToList();
                if (itemGroup.Any())
                {
                    item.ItemGroupText = string.Join(',', itemGroup);
                }
            }
            //var list = objectMapper.Map<List<MOM_ModelElemGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemGetAllOutputDto2>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ModelElem>(entity);
                var id = await repository.InsertAndGetIdAsync(oldentity);
                await this.updateModelElemItemGroup(id, entity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                await this.updateModelElemItemGroup(entity.Id.Value, entity);
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        /// <summary>
        /// 给款式明细添加物料分类
        /// </summary>
        /// <param name="modelElemId"></param>
        /// <param name="input">是否输入货号，如果否就清除掉所有分类</param>
        /// <returns></returns>
        private async Task updateModelElemItemGroup(Guid modelElemId, MOM_ModelElemDto input)
        {
            var olds = await modelElemItemGroupRepository.GetAll().Where(a => a.ModelElemID == modelElemId).ToListAsync();
            foreach (var item in olds)
            {
                await modelElemItemGroupRepository.DeleteAsync(item);
            }
            if (input.IsInputItem && (input.ItemGroupIDs == null || !input.ItemGroupIDs.Any()))
            {
                throw new UserFriendlyException("当允许选择物料时，请绑定物料分类！");
            }
            if (input.ItemGroupIDs != null && input.ItemGroupIDs.Any() && input.IsInputItem)
            {
                foreach (var item in input.ItemGroupIDs)
                {
                    await modelElemItemGroupRepository.InsertAsync(new ModelElemItemGroup() { ModelElemID = modelElemId, ItemGroupID = item, IsActive = true });
                }
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemDto> input)
        {
            foreach (var entity in input)
            {
                await checkUesed(entity);
                var modelmodelelems = await modelModelElemRepository.GetAll().Where(a => a.ModelElemID == entity.Id.Value).ToListAsync();
                foreach (var item in modelmodelelems)
                {
                    await modelModelElemRepository.DeleteAsync(item);
                }
                var modelmodelclients = await modelModelElemClientRepository.GetAll().Where(a => a.ModelElemID == entity.Id.Value).ToListAsync();
                foreach (var item in modelmodelclients)
                {
                    await modelModelElemClientRepository.DeleteAsync(item);
                }

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        private async Task checkUesed(MOM_ModelElemDto input)
        {
            var any = await importSorderModelElemDetailRepository.GetAll().AnyAsync(a => a.ModelElemID == input.Id.Value);
            if (any)
            {
                throw new UserFriendlyException("导入订单模板中有绑定此款明细,请先删除订单导入模板中的数据!");
            }
        }
        protected async Task<bool> ExistCodeAsync(MOM_ModelElemDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
        /// <summary>
        /// 根据款式ID获取款式明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemGetAllOutputDto>> GetByModelElemListID(MOM_ModelElemGetAllInputDto input)
        {
            var sql = (from t in repository.GetAll()
                       join t1x in itemRepository.GetAll() on t.ItemID equals t1x.Id into t1xx
                       from t1 in t1xx.DefaultIfEmpty()
                       where t.ModelElemListID == input.Id
                       select new MOM_ModelElemGetAllOutputDto
                       {
                           Id = t.Id,
                           Code = t.Code,
                           CodeName = t.CodeName,
                           Default = t.Default,
                           Price = t.Price,
                           Qty = t.Qty,
                           Remark = t.Remark,
                           CreateID = t.CreateID,
                           CreateBy = t.CreateBy,
                           CreateOn = t.CreateOn,
                           ModifyID = t.ModifyID,
                           ModifyBy = t.ModifyBy,
                           ModifyOn = t.ModifyOn,
                           ModelElemListID = t.ModelElemListID,
                           ItemID = t.ItemID,
                           IsInputItem = t.IsInputItem,
                           IsInput = t.IsInput,
                           ItemName = t1.Code + ":" + t1.CodeName,
                           ItemGroupIDs = modelElemItemGroupRepository.GetAll().Where(a => a.ModelElemID == t.Id).Select(a => a.ItemGroupID).ToList(),
                           IsActive = t.IsActive,
                           IsItemAdd = t.IsItemAdd,
                           IsItemImageAdd = t.IsItemImageAdd,
                           Sort = t.Sort == null ? 99999 : t.Sort,
                           IsItemElem = t.IsItemElem,
                            InaStyleCode=t.InaStyleCode,
                           //t.DAT_Item.ItemCode,
                           //ItemGroupCode = t.MOM_ModelElemItemGroup.Select(a => a.ItemGroupCode),
                           //ItemSubGroupCode = t.MOM_ModelElemItemGroup.Select(a => a.ItemSubGroupCode),
                           //t.ItemSeriesID,
                           //ItemSeriesCode = t.DAT_ItemSeries.ItemSeriesCode,
                           //ItemSeriesText = t.DAT_ItemSeries != null ? t.DAT_ItemSeries.ItemSeriesCode + ":" + t.DAT_ItemSeries.ItemSeriesName : ""
                       }).WhereIf(!string.IsNullOrEmpty(input.Text), a => a.Code.Trim().ToLower().Contains(input.Text.Trim().ToLower()) || a.CodeName.Trim().ToLower().Contains(input.Text.Trim().ToLower()));
            var count = await sql.CountAsync();
            var result = await sql.OrderBy(a => a.Sort).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var modelelemids = result.Select(a => a.Id).Distinct().ToList();
            var _counts = await (from t in modelModelElemRepository.GetAll().AsNoTracking()
                                 join t1x in modelRepository.GetAll().AsNoTracking() on t.ModelID equals t1x.Id into t1xx
                                 from t1 in t1xx.DefaultIfEmpty()
                                 where t1 != null && modelelemids.Contains(t.ModelElemID)
                                 group t by t.ModelElemID into g
                                 select new
                                 {
                                     ModelElemID = g.Key,
                                     Count = g.Count(),
                                 }).ToListAsync();
            var modelElemItemGroups = await (from t in modelElemItemGroupRepository.GetAll()
                                             join t1x in itemConfigRepository.GetAll() on t.ItemGroupID equals t1x.Id into t1xx
                                             from t1 in t1xx.DefaultIfEmpty()
                                             where modelelemids.Contains(t.ModelElemID)
                                             select new
                                             {
                                                 t.ModelElemID,
                                                 t1.CodeName,
                                             }).ToListAsync();

            foreach (var item in result)
            {
                var modelelem = _counts.FirstOrDefault(a => a.ModelElemID == item.Id);
                if (modelelem != null)
                {
                    item.Count = modelelem.Count;
                }
                var itemGroup = modelElemItemGroups.Where(a => a.ModelElemID == item.Id).Select(a => a.CodeName).ToList();
                if (itemGroup.Any())
                {
                    item.ItemGroupText = string.Join(',', itemGroup);
                }
            }
            //var list = objectMapper.Map<List<MOM_ModelElemGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelElemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MES_ModelElemOutPutDto>> GetByMes(PagedInput input)
        {
            var sql = from t in repository.GetAll()
                      join t1 in modelElemListRepository.GetAll() on t.ModelElemListID equals t1.Id
                      join t2x in modelElemBaseRepository.GetAll() on t1.ModelElemBaseID equals t2x.Id into t2xx
                      from t2 in t2xx.DefaultIfEmpty()
                      join t3x in groupRepository.GetAll() on t1.GroupID equals t3x.Id into t3xx
                      from t3 in t3xx.DefaultIfEmpty()
                      select new MES_ModelElemOutPutDto()
                      {

                          ModelElemID = t.Id,
                          ModelElemCode = t.Code,
                          ModelElemName = t.CodeName,
                          ModelElemListID = t.ModelElemListID,
                          ModelELemListCode = t1.Code,
                          ModelElemListName = t1.CodeName,
                          GenderID = t1.GenderID,
                          GroupID = t1.GroupID,
                          GroupText = t3.CodeName,
                          ModelElemBaseID = t1.ModelElemBaseID,
                          ModelElemBaseCode = t2.Code,
                          ModelElemBaseName = t2.CodeName,
                          ModelElemBaseSort = t2.Sequence,
                          ModelElemListSort = t1.ElemSeq,
                          ModelElemSort = t.Sort,
                          ModelElemTypeID = (int)t1.ModelElemTypeID,
                          ModelElemTypeName = t1.ModelElemTypeID.GetDescription(),
                          InaStyleCode = t.InaStyleCode,
                          InaStyleIndex = t1.InaStyleIndex,
                      };
            var count = await sql.CountAsync();
            var result = await sql.OrderByDescending(a => a.ModelElemTypeID).PageBy(input).ToListAsync();

            return new PagedResultDto<MES_ModelElemOutPutDto>(count, result);
        }
    }
}

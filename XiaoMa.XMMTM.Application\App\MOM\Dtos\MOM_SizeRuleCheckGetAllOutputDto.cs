﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleCheckGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2022年11月1日,星期二 13:40:28
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeRuleCheckGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_SizeRuleCheckGetAllOutputDto : SizeRuleCheck
    {
        public string SorderSizeTypeText { set; get; }
        public string SorderTypeText { set; get; }
        public string SizeRuleCheckTypeText { set; get; }
        public string  SizeColumnNameText { set; get; }
        public string  SizeColumnNameText1 { set; get; }
        public string  SizeColumnNameText2 { set; get; }

        public string BodyListText1 { set; get; }
        public string BodyListText2 { set; get; }
        public string  GroupText { set; get; }
      
    }
}

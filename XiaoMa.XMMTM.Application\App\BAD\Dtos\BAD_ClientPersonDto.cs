/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientPersonDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/24/星期五 17:05:54
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientPersonDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ClientPersonDto : EntityDto<Guid?>
    {
        public string Code { set; get; }

        public string CodeName { set; get; }
        /// <summary>
        /// 
        /// </summary>
        public Guid ClientID { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public string Company { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; }
        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// 移动电话
        /// </summary>
        public string Mobile { get; set; }


        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }


        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 身高
        /// </summary>
        public decimal Height { get; set; }


        /// <summary>
        /// 体重
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public bool Gender { get; set; } = true;

        public virtual bool IsActive { set; get; } = true;

    }
}

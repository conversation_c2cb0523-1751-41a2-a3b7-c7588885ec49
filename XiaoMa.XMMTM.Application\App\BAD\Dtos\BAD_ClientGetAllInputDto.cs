﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    SYM_ClientGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/10/星期五 21:51:18
-----------------------------------------------*/

using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// SYM_ClientGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ClientGetAllInputDto : PagedInput
    {
        /// <summary>
        /// 客户分类
        /// </summary>
        public ClientGroupEnums? ClientGroup { set; get; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public ClientGradeEnums? ClientGrade { set; get; }
    }
}

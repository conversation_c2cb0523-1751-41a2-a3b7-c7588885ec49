﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemBaseGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/28/星期二 16:19:08
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemBaseGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemBaseGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public bool? GenderID { set; get; }
        public Guid? GroupID { set; get; }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SewBaseAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 15:08:06
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SewBaseAppService : XMMTMAppServiceBase, IMOM_SewBaseAppService
    {
        private readonly IRepository<SewBase, Guid> repository;
        private readonly IRepository<SewList, Guid> sewListRepository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Sew, Guid> sewRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SewBaseAppService(
       IRepository<SewBase, Guid> repository,
       IRepository<SewList, Guid> SewListRepository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Sew, Guid> SewRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            sewListRepository = SewListRepository;
            this.modelRepository = modelRepository;
            sewRepository = SewRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SewBaseGetAllOutputDto>> Get(MOM_SewBaseGetAllInputDto input)
        {
            var query = repository.GetAll()
              //.Where(a => a.IsActive)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sequence).ThenByDescending(a => a.CreateOn).ThenByDescending(a=>a.ModifyOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<MOM_SewBaseGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SewBaseGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SewBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<SewBase>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SewBaseDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SewBaseDto> input)
        {
            foreach (var entity in input)
            {
                var model =await modelRepository.GetAll().Where(a => a.SewBaseID == entity.Id).Select(a=>new { a.Code,a.CodeName}).FirstOrDefaultAsync();
                if (model != null)
                {
                    throw new UserFriendlyException($"版型编码：【{model.Code}】,版型名称：【{model.CodeName}】已绑定此缝份类别，请先删除版型绑定！！！");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SewBaseDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
        [HttpPost]
        [AbpAuthorize]
        public async Task DeepCopy(List<MOM_SewBaseDto> input)
        {
            foreach (var entity in input)
            {
                var sewList = await sewListRepository.GetAll().Where(a => a.SewBaseID == entity.Id).ToListAsync();
                var sewlListIds = sewList.Select(a => a.Id).Distinct().ToList();
                var sews = await sewRepository.GetAll().Where(a => sewlListIds.Contains(a.SewListID)).ToListAsync();
                entity.Code = entity.Code + "--Copy";
                entity.CodeName = entity.CodeName + "--Copy";
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }

                var oldentity = ObjectMapper.Map<SewBase>(entity);
                var id = await repository.InsertAndGetIdAsync(oldentity);
                if (sewList.Any())
                {
                    foreach (var item in sewList)
                    {
                        var sewDtos = sews.Where(a => a.SewListID == item.Id).ToList();
                        var sewListDto = new SewList() { IsActive = true, Code = item.Code, CodeName = item.CodeName, ElemSeq = item.ElemSeq, ModelElemTypeID = item.ModelElemTypeID, Remark = item.Remark, SewBaseID = id, xid = item.xid };
                        var sewListDtoID = await sewListRepository.InsertAndGetIdAsync(sewListDto);
                        foreach (var t in sewDtos)
                        {
                            var sewDto = new Sew() { SewListID = sewListDtoID, IsActive = true, Code = t.Code, CodeName = t.CodeName, Remark = t.Remark, SizeElemcID = t.SizeElemcID, xid = t.xid };
                            await sewRepository.InsertAsync(sewDto);
                        }
                    }
                }
            }
        }
    }
}

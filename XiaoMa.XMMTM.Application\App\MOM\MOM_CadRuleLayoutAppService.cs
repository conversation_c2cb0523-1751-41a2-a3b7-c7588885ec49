﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_CadRuleLayoutAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/6/星期四 15:59:47
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_CadRuleLayoutAppService : XMMTMAppServiceBase, IMOM_CadRuleLayoutAppService
    {
        private readonly IRepository<CadRuleLayout, Guid> repository;
        private readonly IRepository<CadRule, Guid> cadRuleRepository;
        private readonly IRepository<CadLayout, Guid> cadLayOutRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<ModelElemCad, Guid> modelElemCadRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_CadRuleLayoutAppService(
       IRepository<CadRuleLayout, Guid> repository,
       IRepository<CadRule, Guid> cadRuleRepository,
       IRepository<CadLayout, Guid> cadLayOutRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<ModelElemCad, Guid> modelElemCadRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.cadRuleRepository = cadRuleRepository;
            this.cadLayOutRepository = cadLayOutRepository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.modelElemCadRepository = modelElemCadRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_CadRuleLayoutGetAllOutputDto>> Get(MOM_CadRuleLayoutGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1 in cadRuleRepository.GetAll() on t.CadRuleID equals t1.Id
                         join t2x in cadLayOutRepository.GetAll() on t.CadLayoutID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelElemRepository.GetAll() on t.ModelElemID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in modelElemListRepository.GetAll() on t3.ModelElemListID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()

                             //where t.IsActive
                         select new MOM_CadRuleLayoutGetAllOutputDto()
                         {
                             Id = t.Id,
                             Remark = t.Remark,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             CadRuleID = t.CadRuleID,
                             ModelElemID = t.ModelElemID,
                             CadLayoutID = t.CadLayoutID,
                             CadRuleText = t1.Code + ":" + t1.CodeName,
                             CadRuleCode = t1.Code,
                             CadLayoutText = t2.Code + ":" + t2.CodeName + ":" + t2.Suffix,
                             IsActive = t.IsActive,
                             ModelElemListText = t4.Code + ":" + t4.CodeName,
                             ModelElemText = t3.Code + (string.IsNullOrEmpty(t3.CodeName)?"":( ":" + t3.CodeName)),
                             ModelElemCode = t3.Code,
                             ModelElemName = t3.CodeName,
                             // GroupName=t4.CFG_Group.GroupName=="单西"?"上衣": t4.CFG_Group.GroupName,
                             GroupID = (from g in modelElemCadRepository.GetAll()
                                        from g1 in modelElemRepository.GetAll()
                                        where g.CadRuleID == t1.Id && g.ModelElemID == g1.Id
                                        select new
                                        {
                                            GroupID = g1.MOM_ModelElemList.GroupID 
                                        }).FirstOrDefault().GroupID,
                             GroupText = (from g in modelElemCadRepository.GetAll()
                                          from g1 in modelElemRepository.GetAll()
                                          from g2 in modelElemListRepository.GetAll()
                                          from g3 in groupRepository.GetAll()
                                          where g.CadRuleID == t1.Id && g.ModelElemID == g1.Id && g1.ModelElemListID == g2.Id && g2.GroupID == g3.Id  
                                          select new
                                          {
                                              GroupName = g3.CodeName
                                          }).FirstOrDefault().GroupName,
                         })
                        .WhereIf(input.CadLayoutID.HasValue, a => a.CadLayoutID == input.CadLayoutID.Value)
                        .WhereIf(input.CadRuleID.HasValue, a => a.CadRuleID == input.CadRuleID.Value)
                        .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                        .WhereIf(input.ModelElemID.HasValue, a => a.ModelElemID == input.ModelElemID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CadRuleText.ToLower().Contains(input.Text.ToLower()) || a.CadLayoutText.ToLower().Contains(input.Text.ToLower()) || a.ModelElemListText.ToLower().Contains(input.Text.ToLower()) || a.ModelElemText.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).ThenBy(a => a.CadRuleCode).ThenBy(a => a.ModelElemID).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_CadRuleLayoutGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_CadRuleLayoutDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<CadRuleLayout>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_CadRuleLayoutDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_CadRuleLayoutDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

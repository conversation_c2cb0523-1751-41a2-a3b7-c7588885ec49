/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemPriceAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/6/4/星期五 10:41:55
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelElemPriceAppService : XMMTMAppServiceBase, IMOM_ModelElemPriceAppService
    {
        private readonly IRepository<ModelElemPrice, Guid> repository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelElemPriceAppService(
       IRepository<ModelElemPrice, Guid> repository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<Item, Guid> itemRepository,
       IRepository<Client, Guid> clientRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.itemRepository = itemRepository;
            this.clientRepository = clientRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelElemPriceGetAllOutputDto>> Get(MOM_ModelElemPriceGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in itemRepository.GetAll() on t.ItemID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in clientRepository.GetAll() on t.ClientID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in modelElemRepository.GetAll() on t.ModelElemID1 equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t6x in modelElemRepository.GetAll() on t.ModelElemID2 equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                         select new MOM_ModelElemPriceGetAllOutputDto()
                         {
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             ItemID = t.ItemID,
                             ModelElemID = t.ModelElemID,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             Price = t.Price,
                             Remark = t.Remark,
                             UseClinetDiscount = t.UseClinetDiscount,
                             ModelElemCode = t1.Code,
                             ModelElemName = t1.CodeName,
                             ItemCode = t2.Code,
                             ItemOriginalItemNo = t2.OriginalItemNo,
                             ModelElemListID = t1.ModelElemListID,
                             ModelElemListCode = t3.Code,
                             ModelElemListName = t3.CodeName,
                             ClientID = t.ClientID,
                             ClientName = t4.CodeName,
                             IsCommon = !t.ClientID.HasValue,//没有绑定客户的就是通用的
                             HalfFitting = t.HalfFitting.HasValue ? t.HalfFitting.Value : false,
                             IsSuit = t.IsSuit.HasValue ? t.IsSuit.Value : false,
                             ModelElemID1 = t.ModelElemID1,
                             ModelElemID2 = t.ModelElemID2,
                             ModelElemCode1 = t5.Code,
                             ModelElemCode2 = t6.Code,
                             ModelElemName1 = t5.CodeName,
                             ModelElemName2 = t6.CodeName,
                             IsUrgent = t.IsUrgent.HasValue ? t.IsUrgent.Value : false,
                             RepairPrice = t.RepairPrice,
                             MaxQty = t.MaxQty,
                             MinQty = t.MinQty,
                             SorderType = t.SorderType,
                             SorderTypeText = t.SorderType.HasValue ? t.SorderType.Value.GetDescription() : null,
                         });

            if (input.ClientHasCommon.HasValue && input.ClientHasCommon.Value)
            {
                 query = query.WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value||a.IsCommon);
                //var list = query.Where(a => !a.IsCommon).WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value);
                //var list1 = query.Where(a => a.IsCommon);
                //query = list.Union(list1);
            }
            else
            {
                query = query.WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value);
            }

            var quer1 = query
                         .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                         .WhereIf(input.IsCommon.HasValue && input.IsCommon.Value, a => a.IsCommon == input.IsCommon.Value)
                         .WhereIf(input.SorderType.HasValue, a => a.SorderType == input.SorderType.Value)
              //.WhereIf(input.ClientID.HasValue, a => a.ClientID == input.ClientID.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemCode.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.ModelElemName.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await quer1.CountAsync();
            var result = await quer1.OrderByDescending(a => a.CreateOn).ThenBy(a => a.ModelElemListCode).ThenBy(a => a.ModelElemCode).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelElemPriceGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelElemPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同项");
                }
                var oldentity = ObjectMapper.Map<ModelElemPrice>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelElemPriceDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同项");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelElemPriceDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_ModelElemPriceDto input)
        {
            var query = repository.GetAll()
                  .Where(a => a.ModelElemID == input.ModelElemID)
                .WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.ItemID.HasValue, a => a.ItemID == input.ItemID.Value)
                .WhereIf(!input.ItemID.HasValue, a => !a.ItemID.HasValue)
                .WhereIf(!input.ClientID.HasValue, a => !a.ClientID.HasValue)
                .WhereIf(input.ClientID.HasValue, a => a.ClientID.Value == input.ClientID)
                .WhereIf(input.HalfFitting.HasValue && input.HalfFitting.Value, a => a.HalfFitting.Value == input.HalfFitting)
                .WhereIf(input.IsUrgent.HasValue && input.IsUrgent.Value, a => a.IsUrgent.Value == input.IsUrgent)
                .WhereIf(input.IsSuit.HasValue && input.IsSuit.Value, a => a.IsSuit.Value == input.IsSuit)
                 .WhereIf(!input.HalfFitting.HasValue || !input.HalfFitting.Value, a => !a.HalfFitting.Value || !a.HalfFitting.Value)
                .WhereIf(!input.IsUrgent.HasValue || !input.IsUrgent.Value, a => !a.IsUrgent.HasValue || !a.IsUrgent.Value)
                .WhereIf(!input.IsSuit.HasValue || !input.IsSuit.Value, a => !a.IsSuit.HasValue || !a.IsSuit.Value)
                .WhereIf(input.ModelElemID1.HasValue, a => a.ModelElemID1.Value == input.ModelElemID1.Value)
                .WhereIf(!input.ModelElemID1.HasValue, a => !a.ModelElemID1.HasValue)
                .WhereIf(input.ModelElemID2.HasValue, a => a.ModelElemID2.Value == input.ModelElemID2.Value)
                .WhereIf(input.SorderType.HasValue, a => a.SorderType.Value == input.SorderType.Value)
                .WhereIf(input.MaxQty.HasValue, a => a.MaxQty.Value == input.MaxQty.Value)
                .WhereIf(input.MinQty.HasValue, a => a.MinQty.Value == input.MinQty.Value)
                .WhereIf(!input.ModelElemID2.HasValue, a => !a.ModelElemID2.HasValue)
                .WhereIf(!input.SorderType.HasValue, a => !a.SorderType.HasValue)
                .WhereIf(!input.MaxQty.HasValue, a => !a.MaxQty.HasValue)
                .WhereIf(!input.MinQty.HasValue, a => !a.MinQty.HasValue)
              ;
            var b = await query.AnyAsync();
            return b;

        }
    }
}

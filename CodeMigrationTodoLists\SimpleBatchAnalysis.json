﻿{
    "Summary":  {
                    "TotalProcessed":  100,
                    "NeedsReview":  82,
                    "GeneratedAt":  "2025-09-16 15:50:40",
                    "HighPriority":  18,
                    "NamespaceOnly":  0
                },
    "HighPriorityFiles":  [
                              {
                                  "FileName":  "XMMTMCoreModule.cs",
                                  "RelativePath":  "XMMTMCoreModule.cs",
                                  "SourceLines":  72,
                                  "TargetLines":  102,
                                  "LineDiff":  30,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "ItemConfig.cs",
                                  "RelativePath":  "Domain\\BAD\\ItemConfig.cs",
                                  "SourceLines":  40,
                                  "TargetLines":  34,
                                  "LineDiff":  6,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "IUpsConfiguration.cs",
                                  "RelativePath":  "Configuration\\IUpsConfiguration.cs",
                                  "SourceLines":  15,
                                  "TargetLines":  19,
                                  "LineDiff":  4,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "UpsConfiguration.cs",
                                  "RelativePath":  "Configuration\\UpsConfiguration.cs",
                                  "SourceLines":  15,
                                  "TargetLines":  18,
                                  "LineDiff":  3,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "RateLimitingActionFilter.cs",
                                  "RelativePath":  "RateLimitingActionFilter.cs",
                                  "SourceLines":  94,
                                  "TargetLines":  93,
                                  "LineDiff":  1,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "IdentityServerConfiguration.cs",
                                  "RelativePath":  "Configuration\\IdentityServerConfiguration.cs",
                                  "SourceLines":  17,
                                  "TargetLines":  17,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "IIdentityServerConfiguration.cs",
                                  "RelativePath":  "Configuration\\IIdentityServerConfiguration.cs",
                                  "SourceLines":  11,
                                  "TargetLines":  11,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "IMTMConfiguration.cs",
                                  "RelativePath":  "Configuration\\IMTMConfiguration.cs",
                                  "SourceLines":  17,
                                  "TargetLines":  17,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "ItemElemItemConfig.cs",
                                  "RelativePath":  "Domain\\BAD\\ItemElemItemConfig.cs",
                                  "SourceLines":  48,
                                  "TargetLines":  48,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "MTMConfiguration.cs",
                                  "RelativePath":  "Configuration\\MTMConfiguration.cs",
                                  "SourceLines":  23,
                                  "TargetLines":  23,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "PermissionNames.cs",
                                  "RelativePath":  "Authorization\\PermissionNames.cs",
                                  "SourceLines":  13,
                                  "TargetLines":  13,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "XMMTMAuthorizationProvider.cs",
                                  "RelativePath":  "Authorization\\XMMTMAuthorizationProvider.cs",
                                  "SourceLines":  30,
                                  "TargetLines":  30,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "AppVersionHelper.cs",
                                  "RelativePath":  "AppVersionHelper.cs",
                                  "SourceLines":  28,
                                  "TargetLines":  28,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "XMMTMConsts.cs",
                                  "RelativePath":  "XMMTMConsts.cs",
                                  "SourceLines":  15,
                                  "TargetLines":  15,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "ETCadConfiguration.cs",
                                  "RelativePath":  "Configuration\\ETCadConfiguration.cs",
                                  "SourceLines":  27,
                                  "TargetLines":  27,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "ICadConfiguration.cs",
                                  "RelativePath":  "Configuration\\ICadConfiguration.cs",
                                  "SourceLines":  26,
                                  "TargetLines":  26,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "SSOUserPermissionCacheItem.cs",
                                  "RelativePath":  "Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                                  "SourceLines":  21,
                                  "TargetLines":  21,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              },
                              {
                                  "FileName":  "AppConfigurations.cs",
                                  "RelativePath":  "Configuration\\AppConfigurations.cs",
                                  "SourceLines":  48,
                                  "TargetLines":  48,
                                  "LineDiff":  0,
                                  "IsIdentical":  false,
                                  "Priority":  "High",
                                  "Category":  "HighPriority"
                              }
                          ],
    "NamespaceOnlyFiles":  {

                           },
    "NeedsReviewFiles":  [
                             {
                                 "FileName":  "Size.cs",
                                 "RelativePath":  "Domain\\MOM\\Size.cs",
                                 "SourceLines":  82,
                                 "TargetLines":  402,
                                 "LineDiff":  320,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ETCadManger.cs",
                                 "RelativePath":  "CAD\\ETCadManger.cs",
                                 "SourceLines":  791,
                                 "TargetLines":  858,
                                 "LineDiff":  67,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "Client.cs",
                                 "RelativePath":  "Domain\\BAD\\Client.cs",
                                 "SourceLines":  239,
                                 "TargetLines":  203,
                                 "LineDiff":  36,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ModelElemPrice.cs",
                                 "RelativePath":  "Domain\\MOM\\ModelElemPrice.cs",
                                 "SourceLines":  74,
                                 "TargetLines":  42,
                                 "LineDiff":  32,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "Sorder.cs",
                                 "RelativePath":  "Domain\\ODM\\Sorder.cs",
                                 "SourceLines":  314,
                                 "TargetLines":  291,
                                 "LineDiff":  23,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SizeList.cs",
                                 "RelativePath":  "Domain\\MOM\\SizeList.cs",
                                 "SourceLines":  102,
                                 "TargetLines":  83,
                                 "LineDiff":  19,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SorderBillDetail.cs",
                                 "RelativePath":  "Domain\\ODM\\SorderBillDetail.cs",
                                 "SourceLines":  76,
                                 "TargetLines":  59,
                                 "LineDiff":  17,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SorderBill.cs",
                                 "RelativePath":  "Domain\\ODM\\SorderBill.cs",
                                 "SourceLines":  75,
                                 "TargetLines":  59,
                                 "LineDiff":  16,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ClientModel.cs",
                                 "RelativePath":  "Domain\\BAD\\ClientModel.cs",
                                 "SourceLines":  39,
                                 "TargetLines":  48,
                                 "LineDiff":  9,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "Item.cs",
                                 "RelativePath":  "Domain\\BAD\\Item.cs",
                                 "SourceLines":  191,
                                 "TargetLines":  184,
                                 "LineDiff":  7,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ModelElem.cs",
                                 "RelativePath":  "Domain\\MOM\\ModelElem.cs",
                                 "SourceLines":  119,
                                 "TargetLines":  114,
                                 "LineDiff":  5,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ClientShop.cs",
                                 "RelativePath":  "Domain\\BAD\\ClientShop.cs",
                                 "SourceLines":  88,
                                 "TargetLines":  93,
                                 "LineDiff":  5,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ClientAddress.cs",
                                 "RelativePath":  "Domain\\BAD\\ClientAddress.cs",
                                 "SourceLines":  100,
                                 "TargetLines":  105,
                                 "LineDiff":  5,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "IETCadManager.cs",
                                 "RelativePath":  "CAD\\IETCadManager.cs",
                                 "SourceLines":  42,
                                 "TargetLines":  37,
                                 "LineDiff":  5,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "Sew.cs",
                                 "RelativePath":  "Domain\\MOM\\Sew.cs",
                                 "SourceLines":  45,
                                 "TargetLines":  41,
                                 "LineDiff":  4,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ClientPerson.cs",
                                 "RelativePath":  "Domain\\BAD\\ClientPerson.cs",
                                 "SourceLines":  81,
                                 "TargetLines":  85,
                                 "LineDiff":  4,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SorderDetailModel.cs",
                                 "RelativePath":  "Domain\\ODM\\SorderDetailModel.cs",
                                 "SourceLines":  216,
                                 "TargetLines":  219,
                                 "LineDiff":  3,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SorderCadDetail.cs",
                                 "RelativePath":  "Domain\\ODM\\SorderCadDetail.cs",
                                 "SourceLines":  50,
                                 "TargetLines":  47,
                                 "LineDiff":  3,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ISizeBaseDto.cs",
                                 "RelativePath":  "Domain\\MOM\\ISizeBaseDto.cs",
                                 "SourceLines":  336,
                                 "TargetLines":  333,
                                 "LineDiff":  3,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ETCADOutputDto.cs",
                                 "RelativePath":  "CAD\\Dtos\\ETCADOutputDto.cs",
                                 "SourceLines":  327,
                                 "TargetLines":  324,
                                 "LineDiff":  3,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ModelElemList.cs",
                                 "RelativePath":  "Domain\\MOM\\ModelElemList.cs",
                                 "SourceLines":  147,
                                 "TargetLines":  145,
                                 "LineDiff":  2,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "BodyList.cs",
                                 "RelativePath":  "Domain\\MOM\\BodyList.cs",
                                 "SourceLines":  38,
                                 "TargetLines":  36,
                                 "LineDiff":  2,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SorderCadState.cs",
                                 "RelativePath":  "Domain\\ODM\\SorderCadState.cs",
                                 "SourceLines":  52,
                                 "TargetLines":  50,
                                 "LineDiff":  2,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "PartPrice.cs",
                                 "RelativePath":  "Domain\\MOM\\PartPrice.cs",
                                 "SourceLines":  43,
                                 "TargetLines":  45,
                                 "LineDiff":  2,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "GetDetailModelQtyOutput.cs",
                                 "RelativePath":  "CAD\\Dtos\\GetDetailModelQtyOutput.cs",
                                 "SourceLines":  30,
                                 "TargetLines":  29,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ICadKeyManager.cs",
                                 "RelativePath":  "CAD\\ICadKeyManager.cs",
                                 "SourceLines":  40,
                                 "TargetLines":  39,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ModelElemImage.cs",
                                 "RelativePath":  "Domain\\MOM\\ModelElemImage.cs",
                                 "SourceLines":  72,
                                 "TargetLines":  73,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "ModelElemListStatus.cs",
                                 "RelativePath":  "Domain\\MOM\\ModelElemListStatus.cs",
                                 "SourceLines":  69,
                                 "TargetLines":  68,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SizeRuleCheck.cs",
                                 "RelativePath":  "Domain\\MOM\\SizeRuleCheck.cs",
                                 "SourceLines":  62,
                                 "TargetLines":  63,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             },
                             {
                                 "FileName":  "SizeListBase.cs",
                                 "RelativePath":  "Domain\\MOM\\SizeListBase.cs",
                                 "SourceLines":  114,
                                 "TargetLines":  113,
                                 "LineDiff":  1,
                                 "IsIdentical":  false,
                                 "Priority":  "Medium",
                                 "Category":  "NeedsReview"
                             }
                         ]
}

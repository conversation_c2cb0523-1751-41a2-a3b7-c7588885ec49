﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientAddressDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/7/24/星期五 17:02:58
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientAddressDto新增/修改数据对象Dto
    /// </summary>
    public class BAD_ClientAddressDto : EntityDto<Guid?>
    {

        public virtual bool IsActive { set; get; } = true;
        /// <summary>
        /// 
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 联系人描述
        /// </summary>
        public string ContactDesc { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Fax { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 县
        /// </summary>
        public string County { get; set; }

        /// <summary>
        /// 街道
        /// </summary>
        public string Street { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 港口
        /// </summary>
        public string Port { get; set; }


        /// <summary>
        /// 运送方式
        /// </summary>
        public string Transport { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid ClientID { get; set; }
    }
}

﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_WashingLabelModelElemListGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2022年10月10日,星期一 14:02:06
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_WashingLabelModelElemListGetAllOutputDto查询返回实体对象
    /// </summary>
    public class BAD_WashingLabelModelElemListGetAllOutputDto : WashingLabelModelElemList
    {
        public string WashingLabelPositionText { set; get; }
        public string WashingLabelPositionTypeText { set; get; }
        public string ModelElemListShowTypeText { set; get; }

        public string ModelElemListText { set; get; }
        public string ModelElem1Text { set; get; }
        public string ModelElem2Text { set; get; }
        public string GroupName { set; get; }
        public Guid? GroupID { set; get; }

    }
}

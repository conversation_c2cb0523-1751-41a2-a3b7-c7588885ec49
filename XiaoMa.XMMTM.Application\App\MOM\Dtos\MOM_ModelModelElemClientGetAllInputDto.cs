﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemClientGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/12/2/星期三 9:52:47
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemClientGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelModelElemClientGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? ModelElemID { set; get; }
        public Guid? ModelID { set; get; }
        public Guid? ClientID { set; get; }

        public Guid? GroupID { set; get; }
    }
}

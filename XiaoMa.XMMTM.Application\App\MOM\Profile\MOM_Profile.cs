﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_Profile
// 功能描述：    
// 作者    zhangby
// 时间    2020/7/28/星期二 16:14:07
-----------------------------------------------*/

using AutoMapper;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.App.SYS.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.MOM
{
    public class MOM_Profile : Profile
    {
        public MOM_Profile()
        {
            CreateMap<ModelElemBase, MOM_ModelElemBaseDto>();
            CreateMap<ModelElemList, MOM_ModelElemListDto>();
            CreateMap<ModelElem, MOM_ModelElemDto>();
            CreateMap<ModelType, MOM_ModelTypeDto>();
            CreateMap<ModelBase, MOM_ModelBaseDto>();
            CreateMap<Model, MOM_ModelDto>();
            CreateMap<SizeList, MOM_SizeListDto>();
            CreateMap<SizeListBase, MOM_SizeListBaseDto>();
            CreateMap<Size, MOM_SizeDto>();
            CreateMap<SizeRule, MOM_SizeRuleDto>();
            CreateMap<SizeRuleDetail, MOM_SizeRuleDetailDto>();
            CreateMap<BodyList, MOM_BodyListDto>();
            CreateMap<Body, MOM_BodyDto>();
            CreateMap<SewBase, MOM_SewBaseDto>();
            CreateMap<SewList, MOM_SewListDto>();
            CreateMap<Sew, MOM_SewDto>();
            CreateMap<ModelModelElem, MOM_ModelModelElemDto>();
            CreateMap<ModelElemCad, MOM_ModelElemCadDto>();
            CreateMap<CadRuleLayout, MOM_CadRuleLayoutDto>();
            CreateMap<CadLayoutModelElem, MOM_CadLayoutModelElemDto>();
            CreateMap<ModelElemVariant, MOM_ModelElemVariantDto>();
            CreateMap<ModelSizeColumn, MOM_ModelSizeColumnDto>();
            CreateMap<ModelBodyList, MOM_ModelBodyListDto>();
            CreateMap<ClientModel, MOM_ModelClientDto>();
            CreateMap<ModelImage, MOM_ModelImageDto>();
            CreateMap<ModelElemImage, MOM_ModelElemImageDto>();
            CreateMap<ModelBaseImage, MOM_ModelBaseImageDto>();
            CreateMap<BodyImage, MOM_BodyImageDto>();
       

            CreateMap<ModelElemBase, MOM_ModelElemBaseGetAllOutputDto>();
            CreateMap<ModelElemList, MOM_ModelElemListGetAllOutputDto>();
            CreateMap<ModelElem, MOM_ModelElemGetAllOutputDto>();
            CreateMap<ModelType, MOM_ModelTypeGetAllOutputDto>();
            CreateMap<ModelBase, MOM_ModelBaseGetAllOutputDto>();
            CreateMap<Model, MOM_ModelGetAllOutputDto>();
            CreateMap<SizeList, MOM_SizeListGetAllOutputDto>();
            CreateMap<SizeListBase, MOM_SizeListBaseGetAllOutputDto>();
            CreateMap<Size, MOM_SizeGetAllOutputDto>();
            CreateMap<SizeRule, MOM_SizeRuleGetAllOutputDto>();
            CreateMap<SizeRuleDetail, MOM_SizeRuleDetailGetAllOutputDto>();
            CreateMap<SizeRuleDetail, MOM_SizeRuleDetailGetAllOutputDto>();
            CreateMap<BodyList, MOM_BodyListGetAllOutputDto>();
            CreateMap<Body, MOM_BodyGetAllOutputDto>();
            CreateMap<SewBase, MOM_SewBaseGetAllOutputDto>();
            CreateMap<SewList, MOM_SewListGetAllOutputDto>();
            CreateMap<Sew, MOM_SewGetAllOutputDto>();
            CreateMap<ModelModelElem, MOM_ModelModelElemGetAllOutputDto>();
            CreateMap<ModelElemCad, MOM_ModelElemCadGetAllOutputDto2>();
            CreateMap<CadRuleLayout, MOM_CadRuleLayoutGetAllOutputDto>();
            CreateMap<CadLayoutModelElem, MOM_CadLayoutModelElemGetAllOutputDto>();
            CreateMap<ModelElemVariant, MOM_ModelElemVariantGetAllOutputDto>();
            CreateMap<ModelImage, MOM_ModelImageGetAllOutputDto>();
            CreateMap<ModelElemImage, MOM_ModelElemImageGetAllOutputDto>();
            CreateMap<ModelBaseImage, MOM_ModelBaseImageGetAllOutputDto>();
            CreateMap<BodyImage, MOM_BodyImageGetAllOutputDto>();
            CreateMap<ModelElemListStatus, MOM_ModelElemListStatusGetAllOutputDto>();
            CreateMap<SizeColumnImage, MOM_ModelElemListStatusGetAllOutputDto>();


            CreateMap<MOM_ModelElemBaseDto, ModelElemBase>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemListDto, ModelElemList>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemDto, ModelElem>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelTypeDto, ModelType>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelBaseDto, ModelBase>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelDto, Model>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeListDto, SizeList>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeListBaseDto, SizeListBase>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeDto, Size>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeRuleDto, SizeRule>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeRuleDetailDto, SizeRuleDetail>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_BodyListDto, BodyList>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_BodyDto, Body>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SewBaseDto, SewBase>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SewDto, Sew>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SewListDto, SewList>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelModelElemDto, ModelModelElem>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemCadDto, ModelElemCad>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_CadRuleLayoutDto, CadRuleLayout>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_CadLayoutModelElemDto, CadLayoutModelElem>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemVariantDto, ModelElemVariant>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelSizeColumnDto, ModelSizeColumn>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelBodyListDto, ModelBodyList>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelClientDto,ClientModel>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelImageDto, ModelImage>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemImageDto, ModelElemImage>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelBaseImageDto, ModelBaseImage>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_BodyImageDto, BodyImage>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemListStatusDto, ModelElemListStatus>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_SizeColumnImageDto, SizeColumnImage>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelElemRuleDto, ModelElemRule>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值
            CreateMap<MOM_ModelModelElemClientDto, ModelModelElemClient>().ForMember(dest => dest.Id, source => source.Ignore());//忽略空值

        }
    }
}

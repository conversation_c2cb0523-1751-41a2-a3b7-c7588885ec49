/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/30/星期四 15:04:18
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelAppService : XMMTMAppServiceBase, IMOM_ModelAppService
    {
        private readonly IRepository<Model, Guid> repository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<ModelBase, Guid> modelBaseRepository;
        private readonly IRepository<SizeList, Guid> sizeListRepository;
        private readonly IRepository<SewBase, Guid> sewBaseRepository;
        private readonly IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<ModelBodyList, Guid> modelBodyLisyRepository;
        private readonly IRepository<ClientModel, Guid> clientModelRepository;
        private readonly IRepository<Client, Guid> clientRepository;
        private readonly IRepository<ClientModel, Guid> clienModeltRepository;
        private readonly IRepository<BodyList, Guid> bodyLisyRepository;
        private readonly IRepository<ModelModelElem, Guid> modelModelElemRepository;
        private readonly IRepository<ModelModelElemClient, Guid> modelModelElemClientRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly IRepository<Class, Guid> classRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<SorderDetailFile, Guid> sorderDetailFileRepository;
        private readonly IRepository<SorderDetailModel, Guid> sorderDetailModelRepository;
        private readonly IRepository<SorderDetailElem, Guid> sorderDetailModelElemRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<ItemElemItem, Guid> itemElemItemRepository;
        private readonly FileServer.FileServer fileServer;
        private readonly IObjectMapper objectMapper;

        public MOM_ModelAppService(
       IRepository<Model, Guid> repository,
       IRepository<Group, Guid> groupRepository,
       IRepository<ModelBase, Guid> modelBaseRepository,
       IRepository<SizeList, Guid> sizeListRepository,
       IRepository<SewBase, Guid> sewBaseRepository,
       IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<ModelBodyList, Guid> modelBodyLisyRepository,
       IRepository<ClientModel, Guid> clientModelRepository,
       IRepository<Client, Guid> clientRepository,
       IRepository<ClientModel, Guid> clienModeltRepository,
       IRepository<BodyList, Guid> bodyLisyRepository,
       IRepository<ModelModelElem, Guid> modelModelElemRepository,
       IRepository<ModelModelElemClient, Guid> modelModelElemClientRepository,
       IRepository<ModelImage, Guid> modelImageRepository,
       IRepository<Class, Guid> classRepository,
       IRepository<Sorder, Guid> sorderRepository,
       IRepository<SorderDetail, Guid> sorderDetailRepository,
       IRepository<SorderDetailFile, Guid> sorderDetailFileRepository,
       IRepository<SorderDetailModel, Guid> sorderDetailModelRepository,
       IRepository<SorderDetailElem, Guid> sorderDetailModelElemRepository,
       IRepository<Item, Guid> itemRepository,
          IRepository<ItemElemItem, Guid> itemElemItemRepository,
          FileServer.FileServer fileServer,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.groupRepository = groupRepository;
            this.modelBaseRepository = modelBaseRepository;
            this.sizeListRepository = sizeListRepository;
            this.sewBaseRepository = sewBaseRepository;
            this.modelSizeColumnRepository = modelSizeColumnRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.modelBodyLisyRepository = modelBodyLisyRepository;
            this.clientModelRepository = clientModelRepository;
            this.clientRepository = clientRepository;
            this.clienModeltRepository = clienModeltRepository;
            this.bodyLisyRepository = bodyLisyRepository;
            this.modelModelElemRepository = modelModelElemRepository;
            this.modelModelElemClientRepository = modelModelElemClientRepository;
            this.modelImageRepository = modelImageRepository;
            this.classRepository = classRepository;
            this.sorderRepository = sorderRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.sorderDetailFileRepository = sorderDetailFileRepository;
            this.sorderDetailModelRepository = sorderDetailModelRepository;
            this.sorderDetailModelElemRepository = sorderDetailModelElemRepository;
            this.itemRepository = itemRepository;
            this.itemElemItemRepository = itemElemItemRepository;
            this.fileServer = fileServer;
            this.objectMapper = objectMapper;
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelGetAllOutputDto>> Get(MOM_ModelGetAllInputDto input)
        {
            var query = (from a in repository.GetAll()
                         join t1x in groupRepository.GetAll() on a.GroupID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in modelBaseRepository.GetAll() on a.ModelBaseID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in sizeListRepository.GetAll() on a.SizeListID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in sewBaseRepository.GetAll() on a.SewBaseID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                             //join t5x in clienModeltRepository.GetAll() on a.Id equals t5x.ModelID into t5xx
                             //from t5 in t5xx.DefaultIfEmpty()
                         join t5x in modelImageRepository.GetAll() on a.Id equals t5x.ModelID into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t6x in classRepository.GetAll() on a.ModelGroupID equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                         join t7x in repository.GetAll() on a.OriginalModelID equals t7x.Id into t7xx
                         from t7 in t7xx.DefaultIfEmpty()
                         join t8x in itemRepository.GetAll() on a.ItemID equals t8x.Id into t8xx
                         from t8 in t8xx.DefaultIfEmpty()
                         select new MOM_ModelGetAllOutputDto()
                         {
                             SizeListID = a.SizeListID,
                             BusinessSubType = a.BusinessSubType,
                             Code = a.Code,
                             CodeName = a.CodeName,
                             CreateBy = a.CreateBy,
                             CreateID = a.CreateID,
                             CreateOn = a.CreateOn,
                             GenderID = a.GenderID,
                             GroupID = a.GroupID,
                             Id = a.Id,
                             IsActive = a.IsActive,
                             IsClientShow = a.IsClientShow,
                             IsRuleSize = a.IsRuleSize,
                             //IssueDate = a.IssueDate,
                             MaxEase = a.MaxEase,
                             MinEase = a.MinEase,
                             ModelBaseID = a.ModelBaseID,
                             ModifyBy = a.ModifyBy,
                             ModifyID = a.ModifyID,
                             ModifyOn = a.ModifyOn,
                             //OriginModelID = a.OriginModelID,
                             Prefix = a.Prefix,
                             Remark = a.Remark,
                             SewBaseID = a.SewBaseID,
                             Single = a.Single,
                             Owner = a.Owner,
                             OwnerID = a.OwnerID,
                             ShortName = a.ShortName,
                             GenderText = a.GenderID ? "男" : "女",
                             BusinessSubTypeText = a.BusinessSubType.GetDescription(),
                             GroupText = t1.Code + ":" + t1.CodeName,
                             ModelBaseText = t2.Code + ":" + t2.CodeName,
                             SewBaseText = t4.Code + ":" + t4.CodeName,
                             SizeListText = t3.Code + ":" + t3.CodeName,
                             ClinetID = input.ClientID,
                             ImagePath = fileServer.GetImageUrl(t5.ImagePath),
                             ModelGroupID = t6.Id,
                             ModelGroupText = t6 != null ? (t6.Code + ":" + t6.CodeName) : "",
                             ItemID = a.ItemID,
                             OriginalModelID = a.OriginalModelID,
                             SoderDetailModelSource = a.SoderDetailModelSource,
                             IssueDate = a.IssueDate,
                             ItemCode = t8.Code,
                             ItemName = t8.CodeName,
                             ItemOriginalItemNo = t8.OriginalItemNo,
                             //PlushModelCheckMessage = a.PlushModelCheckMessage,
                             OriginalModelCode = t7.Code,
                             //PlushModelChecked = a.PlushModelChecked,
                             OriginalModelName = t7.CodeName,
                             DesignNo = a.DesignNo,
                             ShortModelCode = a.ShortModelCode,
                             IsResearchDevelopmentSource = a.IsResearchDevelopmentSource,
                             FileManagementID = a.FileManagementID,
                         })
                         .WhereIf(input.Id.HasValue, a => a.Id == input.Id.Value)
              .WhereIf(input.BusinessSubType.HasValue, a => a.BusinessSubType == input.BusinessSubType.Value)
              .WhereIf(input.GenderID.HasValue, a => a.GenderID == input.GenderID.Value)
              .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
              .WhereIf(input.ModelBaseID.HasValue, a => a.ModelBaseID == input.ModelBaseID.Value)
              .WhereIf(input.IsActive.HasValue, a => a.IsActive == input.IsActive.Value)
              .WhereIf(input.SewBaseID.HasValue, a => a.SewBaseID == input.SewBaseID.Value)
              .WhereIf(input.SizeListID.HasValue, a => a.SizeListID == input.SizeListID.Value)
              .WhereIf(input.ModelGroupID.HasValue, a => a.ModelGroupID == input.ModelGroupID.Value)
              .WhereIf(input.BusinessSubTypes.Any(), a => input.BusinessSubTypes.Contains(a.BusinessSubType))
              //.WhereIf(input.ClientID.HasValue,a=>a.ClinetID==input.ClientID)
              .WhereIf(!string.IsNullOrEmpty(input.DesignNo), a => a.DesignNo.ToLower().Contains(input.DesignNo.ToLower()))
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.ToLower().Contains(input.Text.ToLower()) || a.Code.ToLower().Contains(input.Text.ToLower()));
            if (input.ClientID.HasValue)
            {
                var clientModelIds = await clienModeltRepository.GetAll().Where(a => a.ClientID == input.ClientID).Select(a => a.ModelID).ToListAsync();
                query = from t in query
                        where clientModelIds.Contains(t.Id)
                        select t;
            }
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).ThenBy(a => a.IsActive).PageBy(input).ToListAsync();
            var sorderdetailmodelids = result.Where(a => a.SoderDetailModelSource.HasValue).Select(a => a.SoderDetailModelSource).Distinct().ToList();
            var sorders = await (from t in sorderDetailModelRepository.GetAll()
                                 join t1 in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1.Id
                                 join t2 in sorderRepository.GetAll() on t1.SorderID equals t2.Id
                                 where sorderdetailmodelids.Contains(t.Id)
                                 select new
                                 {
                                     SorderDetailModelID = t.Id,
                                     SorderNumber = t2.Code,
                                     SorderID = t2.Id
                                 }).ToListAsync();
            //var OriginalModelIDs = result.Where(a => a.OriginalModelID.HasValue).Select(a => a.OriginalModelID).Distinct().ToList();
            //var OriginalModels =await repository.GetAll().Where(a => OriginalModelIDs.Contains(a.Id)).ToListAsync();
            foreach (var item in result)
            {

                var sorder = sorders.FirstOrDefault(a => a.SorderDetailModelID == item.SoderDetailModelSource);
                if (sorder != null)
                {
                    item.SoderNumber = sorder.SorderNumber;
                    item.SorderID = sorder.SorderID;
                }
            }
            //var list = objectMapper.Map<List<MOM_ModelGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelGetAllOutputDto>(count, result);
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<Model>(entity);
                oldentity.Id = Guid.NewGuid();
                if (oldentity.BusinessSubType == ModelBusinessSubType.CommonModel)
                {
                    oldentity.ItemID = null;
                    oldentity.OriginalModelID = null;
                    oldentity.SoderDetailModelSource = null;
                    oldentity.DesignNo = null;
                    //oldentity.PlushModelCheckMessage = null;
                    //oldentity.PlushModelChecked = null;
                }
                if (oldentity.BusinessSubType == ModelBusinessSubType.TeamModel)
                {
                    await plusModelModelElem(entity, oldentity.Id, entity.ItemID);
                }
                oldentity = await IsResearchDevelopmentSource(oldentity);
                await repository.InsertAndGetIdAsync(oldentity);
            }
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                if (entity.BusinessSubType == ModelBusinessSubType.CommonModel)
                {
                    entity.ItemID = null;
                    if (oldentity.BusinessSubType == ModelBusinessSubType.TeamModel)
                    {
                        entity.SoderDetailModelSource = null;
                        entity.PlushModelCheckMessage = null;
                        entity.PlushModelChecked = null;
                    }

                }
                if (entity.BusinessSubType == ModelBusinessSubType.TeamModel && (oldentity.OriginalModelID != entity.OriginalModelID || oldentity.ItemID != entity.ItemID))
                {
                    oldentity.ItemID = entity.ItemID;
                    var itemid = oldentity.ItemID;
                    entity.PlushModelChecked = false;
                    entity.PlushModelCheckMessage = null;

                    await plusModelModelElem(entity, oldentity.Id, itemid);
                }
                ObjectMapper.Map(entity, oldentity);
                oldentity = await IsResearchDevelopmentSource(oldentity);
                await repository.UpdateAsync(oldentity);

            }
        }

        /// <summary>
        /// 判断是否是研发版
        /// 研发版关联工艺图
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<Model> IsResearchDevelopmentSource(Model input)
        {
            input.IsResearchDevelopmentSource = null;
            input.FileManagementID = null;
            if (!input.SoderDetailModelSource.HasValue || input.SoderDetailModelSource.Value == Guid.Empty)
            {
                return input;
            }
            if (input.BusinessSubType != ModelBusinessSubType.TeamModel)
            {
                return input;
            }
            var sorderDetailModel = await (from t in sorderDetailModelRepository.GetAll()
                                           join t1x in sorderDetailRepository.GetAll() on t.SorderDetailID equals t1x.Id into t1xx
                                           from t1 in t1xx.DefaultIfEmpty()
                                           join t2x in sorderRepository.GetAll() on t1.SorderID equals t2x.Id into t2xx
                                           from t2 in t2xx.DefaultIfEmpty()
                                           join t3x in sorderDetailFileRepository.GetAll() on t.Id equals t3x.SorderDetailModelID into t3xx
                                           from t3 in t3xx.DefaultIfEmpty()
                                           where t2.SorderTypeID.HasValue && t2.SorderTypeID.Value == ODM_SorderType.ResearchDevelopment
                                           where t3.IsDefault
                                           where t.Id == input.SoderDetailModelSource.Value
                                           select new { SorderDetailModelID = t.Id, t3.FileManagementID }).FirstOrDefaultAsync();
            if (sorderDetailModel != null)
            {
                input.IsResearchDevelopmentSource = true;
                input.FileManagementID = sorderDetailModel.FileManagementID;
            }

            return input;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelDto> input)
        {
            //需要删除
            // MOM_ModelModelElem
            //MOM_ModelModelElemClient
            foreach (var entity in input)
            {
                var sorderdetailmodelid = await sorderDetailModelRepository.GetAll().Where(a => a.ModelID == entity.Id).Select(a => a.SorderDetailID).ToListAsync();
                var sorder = await (from t in sorderDetailRepository.GetAll()
                                    join t2x in sorderRepository.GetAll() on t.SorderID equals t2x.Id into t2xx
                                    from t2 in t2xx.DefaultIfEmpty()
                                    where sorderdetailmodelid.Contains(t.Id) && !t2.IsDeleted
                                    select new { t.Id, SorderID = t2.Id, t2.Code }).ToListAsync();
                if (sorder.Any())
                {
                    var codes = sorder.Select(a => a.Code).Distinct().ToList();
                    var str = string.Join(",", codes);
                    throw new UserFriendlyException($"订单{str}中有引用此版型,不能删除,请先删除订单中的此版型.");
                }
                //版型绑定的款式明细
                var modelmodelelems = await modelModelElemRepository.GetAll().Where(a => a.ModelID == entity.Id.Value).ToListAsync();

                if (modelmodelelems.Any())
                {
                    await modelModelElemRepository.GetDbContext().BulkDeleteAsync<ModelModelElem>(modelmodelelems);
                    //foreach (var item in modelmodelelems)
                    //{
                    //    await modelModelElemRepository.DeleteAsync(item);
                    //}
                }
                //客户绑定的款式明细
                var modelmdodelelemclients = await modelModelElemClientRepository.GetAll().Where(a => a.ModelID == entity.Id.Value).ToListAsync();
                if (modelmdodelelemclients.Any())
                {
                    await modelModelElemClientRepository.GetDbContext().BulkDeleteAsync(modelmdodelelemclients);
                }
                //foreach (var item in modelmdodelelemclients)
                //{
                //    await modelModelElemClientRepository.DeleteAsync(item);
                //}
                //客户绑定的版型
                var clientmodels = await clientModelRepository.GetAll().Where(a => a.ModelID == entity.Id.Value).ToListAsync();
                if (clientmodels.Any())
                {
                    await clientModelRepository.GetDbContext().BulkDeleteAsync(clientmodels);
                }
                //foreach (var item in clientmodels)
                //{
                //    await clientModelRepository.DeleteAsync(item);
                //}
                var oldentity = await repository.GetAll().AsNoTracking().Where(a => a.Id == entity.Id.Value).ToListAsync();
                //ObjectMapper.Map(entity, oldentity);
                await repository.GetDbContext().BulkDeleteAsync(oldentity);
            }
        }

        /// <summary>
        /// 固化版型
        /// </summary>
        /// <param name="model"></param>
        /// <param name="modelID"></param>
        /// <param name="oldItemID"></param>
        /// <returns></returns>
        private async Task plusModelModelElem(MOM_ModelDto model, Guid modelID, Guid? oldItemID)
        {
            if (!model.OriginalModelID.HasValue)
            {
                return;
            }
            if (!model.ItemID.HasValue)
            {
                throw new UserFriendlyException("固化版型面料信息不能为空");
            }
            var modelModelElems = await modelModelElemRepository.GetAll().Where(a => a.ModelID == modelID).ToListAsync();
            foreach (var item in modelModelElems)
            {
                await modelModelElemRepository.DeleteAsync(item);
            }
            var modelsizes = await modelSizeColumnRepository.GetAll().Where(a => a.ModelID == modelID).ToListAsync();
            foreach (var item in modelsizes)
            {
                await modelSizeColumnRepository.DeleteAsync(item);
            }
            var modelBodys = await modelBodyLisyRepository.GetAll().Where(a => a.ModelID == modelID).ToListAsync();
            foreach (var item in modelBodys)
            {
                await modelBodyLisyRepository.DeleteAsync(item);
            }
            var originalmodelElems = await modelModelElemRepository.GetAll().Where(a => a.ModelID == model.OriginalModelID.Value).Select(a => new ModelModelElem()
            {
                Id = new Guid(),
                ModelID = modelID,
                ModelElemID = a.ModelElemID,
                IsActive = true,
            }).ToListAsync();
            if (model.SoderDetailModelSource.HasValue)
            {
                var sorderDetailElems = await sorderDetailModelElemRepository.GetAll().Where(a => a.SorderDetailModelID == model.SoderDetailModelSource.Value).ToListAsync();
                originalmodelElems = (from t in originalmodelElems
                                      join t1x in sorderDetailElems on t.ModelElemID equals t1x.ModelElemID into t1xx
                                      from t1 in t1xx.DefaultIfEmpty()
                                      select new ModelModelElem()
                                      {
                                          Id = t.Id,
                                          ModelElemID = t.ModelElemID,
                                          ModelID = t.ModelID,
                                          IsActive = t.IsActive,
                                          Default = t1 != null,
                                          Input = t1?.Input,
                                          ItemID = t1?.ItemID,
                                          Qty = t1?.Qty,
                                      }).ToList();
            }
            //面料配色方案
            if (oldItemID.HasValue && oldItemID.Value != model.ItemID.Value)
            {
                originalmodelElems = await ItemElemItem(originalmodelElems, model.ItemID.Value, null);
            }
            foreach (var item in originalmodelElems)
            {
                await modelModelElemRepository.InsertAsync(item);
            }
            var nModelsizes = await modelSizeColumnRepository.GetAll().Where(a => a.ModelID == model.OriginalModelID.Value).Select(a => new ModelSizeColumn() { Id = new Guid(), ModelID = modelID, IsActive = true, IsRequired = a.IsRequired, SizeColumnID = a.SizeColumnID }).ToListAsync();
            foreach (var item in nModelsizes)
            {
                await modelSizeColumnRepository.InsertAsync(item);
            }
            var nModelBodys = await modelBodyLisyRepository.GetAll().Where(a => a.ModelID == model.OriginalModelID.Value).Select(a => new ModelBodyList() { Id = new Guid(), ModelID = modelID, BodyListID = a.BodyListID, IsActive = true }).ToListAsync();
            foreach (var item in nModelBodys)
            {
                await modelBodyLisyRepository.InsertAsync(item);
            }


        }

        /// <summary>
        /// 插入(面料/辅料)配色方案
        /// </summary>
        /// <param name="list"></param>
        /// <param name="ItemID"></param>
        /// <param name="ClientID"></param>
        /// <param name="sorderTypeID"></param>
        /// <returns></returns>
        private async Task<List<ModelModelElem>> ItemElemItem(List<ModelModelElem> list, Guid ItemID, Guid? ClientID, ODM_SorderType sorderTypeID = ODM_SorderType.MTMGD)
        {

            var elemids = list.Select(a => a.ModelElemID).ToList();
            //面料配色方案
            var itemmls = await (from t in itemElemItemRepository.GetAll()
                                 where ItemID == t.ItemID && t.SorderType == sorderTypeID && t.IsActive && elemids.Contains(t.ModelElemID)
                                 select new
                                 {
                                     t.Id,
                                     t.ModelElemID,
                                     t.ItemID1,
                                     t.ClientID,
                                 }).ToListAsync();
            if (ClientID.HasValue && ClientID.Value != Guid.Empty)
            {
                var a = itemmls.Where(a => a.ClientID.HasValue && a.ClientID.Value == ClientID.Value).ToList();
                var b = itemmls.Where(a => !a.ClientID.HasValue).ToList();
                itemmls = (from t in (a.Union(b))
                           group t by t.ModelElemID into p
                           select new
                           {
                               Id = p.FirstOrDefault().Id,
                               ModelElemID = p.Key,
                               ItemID1 = p.Any(a => a.ClientID.HasValue && a.ClientID.Value != Guid.Empty) ? p.FirstOrDefault(a => a.ClientID.HasValue).ItemID1 : p.FirstOrDefault().ItemID1,
                               ClientID = p.FirstOrDefault().ClientID,
                           }).ToList();

            }
            else
            {
                itemmls = itemmls.Where(a => !a.ClientID.HasValue).ToList();
            }

            //辅料配色方案
            var itemids = list.Where(a => a.ItemID.HasValue).Select(a => a.ItemID).Distinct().ToList();
            var itemfls = await (from t in itemElemItemRepository.GetAll()
                                 where itemids.Contains(t.ItemID) && t.SorderType == sorderTypeID && t.IsActive && elemids.Contains(t.ModelElemID)
                                 select new
                                 {
                                     t.ModelElemID,
                                     t.ItemID1,
                                     t.ClientID,
                                 }).ToListAsync();

            if (ClientID.HasValue && ClientID.Value != null)
            {
                var a = itemfls.Where(a => a.ClientID.HasValue && a.ClientID.Value == ClientID.Value).ToList();
                var b = itemfls.Where(a => !a.ClientID.HasValue).ToList();
                itemfls = (from t in (a.Union(b))
                           group t by t.ModelElemID into p
                           select new
                           {
                               ModelElemID = p.Key,
                               ItemID1 = p.Any(a => a.ClientID.HasValue && a.ClientID.Value != Guid.Empty) ? p.FirstOrDefault(a => a.ClientID.HasValue).ItemID1 : p.FirstOrDefault().ItemID1,
                               ClientID = p.FirstOrDefault().ClientID,
                           }).ToList();
            }
            else
            {
                itemfls = itemfls.Where(a => !a.ClientID.HasValue).ToList();
            }

            foreach (var elem in list)
            {

                #region 面料配色方案
                var itemml = itemmls.FirstOrDefault(a => a.ModelElemID == elem.ModelElemID);
                if (itemml != null)
                {
                    elem.ItemID = itemml.ItemID1;
                }
                #endregion

                #region 辅料配色方案
                var itemfl = itemfls.FirstOrDefault(a => a.ModelElemID == elem.ModelElemID);
                //var isItemInput = elem.ModelElem.FirstOrDefault(a => a.ModelElemID == elem.ModelElemID);
                if (itemfl != null)
                {
                    elem.ItemID = itemfl.ItemID1;
                }
                #endregion
            }
            return list;
        }
        protected async Task<bool> ExistCodeAsync(MOM_ModelDto input)
        {
            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }
        }

        /// <summary>
        /// 获取版型关联的规格
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelSizeColumnGetAllOutputDto>> GetModelSizeColumnByModelID(MOM_ModelSizeColumnGetAllInputDto input)
        {
            var query = from t in sizeColumnRepository.GetAll()
                        join t1x in modelSizeColumnRepository.GetAll().Where(a => a.ModelID == input.Id) on t.Id equals t1x.SizeColumnID into t1xx
                        from t1 in t1xx.DefaultIfEmpty()
                        orderby t.Sequence
                        select new MOM_ModelSizeColumnGetAllOutputDto()
                        {
                            ModelID = input.Id,
                            Id = t1 == null ? Guid.Empty : t1.Id,
                            SizeColumnID = t.Id,
                            SizeColumnName = t.CodeName,
                            SizeColumnCode = t.Code,
                            //t1.GroupID,
                            IsActive = t1 == null ? false : t1.IsActive,
                            IsRequired = t1 == null ? false : t1.IsRequired,
                            Selected = t1 == null ? false : true,
                            CreateOn = t.CreateOn,
                            Sequence = t.Sequence
                        };
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive).ThenBy(a => a.Sequence).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelSizeColumnGetAllOutputDto>(count, result);
        }

        /// <summary>
        /// 更新版型关联的规格
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task ModelSizeColumnUpdatesByModelID(List<MOM_ModelSizeColumnDto> input)
        {
            var adds = input.Where(a => a.Id == Guid.Empty && a.Selected);
            var edits = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected);
            var dels = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected);
            foreach (var item in adds)
            {
                item.IsActive = true;
                var oldentity = ObjectMapper.Map<ModelSizeColumn>(item);
                await modelSizeColumnRepository.InsertAsync(oldentity);
            }
            foreach (var entity in edits)
            {
                entity.IsActive = true;
                var oldentity = modelSizeColumnRepository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await modelSizeColumnRepository.UpdateAsync(oldentity);
            }
            foreach (var item in dels)
            {
                await modelSizeColumnRepository.DeleteAsync(item.Id.Value);
            }
        }

        /// <summary>
        /// 获取版型关联的特体
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelBodyListGetAllOutputDto>> GetModelBodyListByModelID(MOM_ModelBodyListGetAllInputDto input)
        {
            var query = (from t in bodyLisyRepository.GetAll()
                         join t1x in modelBodyLisyRepository.GetAll().Where(a => a.ModelID == input.Id) on t.Id equals t1x.BodyListID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         where t1.IsActive && t.IsActive
                         orderby t.Sequence
                         select new MOM_ModelBodyListGetAllOutputDto()
                         {
                             ModelID = input.Id,
                             Id = t1 == null ? Guid.Empty : t1.Id,
                             BodyListID = t.Id,
                             BodyListName = t.CodeName,
                             BodyListCode = t.Code,
                             //t1.GroupID,
                             IsActive = t1 == null ? false : t1.IsActive,
                             Selected = t1 == null ? false : true,
                             CreateOn = t.CreateOn
                         }).WhereIf(!string.IsNullOrEmpty(input.Text), a => a.BodyListName.ToLower().Contains(input.Text.ToLower()) || a.BodyListCode.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelBodyListGetAllOutputDto>(count, result);
        }

        /// <summary>
        /// 更新版型关联的特体
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task ModelBodyListUpdatesByModelID(List<MOM_ModelBodyListDto> input)
        {
            var adds = input.Where(a => a.Id == Guid.Empty && a.Selected);
            var edits = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected);
            var dels = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected);
            foreach (var item in adds)
            {
                var oldentity = ObjectMapper.Map<ModelBodyList>(item);
                await modelBodyLisyRepository.InsertAsync(oldentity);
            }
            foreach (var entity in edits)
            {
                var oldentity = modelBodyLisyRepository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await modelBodyLisyRepository.UpdateAsync(oldentity);
            }
            foreach (var item in dels)
            {
                await modelBodyLisyRepository.DeleteAsync(item.Id.Value);
            }
        }
        /// <summary>
        /// 获取版型关联的客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelClientGetAllOutputDto>> GetModelClientByModelID(MOM_ModelClientGetAllInputDto input)
        {
            var query = (from t in clientRepository.GetAll().Where(a => a.ClientGroup != ClientGroupEnums.G && a.IsActive)
                         join t1x in clientModelRepository.GetAll().Where(a => a.ModelID == input.Id) on t.Id equals t1x.ClientID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         orderby t.Code
                         select new MOM_ModelClientGetAllOutputDto()
                         {
                             ModelID = input.Id,
                             Id = t1 == null ? Guid.Empty : t1.Id,
                             ClientID = t.Id,
                             ClientName = t.CodeName,
                             ClientCode = t.Code,
                             Selected = t1 == null ? false : true,
                             CreateOn = t.CreateOn
                         }).WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ClientCode.ToLower().Contains(input.Text.ToLower()) || a.ClientName.ToLower().Contains(input.Text.ToLower()));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelClientGetAllOutputDto>(count, result);
        }
        /// <summary>
        /// 更新版型关联的客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task ModelClientUpdatesByModelID(List<MOM_ModelClientDto> input)
        {
            var adds = input.Where(a => a.Id == Guid.Empty && a.Selected);
            var edits = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected);
            var dels = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected);
            foreach (var item in adds)
            {
                var oldentity = ObjectMapper.Map<ClientModel>(item);
                await clientModelRepository.InsertAsync(oldentity);
            }
            foreach (var entity in edits)
            {
                var oldentity = clientModelRepository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await clientModelRepository.UpdateAsync(oldentity);
            }
            foreach (var item in dels)
            {
                await clientModelRepository.DeleteAsync(item.Id.Value);
            }
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task DeepClone(DeepCloneDto input)
        {

            var entity = await repository.GetAsync(input.Id);
            var modelModelElems = await modelModelElemRepository.GetAll().Where(a => a.ModelID == input.Id).ToListAsync();
            var modelSizeColumns = await modelSizeColumnRepository.GetAll().Where(a => a.ModelID == input.Id).ToListAsync();
            var modelBodyLists = await modelBodyLisyRepository.GetAll().Where(a => a.ModelID == input.Id).ToListAsync();
            var clientModels = await clientModelRepository.GetAll().Where(a => a.ModelID == input.Id).ToListAsync();
            entity.Code += "---Copy";
            entity.CodeName += "---Copy";
            entity.Id = Guid.Empty;
            entity.CreateOn = DateTime.Now;
            entity.CreateID = this.SSOSession.UserId;
            entity.CreateBy = this.SSOSession.Name;
            entity.ModifyBy = null;
            entity.ModifyID = null;
            entity.ModifyOn = null;
            entity.OriginalModelID = input.Id;
            //entity.PlushModelChecked = null;
            //entity.PlushModelCheckMessage = null;
            var newID = await repository.InsertAndGetIdAsync(entity);
            if (modelModelElems.Any())
            {
                foreach (var item in modelModelElems)
                {
                    item.Id = Guid.Empty;
                    item.ModelID = newID;
                    await modelModelElemRepository.InsertAsync(item);
                }
            }
            if (modelSizeColumns.Any())
            {
                foreach (var item in modelSizeColumns)
                {
                    item.Id = Guid.Empty;
                    item.ModelID = newID;
                    await modelSizeColumnRepository.InsertAsync(item);
                }
            }
            if (modelBodyLists.Any())
            {
                foreach (var item in modelBodyLists)
                {
                    item.Id = Guid.Empty;
                    item.ModelID = newID;
                    await modelBodyLisyRepository.InsertAsync(item);
                }
            }
            if (clientModels.Any())
            {
                foreach (var item in clientModels)
                {
                    item.Id = Guid.Empty;
                    item.ModelID = newID;
                    await clientModelRepository.InsertAsync(item);
                }
            }
        }


        ///// <summary>
        ///// 版型状态修改
        ///// </summary>
        ///// <returns></returns>
        //private async Task Edit(MOM_ModelDto newModel, Model oldModel = null)
        //{

        //}

    }
}

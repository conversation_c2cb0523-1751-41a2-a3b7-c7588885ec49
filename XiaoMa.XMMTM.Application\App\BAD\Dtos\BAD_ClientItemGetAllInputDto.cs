﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientItemGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2021/6/30/星期三 16:51:24
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientItemGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ClientItemGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? ClientID { set; get; }
    }
}

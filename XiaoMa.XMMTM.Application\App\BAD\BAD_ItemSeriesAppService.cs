﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemSeriesAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 19:45:37
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ItemSeriesAppService : XMMTMAppServiceBase, IBAD_ItemSeriesAppService
    {
        private readonly IRepository<ItemSeries, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public BAD_ItemSeriesAppService(
       IRepository<ItemSeries, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ItemSeriesGetAllOutputDto>> Get(BAD_ItemSeriesGetAllInputDto input)
        {
            var query = repository.GetAll()
              //.Where(a => a.IsActive)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text)).Select(a => new BAD_ItemSeriesGetAllOutputDto()
              {
                  Id = a.Id,
                  Code = a.Code,
                  CodeName = a.CodeName,
                  CreateBy = a.CreateBy,
                  CreateID = a.CreateID,
                  CreateOn = a.CreateOn,
                  IsActive = a.IsActive,
                  ModifyBy = a.ModifyBy,
                  ModifyID = a.ModifyID,
                  ModifyOn = a.ModifyOn,
                  Remark = a.Remark,
                  SizeCodeType = a.SizeCodeType,
                  SizeCodeTypeText = a.SizeCodeType.GetDescription()
              });
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<BAD_ItemSeriesGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ItemSeriesGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ItemSeriesDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ItemSeries>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ItemSeriesDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ItemSeriesDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ItemSeriesDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
            }

        }
    }
}

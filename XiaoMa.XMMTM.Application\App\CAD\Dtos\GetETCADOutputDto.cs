using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.CAD.Dtos;

namespace XiaoMa.XMMTM.App.CAD.Dtos
{
  public  class GetETCADOutputDto
    {
        /// <summary>
        /// 0表示成功，100表示没有可下载数据了，其他表示失败
        /// </summary>
        public int errcode { set; get; } = 0;
        public string errmsg { set; get; }
        public string token { set; get; }
        /// <summary>
        /// 用于确认下载成功的回调地址，此地址中应该包含参数
        /// </summary>
        public string callback { set; get; }
        /// <summary>
        /// 上传 svg 地址，可以没有
        /// </summary>
        public string upload { set; get; }
        /// <summary>
        /// 工作包
        /// </summary>
        public ETCADDto data { set; get; }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/3/星期一 16:54:45
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;
using XiaoMa.XMMTM.Domain.MOM;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_SizeDto : EntityDto<Guid?>, ISizeBaseDto
    {
        public string CodeName { get; set; }

        public string Code { get; set; }
        public string Remark { set; get; }
        public virtual bool IsActive { set; get; }
        /// <summary>
        /// CAD版型
        /// </summary>

        public string CadModelCode { get; set; }
        /// <summary>
        /// CAD版型
        /// </summary>

        public string CadSizeCode { get; set; }

        /// <summary>
        /// 规格名称
        /// </summary>

        public string SizeName { get; set; }

        /// <summary>
        /// 身高
        /// </summary>

        public decimal Height { get; set; }

        /// <summary>
        /// 尖下口前衣长
        /// </summary>

        public decimal SharpFrontLength { get; set; }

        /// <summary>
        /// 前衣长
        /// </summary>

        public decimal FrontLength { get; set; }

        /// <summary>
        /// 后中长
        /// </summary>

        public decimal BackMiddleLength { get; set; }

        /// <summary>
        /// 胸围
        /// </summary>

        public decimal Bust { get; set; }

        /// <summary>
        /// 中腰
        /// </summary>

        public decimal MiddleWaist { get; set; }

        /// <summary>
        /// 肩宽
        /// </summary>

        public decimal ShoulderWidth { get; set; }

        /// <summary>
        /// 小肩宽
        /// </summary>

        public decimal SmallShoulderWidth { get; set; }

        /// <summary>
        /// 袖长
        /// </summary>

        public decimal SleeveLength { get; set; }

        /// <summary>
        /// 衬衣领围
        /// </summary>

        public decimal Collar { get; set; }

        /// <summary>
        /// 背宽
        /// </summary>

        public decimal BackWidth { get; set; }

        /// <summary>
        /// 半背宽
        /// </summary>

        public decimal HalfBackWidth { get; set; }

        /// <summary>
        /// 臂围/袖肥
        /// </summary>

        public decimal SleeveWidth { get; set; }

        /// <summary>
        /// 半袖肥
        /// </summary>

        public decimal HalfSleeveWidth { get; set; }

        /// <summary>
        /// 袖口
        /// </summary>

        public decimal Cuff { get; set; }

        /// <summary>
        /// 半袖口
        /// </summary>

        public decimal HalfCuff { get; set; }

        /// <summary>
        /// 上衣臀围
        /// </summary>

        public decimal JacketHipline { get; set; }

        /// <summary>
        /// 下摆
        /// </summary>

        public decimal Hem { get; set; }

        /// <summary>
        /// 毛裤长
        /// </summary>

        public decimal TrouserLong { get; set; }

        /// <summary>
        /// 净裤长
        /// </summary>

        public decimal NetTrouserLong { get; set; }

        /// <summary>
        /// 毛内裤长
        /// </summary>

        public decimal InnerTrouserLong { get; set; }

        /// <summary>
        /// 净内裤长
        /// </summary>

        public decimal NetInnerTrouserLong { get; set; }

        /// <summary>
        /// 腰围
        /// </summary>

        public decimal Waist { get; set; }

        /// <summary>
        /// 腰绳
        /// </summary>

        public decimal Yaosheng { get; set; }

        /// <summary>
        /// 腰围（拉前）
        /// </summary>

        public decimal Waist1 { get; set; }

        /// <summary>
        /// 腰围（拉后）
        /// </summary>

        public decimal Waist2 { get; set; }

        /// <summary>
        /// 臀围
        /// </summary>

        public decimal Hipline { get; set; }

        /// <summary>
        /// 腿围（裆下5CM）
        /// </summary>

        public decimal SmallCrossCrotch { get; set; }

        /// <summary>
        /// 小半横档
        /// </summary>

        public decimal HalfSmallCrossCrotch { get; set; }

        /// <summary>
        /// 腿根围/横档
        /// </summary>

        public decimal CrossCrotch { get; set; }

        /// <summary>
        /// 半横档
        /// </summary>

        public decimal HalfCrossCrotch { get; set; }

        /// <summary>
        /// 膝围
        /// </summary>

        public decimal KneeGirth { get; set; }

        /// <summary>
        /// 半膝围
        /// </summary>

        public decimal HalfKneeGirth { get; set; }

        /// <summary>
        /// 前直裆
        /// </summary>

        public decimal FrontCrotch { get; set; }

        /// <summary>
        /// 前浪长
        /// </summary>

        public decimal FrontWaveLength { get; set; }

        /// <summary>
        /// 后浪长
        /// </summary>

        public decimal BackWaveLength { get; set; }

        /// <summary>
        /// 脚口
        /// </summary>

        public decimal TrouserBottom { get; set; }

        /// <summary>
        /// 半脚口
        /// </summary>

        public decimal HalfTrouserBottom { get; set; }

        /// <summary>
        /// 通裆
        /// </summary>

        public decimal ThroughCrotch { get; set; }

        /// <summary>
        /// 立裆
        /// </summary>

        public decimal StandCrotch { get; set; }

        /// <summary>
        /// 拉链长
        /// </summary>

        public decimal ZipperLength { get; set; }

        /// <summary>
        /// 规格单ID
        /// </summary>

        public Guid SizeListID { get; set; }

        /// <summary>
        /// 身高ID
        /// </summary>

        public Guid? SizeElemaID { get; set; }

        /// <summary>
        /// 胸围ID
        /// </summary>

        public Guid? SizeElembID { get; set; }

        /// <summary>
        /// 体型ID
        /// </summary>

        public Guid? SizeElemcID { get; set; }

        /// <summary>
        /// 臀围ID
        /// </summary>

        public Guid? SizeElemdID { get; set; }



        /// <summary>
        /// 左内裤长
        /// </summary>
        public decimal LNetInnerTrouserLong { get; set; } // 左内裤长
        /// <summary>
        /// 右内裤长
        /// </summary>
        public decimal RNetInnerTrouserLong { get; set; } //右内裤长


        //public decimal SmallShoulderWidth { get; set; } //小肩宽
        //public decimal HalfBackWidth { get; set; } //半背宽


        /// <summary>
        /// 右内袖长
        /// </summary>
        public decimal RightinSleeveLength { get; set; } // 右内袖长
        /// <summary>
        /// 左内袖长
        /// </summary>
        public decimal LeftinSleeveLength { get; set; } //左内袖长
        /// <summary>
        /// 半臀围
        /// </summary>
        public decimal HalfHipline { get; set; } //半臀围
        /// <summary>
        /// 半胸围
        /// </summary>
        public decimal HalfBust { get; set; } //半胸围
        /// <summary>
        /// 半腰围
        /// </summary>
        public decimal HalfWaist { get; set; } // 半腰围
        /// <summary>
        /// 半腰围
        /// </summary>
        public decimal HalfMiddleWaist { get; set; } //半腰围
        /// <summary>
        /// 半下摆
        /// </summary>
        public decimal HalfHem { get; set; } //半下摆
        /// <summary>
        /// 裆下44CM
        /// </summary>
        public decimal Half44CrossCrotch { get; set; } //裆下44CM
        /// <summary>
        /// 裆下15CM
        /// </summary>
        public decimal Half15CrossCrotch { get; set; } //裆下15CM
        /// <summary>
        /// 半臀围
        /// </summary>
        public decimal HalfJacketHipline { get; set; } //半臀围

        /// <summary>
        /// 右外袖长
        /// </summary>
        public decimal RightOutSleeveLength { get; set; } // 
        /// <summary>
        /// 左外袖长
        /// </summary>
        public decimal LeftOutSleeveLength { get; set; } // 
        /// <summary>
        /// 左外裤长
        /// </summary>
        public decimal LeftOutTrouserLong { get; set; } // 
        /// <summary>
        /// 右外裤长
        /// </summary>
        public decimal RightOutTrouserLong { get; set; } // 
        /// <summary>
        /// 裙长
        /// </summary>

        public decimal Skirtlength { set; get; }
        public decimal HalfMiddleWaistOpen { set; get; }
    }
}

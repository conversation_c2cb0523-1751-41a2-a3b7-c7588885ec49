﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ElemRuleOutputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/11/25/星期三 17:23:37
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class ElemRuleOutputDto
    {
        public Guid ModelElemListID { set; get; }
        public Guid? ModelElemID { get; set; }

        public Guid? ItemID { get; set; }
        public decimal? Qty { get; set; }
        public RuleTypeEnum type { set; get; }
    }
}

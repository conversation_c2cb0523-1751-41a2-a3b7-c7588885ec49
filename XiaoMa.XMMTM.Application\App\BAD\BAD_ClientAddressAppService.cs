﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientAddressAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/24/星期五 17:00:10
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientAddressAppService : XMMTMAppServiceBase, IBAD_ClientAddressAppService
    {
        private readonly IRepository<ClientAddress, Guid> repository;
        private readonly IObjectMapper objectMapper;
        public BAD_ClientAddressAppService(
       IRepository<ClientAddress, Guid> repository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientAddressGetAllOutputDto>> Get(BAD_ClientAddressGetAllInputDto input)
        {
            var query = repository.GetAll()
              .Where(a =>a.ClientID==input.Id)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.Contact.Contains(input.Text) || a.ContactDesc.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<BAD_ClientAddressGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ClientAddressGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<BAD_ClientAddressDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = ObjectMapper.Map<ClientAddress>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientAddressDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientAddressDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        //protected async Task<bool> ExistCodeAsync(BAD_ClientAddressDto input)
        //{

        //    if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
        //    {
        //        return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code));
        //    }
        //    else
        //    {
        //        return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value);
        //    }

        //}
    }
}

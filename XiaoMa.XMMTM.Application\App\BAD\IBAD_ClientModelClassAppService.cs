﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ClientModelClassAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2021/3/2/星期二 14:40:51
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ClientModelClassAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ClientModelClassGetAllOutputDto>> Get(BAD_ClientModelClassGetAllInputDto input);
        Task Updates(List<BAD_ClientModelClassDto> input);
        Task UpdatePlus();
        Task InheritedParentModel(Guid ClientID);

        Task ClearPlus();
    }
}

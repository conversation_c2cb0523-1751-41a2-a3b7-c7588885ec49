﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeColumnImageAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/10/15/星期四 14:29:01
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeColumnImageAppService : XMMTMAppServiceBase, IMOM_SizeColumnImageAppService
    {
        private readonly IRepository<SizeColumnImage, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly IObjectMapper objectMapper;
        private readonly FileServer.FileServer fileServer;

        public MOM_SizeColumnImageAppService(
       IRepository<SizeColumnImage, Guid> repository,
       IRepository<Model, Guid> modelRepository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<ModelImage, Guid> modelImageRepository,
       IObjectMapper objectMapper,
     FileServer.FileServer fileServer
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.modelImageRepository = modelImageRepository;
            this.objectMapper = objectMapper;
            this.fileServer = fileServer;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeColumnImageGetAllOutputDto>> Get(MOM_SizeColumnImageGetAllInputDto input)
        {
            var query = from t in repository.GetAll()
                        join t1x in modelRepository.GetAll() on t.ModelID equals t1x.Id into t1xx
                        from t1 in t1xx.DefaultIfEmpty()
                        join t2 in sizeColumnRepository.GetAll() on t.SizeColumnID equals t2.Id
                        join t3 in modelImageRepository.GetAll() on t.ModelImageID equals t3.Id
                        join t4x in modelImageRepository.GetAll() on t.ModelImageID1 equals t4x.Id into t4xx
                        from t4 in t4xx.DefaultIfEmpty()
                        select new MOM_SizeColumnImageGetAllOutputDto
                        {
                            Id = t.Id,
                            SizeColumnID = t.SizeColumnID,
                            IsActive = t.IsActive,
                            ModelID = t.ModelID,
                            ModelImageID = t.ModelImageID,
                            ModelImageID1 = t.ModelImageID1,
                            Remark = t.Remark,
                            ModelName = t1.Code,
                            SizeColumnName = t2.CodeName,
                            ModelImage1Path = fileServer.GetImageUrl(t4.ImagePath),
                            ModelImagePath = fileServer.GetImageUrl(t3.ImagePath),
                            SizeColumnCode = t2.Code,
                            ModifyOn = t.ModifyOn,
                            ModifyID = t.ModifyID,
                            ModifyBy = t.ModifyBy,
                            CreateOn = t.CreateOn,
                            CreateID = t.CreateID,
                            CreateBy = t.CreateBy,
                            ImagePath1 = t3.ImagePath,
                            ImagePath2 = t4.ImagePath,
                            ModelImage1 = t3.Code + "" + t3.CodeName,
                            ModelImage2 = t4.Code + "" + t4.CodeName,
                        };

            query = query
                .WhereIf(input.SizeColumnID.HasValue, a => a.SizeColumnID == input.SizeColumnID.Value)
                .WhereIf(input.ModelID.HasValue, a => a.ModelID == input.ModelID.Value)
                .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.SizeColumnName.Contains(input.Text) || a.SizeColumnCode.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SizeColumnImageGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeColumnImageGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeColumnImageDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<SizeColumnImage>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeColumnImageDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeColumnImageDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }


    }
}

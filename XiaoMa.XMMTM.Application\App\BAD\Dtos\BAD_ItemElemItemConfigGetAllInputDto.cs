﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ItemElemItemConfigGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/12/28/星期一 10:20:07
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ItemElemItemConfigGetAllInputDto查询参数带分页
    /// </summary>
    public class BAD_ItemElemItemConfigGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        /// <summary>
        /// 物料类别
        /// </summary>
        public BAD_ItemClass? ItemClassID { set; get; }

        public Guid? GroupID { set; get; }
    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientPersonGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/7/24/星期五 17:13:54
-----------------------------------------------*/

using Abp.AutoMapper;
using System;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD.Dtos
{
    /// <summary>
    /// BAD_ClientPersonGetAllOutputDto查询返回实体对象
    /// </summary>
    [AutoMap(typeof(ClientPerson))]
    public class BAD_ClientPersonGetAllOutputDto : ClientPerson
    {

    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    BAD_ClientPersonAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/7/24/星期五 17:06:41
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Domain.BAD;
using XiaoMa.XMMTM.FileServer;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class BAD_ClientPersonAppService : XMMTMAppServiceBase, IBAD_ClientPersonAppService
    {
        private readonly IRepository<ClientPerson, Guid> repository;
        private readonly IRepository<ClientPersonImage, Guid> clientPersonImagerepository;
        private readonly IRepository<SorderDetailImage, Guid> sorderDetailImageRepository;
        private readonly IRepository<SorderDetail, Guid> sorderDetailRepository;
        private readonly IRepository<Sorder, Guid> sorderRepository;
        private readonly IObjectMapper objectMapper;
        private readonly IFileServer fileServer;

        public BAD_ClientPersonAppService(
       IRepository<ClientPerson, Guid> repository,
       IRepository<ClientPersonImage, Guid> clientPersonImagerepository,
       IRepository<SorderDetailImage, Guid> sorderDetailImageRepository,
       IRepository<SorderDetail, Guid> sorderDetailRepository,
       IRepository<Sorder, Guid> sorderRepository,
       IObjectMapper objectMapper,
          IFileServer fileServer
         )
        {
            this.repository = repository;
            this.clientPersonImagerepository = clientPersonImagerepository;
            this.sorderDetailImageRepository = sorderDetailImageRepository;
            this.sorderDetailRepository = sorderDetailRepository;
            this.sorderRepository = sorderRepository;
            this.objectMapper = objectMapper;
            this.fileServer = fileServer;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<BAD_ClientPersonGetAllOutputDto>> Get(BAD_ClientPersonGetAllInputDto input)
        {
            var query = repository.GetAll()
              .Where(a => a.ClientID == input.Id)
              .WhereIf(input.IsActive.HasValue, a => a.IsActive == input.IsActive.Value)
              .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.CodeName.Contains(input.Text) || a.Code.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.IsActive).ThenByDescending(a => a.CreateOn).PageBy(input).ToListAsync();
            var list = objectMapper.Map<List<BAD_ClientPersonGetAllOutputDto>>(result);
            return new PagedResultDto<BAD_ClientPersonGetAllOutputDto>(count, list);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<List<BAD_ClientPersonGetAllOutputDto>> Adds(List<BAD_ClientPersonDto> input)
        {
            var list = new List<BAD_ClientPersonGetAllOutputDto>();
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<ClientPerson>(entity);
                oldentity.Id = Guid.NewGuid();
                await repository.InsertAndGetIdAsync(oldentity);
                list.Add(objectMapper.Map<BAD_ClientPersonGetAllOutputDto>(oldentity));
            }
            return list;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<BAD_ClientPersonDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<BAD_ClientPersonDto> input)
        {
            foreach (var entity in input)
            {
                var personimage = await clientPersonImagerepository.GetAll().Where(a => a.ClientPersonID == entity.Id.Value).Select(a => new { a.Id, a.ImageUrl }).ToListAsync();
                var imageids = personimage.Select(a => a.Id).ToList();
                if (await sorderDetailImageRepository.GetAll().AnyAsync(a => a.ClientPersonID == entity.Id.Value && imageids.Contains(a.ImageID)))
                {
                    throw new UserFriendlyException("已有订单使用此顾客照片,请删除订单中的顾客照片,再删除此照片");
                }
                var sorderany =await (from t in sorderDetailRepository.GetAll()
                                 join t1x in sorderRepository.GetAll() on t.SorderID equals t1x.Id into t1xx
                                 from t1 in t1xx.DefaultIfEmpty()
                                 where !t1.IsDeleted && t.ClientPersonID == entity.Id
                                 select t1.Code).Distinct().ToListAsync();
                if (sorderany.Any())
                {
                    var str = string.Join(",",sorderany);
                    throw new UserFriendlyException($"订单{str}关联此顾客,请先删除订单后再操作！！！");
                }
                foreach (var image in personimage)
                {
                    fileServer.RemoveImage(image.ImageUrl);
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(BAD_ClientPersonDto input)
        {

            if (!input.Id.HasValue || Guid.Empty == input.Id.Value)
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.ClientID == input.ClientID);
            }
            else
            {
                return await repository.GetAll().AnyAsync(a => a.Code.Equals(input.Code) && a.Id != input.Id.Value && a.ClientID == input.ClientID);
            }

        }
    }
}

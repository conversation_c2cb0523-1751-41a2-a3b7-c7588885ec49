﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/3/星期一 16:54:50
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_SizeGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {
        public Guid? SizeListID { set; get; }
        public Guid? Id { set; get; }
        public Guid? ModelId { set; get; }
        public Guid? SizeElemaID { set; get; }
        public Guid? SizeElembID { set; get; }
        public Guid? SizeElemcID { set; get; }
        public Guid? SizeElemdID { set; get; }

        public List<Guid> SizeElemaIDs { set; get; }
        public List<Guid> SizeElembIDs { set; get; }
        public List<Guid> SizeElemcIDs { set; get; }
        public List<Guid> SizeElemdIDs { set; get; }

    }
}

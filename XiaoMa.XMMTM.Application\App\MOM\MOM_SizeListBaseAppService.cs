﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeListBaseAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/1/星期六 10:52:04
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeListBaseAppService : XMMTMAppServiceBase, IMOM_SizeListBaseAppService
    {
        private readonly IRepository<SizeListBase, Guid> repository;
        private readonly IRepository<SizeList, Guid> sizeListrepository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnrepository;
        private readonly IRepository<SizeElema, Guid> sizeElemaRepository;
        private readonly IRepository<SizeElemb, Guid> sizeElembRepository;
        private readonly IRepository<SizeElemc, Guid> sizeElemcRepository;
        private readonly IRepository<SizeElemd, Guid> sizeElemdRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeListBaseAppService(
       IRepository<SizeListBase, Guid> repository,
       IRepository<SizeList, Guid> sizeListrepository,
       IRepository<SizeColumn, Guid> sizeColumnrepository,
       IRepository<SizeElema, Guid> sizeElemaRepository,
       IRepository<SizeElemb, Guid> sizeElembRepository,
       IRepository<SizeElemc, Guid> sizeElemcRepository,
       IRepository<SizeElemd, Guid> sizeElemdRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sizeListrepository = sizeListrepository;
            this.sizeColumnrepository = sizeColumnrepository;
            this.sizeElemaRepository = sizeElemaRepository;
            this.sizeElembRepository = sizeElembRepository;
            this.sizeElemcRepository = sizeElemcRepository;
            this.sizeElemdRepository = sizeElemdRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeListBaseGetAllOutputDto>> Get(MOM_SizeListBaseGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join sl in sizeListrepository.GetAll() on t.SizeListID equals sl.Id
                         join t1x in sizeColumnrepository.GetAll() on t.SizeColumnID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in sizeElemaRepository.GetAll() on t.SizeElemaID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in sizeElembRepository.GetAll() on t.SizeElembID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in sizeElemcRepository.GetAll() on t.SizeElemcID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in sizeElemdRepository.GetAll() on t.SizeElemdID equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         where t.SizeListID == input.Id
                         select new MOM_SizeListBaseGetAllOutputDto()
                         {
                             Id = t.Id,
                             Stepa = t.Stepa,
                             Stepb = t.Stepb,
                             Stepc = t.Stepc,
                             Stepd = t.Stepd,
                             Value = t.Value,
                             MinValue = t.MinValue,
                             MaxValue = t.MaxValue,
                             Remark = t.Remark,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             SizeListID = t.SizeListID,
                             SizeColumnID = t.SizeColumnID,
                             SizeElemaID = t.SizeElemaID,
                             SizeElembID = t.SizeElembID,
                             SizeElemcID = t.SizeElemcID,
                             SizeElemdID = t.SizeElemdID,
                             SizeList = sl == null ? null : sl.Code + ": " + sl.CodeName,
                             SizeColumn = t1 == null ? null : t1.Code + ": " + t1.CodeName,
                             SizeElema = t2 == null ? null : t2.Code, // + ": " + t2.SizeElemaName,
                             SizeElemb = t3 == null ? null : t3.Code, //, + ": " + t3.SizeElembName,
                             SizeElemc = t4 == null ? null : t4.Code, // + ": " + t4.SizeElemcName,
                             SizeElemd = t5 == null ? null : t5.Code,// + ": " + t5.SizeElemdName,
                             Sequence = t1.Sequence,
                             AlgorithmType = t.AlgorithmType,
                             AlgorithmTypeText = t.AlgorithmType.HasValue ? t.AlgorithmType.Value.GetDescription() : "",
                             Sequence1 = t2.Sequence,
                             Sequence2 = t3.Sequence,
                             Sequence3 = t4.Sequence,
                             Sequence4 = t5.Sequence,
                         }).WhereIf(input.Id.HasValue, a => a.SizeListID == input.Id.Value);
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.Sequence).ThenBy(a=>a.Sequence1).ThenBy(a => a.Sequence2).ThenBy(a => a.Sequence3).ThenBy(a => a.Sequence4).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_SizeListBaseGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_SizeListBaseGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeListBaseDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = ObjectMapper.Map<SizeListBase>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeListBaseDto> input)
        {
            foreach (var entity in input)
            {

                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeListBaseDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Copy(SizeListBaseCopyInputDto input)
        {
            var list = await repository.GetAll().Where(a => a.SizeListID == input.OriginalID).AsNoTracking().ToListAsync();
            foreach (var item in list)
            {
                item.SizeListID = input.TargetID;
                item.Id = new Guid();
                await repository.InsertAsync(item);
            }
        }

    }
}

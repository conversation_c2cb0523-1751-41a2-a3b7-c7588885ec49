/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MES_ModelElemOutPutDto
// 功能描述：    
// 作者    zhangby
// 时间    2021/3/10/星期三 13:50:49
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class MES_ModelElemOutPutDto
    {
        public Guid ModelElemID { set; get; }

        public string ModelElemCode { set; get; }
        public string ModelElemName { set; get; }
        public int? ModelElemSort { set; get; }
        public Guid ModelElemListID { set; get; }
        public string ModelELemListCode { set; get; }
        public string ModelElemListName { set; get; }
        public int? ModelElemListSort { set; get; }
        public int ModelElemTypeID { set; get; }
        public string ModelElemTypeName { set; get; }
        public InaStyleCodeEnums? InaStyleCode { set; get; }
        public InaStyleIndexEnums? InaStyleIndex { set; get; }
        /// <summary>
        /// 款式部位ID
        /// </summary>
        public Guid ModelElemBaseID { get; set; }
        public int? ModelElemBaseSort { set; get; }
        public string ModelElemBaseCode { set; get; }
        public string ModelElemBaseName { set; get; }
        /// <summary>
        ///
        /// </summary>
        public Guid? GroupID { get; set; }
        public string GroupText { set; get; }
        /// <summary>
        /// 性别
        /// </summary>
        public bool GenderID { get; set; }
    }
}

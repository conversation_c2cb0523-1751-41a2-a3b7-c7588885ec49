# Core模块文件对比TodoList

## 📊 对比统计

- **总文件数**: 581个
- **两项目都存在**: 365个文件 ✅ 需要内容对比
- **仅源项目存在**: 62个文件 📥 需要复制到目标
- **仅目标项目存在**: 154个文件 📋 目标项目特有

## 🎯 操作说明

- ✅ 勾选需要处理的文件
- 🔍 **需要内容对比**: 检查两个文件的差异，决定是否覆盖
- 📥 **需要复制**: 从源项目复制到目标项目
- 📋 **目标特有**: 目标项目独有的文件，通常保留

---

## 📂 1. 两个项目都存在的文件 (365个) - 需要内容对比

### 🔴 高优先级文件 (核心配置和模块)

#### [ ] **XMMTMCoreModule.cs**
- 📁 路径: `XMMTMCoreModule.cs`
- 🎯 优先级: 🔴 极高 (模块配置)
- 💡 说明: ABP模块配置文件，影响整个Core模块的依赖和配置

#### [ ] **XMMTMConsts.cs**
- 📁 路径: `XMMTMConsts.cs`
- 🎯 优先级: 🔴 高 (常量定义)
- 💡 说明: 系统常量定义，可能影响多个模块

#### [ ] **AppVersionHelper.cs**
- 📁 路径: `AppVersionHelper.cs`
- 🎯 优先级: 🟡 中 (版本管理)
- 💡 说明: 应用版本管理工具

#### [ ] **RateLimitingActionFilter.cs**
- 📁 路径: `RateLimitingActionFilter.cs`
- 🎯 优先级: 🟡 中 (限流功能)
- 💡 说明: API限流过滤器

### 🟡 权限相关文件

#### [ ] **Authorization\PermissionNames.cs**
- 📁 路径: `Authorization\PermissionNames.cs`
- 🎯 优先级: 🔴 高 (权限定义)
- 💡 说明: 系统权限名称定义

#### [ ] **Authorization\XMMTMAuthorizationProvider.cs**
- 📁 路径: `Authorization\XMMTMAuthorizationProvider.cs`
- 🎯 优先级: 🔴 高 (权限提供者)
- 💡 说明: ABP权限提供者配置

#### [ ] **Authorization\Users\SSOUserPermissionCacheItem.cs**
- 📁 路径: `Authorization\Users\SSOUserPermissionCacheItem.cs`
- 🎯 优先级: 🟡 中 (用户权限缓存)
- 💡 说明: SSO用户权限缓存项

### 🔧 CAD相关文件

#### [ ] **CAD\CadKeyManager.cs**
- 📁 路径: `CAD\CadKeyManager.cs`
- 🎯 优先级: 🟡 中 (CAD密钥管理)
- 💡 说明: CAD系统密钥管理器

#### [ ] **CAD\ETCadManger.cs**
- 📁 路径: `CAD\ETCadManger.cs`
- 🎯 优先级: 🟡 中 (ET CAD管理)
- 💡 说明: ET CAD管理器

#### [ ] **CAD\ICadKeyManager.cs**
- 📁 路径: `CAD\ICadKeyManager.cs`
- 🎯 优先级: 🟡 中 (CAD接口)
- 💡 说明: CAD密钥管理器接口

### 📝 批量操作选项

#### [ ] **全选高优先级文件** (推荐先处理)
- 包含: 模块配置、权限定义、核心常量等
- 建议: 优先对比这些文件，它们影响系统核心功能

#### [ ] **全选中优先级文件**
- 包含: 工具类、管理器、缓存等
- 建议: 在高优先级文件处理完后进行

---

## 📥 2. 仅源项目存在的文件 (62个) - 需要复制

### 🔴 重要的缺失文件

#### [ ] **[待分析]** 
- 💡 说明: 需要查看具体的62个文件列表，识别重要的业务逻辑文件
- 🎯 建议: 优先复制业务相关的实体、服务、管理器等

---

## 📋 3. 仅目标项目存在的文件 (154个) - 目标特有

### 🟢 目标项目特有功能

#### [ ] **[待分析]**
- 💡 说明: 这些是目标项目(Shop)特有的功能
- 🎯 建议: 通常保留这些文件，除非确认需要删除

---

## 🚀 推荐处理顺序

1. **第一批**: 处理高优先级的共同文件 (模块配置、权限、常量)
2. **第二批**: 分析并复制重要的源项目独有文件
3. **第三批**: 处理中优先级的共同文件
4. **第四批**: 检查目标项目特有文件是否需要保留

## 📝 下一步操作

请告诉我您想要：
1. 先查看具体某个文件的详细差异对比
2. 获取完整的62个"仅源项目存在"文件列表
3. 获取完整的154个"仅目标项目存在"文件列表
4. 开始处理特定优先级的文件

---

**生成时间**: 2024-12-19 15:45  
**数据来源**: CoreModuleComparison.json  
**状态**: 等待用户选择处理方式

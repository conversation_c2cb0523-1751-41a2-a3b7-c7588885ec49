/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeRuleCheckAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2022年11月1日,星期二 13:39:35
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.UI;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_SizeRuleCheckAppService : XMMTMAppServiceBase, IMOM_SizeRuleCheckAppService
    {
        private readonly IRepository<SizeRuleCheck, Guid> repository;
        private readonly IRepository<SizeColumn, Guid> sizeColumnRepository;
        private readonly IRepository<BodyList, Guid> bodyListRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_SizeRuleCheckAppService(
       IRepository<SizeRuleCheck, Guid> repository,
       IRepository<SizeColumn, Guid> sizeColumnRepository,
       IRepository<BodyList, Guid> bodyListRepository,
       IRepository<Group, Guid> groupRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.sizeColumnRepository = sizeColumnRepository;
            this.bodyListRepository = bodyListRepository;
            this.groupRepository = groupRepository;
            this.objectMapper = objectMapper;
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_SizeRuleCheckGetAllOutputDto>> Get(MOM_SizeRuleCheckGetAllInputDto input)
        {
            var query = (from t in repository.GetAll()
                         join t1x in sizeColumnRepository.GetAll() on t.SizeColumnID equals t1x.Id into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in sizeColumnRepository.GetAll() on t.SizeColumnID1 equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t3x in groupRepository.GetAll() on t.GroupID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in sizeColumnRepository.GetAll() on t.SizeColumnID2 equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         join t5x in bodyListRepository.GetAll() on t.BodyListID1 equals t5x.Id into t5xx
                         from t5 in t5xx.DefaultIfEmpty()
                         join t6x in bodyListRepository.GetAll() on t.BodyListID2 equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                             // where t.IsActive
                         select new MOM_SizeRuleCheckGetAllOutputDto()
                         {
                             SizeRuleCheckType = t.SizeRuleCheckType,
                             SizeColumnID = t.SizeColumnID,
                             SorderType = t.SorderType,
                             CreateBy = t.CreateBy,
                             CreateID = t.CreateID,
                             CreateOn = t.CreateOn,
                             Id = t.Id,
                             IsActive = t.IsActive,
                             IsCheckNull = t.IsCheckNull,
                             IsManualMaxValue1 = t.IsManualMaxValue1,
                             IsManualMaxValue2 = t.IsManualMaxValue2,
                             IsManualMinValue1 = t.IsManualMinValue1,
                             IsManualMinValue2 = t.IsManualMinValue2,
                             MaxValue = t.MaxValue,
                             MinValue = t.MinValue,
                             ModifyBy = t.ModifyBy,
                             ModifyID = t.ModifyID,
                             ModifyOn = t.ModifyOn,
                             SizeColumnID1 = t.SizeColumnID1,
                             SorderSizeType = t.SorderSizeType,
                             Value = t.Value,
                             SizeColumnNameText = t1.CodeName,
                             SizeColumnNameText1 = t2.CodeName,
                             SizeColumnNameText2 = t4.CodeName,
                             SizeRuleCheckTypeText = t.SizeRuleCheckType.GetDescription(),
                             SorderSizeTypeText = t.SorderSizeType.GetDescription(),
                             SorderTypeText = t.SorderType.GetDescription(),
                             GroupID = t.GroupID,
                             GroupText = t3.CodeName,
                             Remark = t.Remark,
                             Sort = t.Sort,
                             SizeColumnID2 = t.SizeColumnID2,
                             BodyListID1 = t.BodyListID1,
                             BodyListID2 = t.BodyListID2,
                             BodyListText1 = t5.Code + ":" + t5.CodeName,
                             BodyListText2 = t6.Code + ":" + t6.CodeName,
                             HasSizeElemB = t.HasSizeElemB,
                             HasSizeElemC = t.HasSizeElemC,
                             HasSizeElemD = t.HasSizeElemD,
                             HasSizeElemA = t.HasSizeElemA,
                         })
                         .WhereIf(input.SorderType.HasValue, a => a.SorderType == input.SorderType.Value)
                         .WhereIf(input.SorderSizeType.HasValue, a => a.SorderSizeType == input.SorderSizeType.Value)
                         .WhereIf(input.SizeRuleCheckType.HasValue, a => a.SizeRuleCheckType == input.SizeRuleCheckType.Value)
                         .WhereIf(input.SizeColumnID.HasValue, a => a.SizeColumnID == input.SizeColumnID.Value)
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.SizeColumnNameText.ToLower().Trim().Contains(input.Text.ToLower().Trim()) || a.SizeColumnNameText1.ToLower().Trim().Contains(input.Text.ToLower().Trim()));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a=>a.SorderSizeType).ThenBy(a=>a.SizeColumnID).ThenByDescending(a=>a.CreateOn).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_SizeRuleCheckGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_SizeRuleCheckDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = ObjectMapper.Map<SizeRuleCheck>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_SizeRuleCheckDto> input)
        {
            foreach (var entity in input)
            {
                if (await this.ExistCodeAsync(entity))
                {
                    throw new UserFriendlyException("已经存在相同的编码");
                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_SizeRuleCheckDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }

        protected async Task<bool> ExistCodeAsync(MOM_SizeRuleCheckDto input)
        {

            var any = await repository.GetAll().WhereIf(input.Id.HasValue, a => a.Id != input.Id.Value)
                .WhereIf(input.SizeColumnID1.HasValue, a => a.SizeColumnID1.Value == input.SizeColumnID1.Value)
                .WhereIf(input.GroupID.HasValue, a => a.GroupID.Value == input.GroupID.Value)
                .WhereIf(!input.GroupID.HasValue || input.GroupID.Value == Guid.Empty, a => !a.GroupID.HasValue || a.GroupID.Value == Guid.Empty)
                .WhereIf(!input.SizeColumnID1.HasValue || input.SizeColumnID1.Value == Guid.Empty, a => !a.SizeColumnID1.HasValue || a.SizeColumnID1.Value == Guid.Empty)
                .AnyAsync(a => a.SorderSizeType == input.SorderSizeType && a.SorderType == input.SorderType && a.SizeColumnID == input.SizeColumnID && a.SizeRuleCheckType == input.SizeRuleCheckType);
            return any;

        }
    }
}

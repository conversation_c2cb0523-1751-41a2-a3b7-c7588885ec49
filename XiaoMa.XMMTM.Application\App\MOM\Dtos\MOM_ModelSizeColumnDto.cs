﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelSizeColumnDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/7/星期五 17:10:09
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using System;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelSizeColumnDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelSizeColumnDto : EntityDto<Guid?>
    {

        public Guid ModelID { set; get; }

        public Guid SizeColumnID { set; get; }
        public bool IsActive { set; get; } = true;

        public bool IsRequired { set; get; }

        public bool Selected { set; get; }
    }
}

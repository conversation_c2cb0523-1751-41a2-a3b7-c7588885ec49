/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelElemGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/7/28/星期二 16:20:25
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.Enums;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelElemGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelElemGetAllInputDto2 : PagedInput  //根据实际情况选择是否继承
    {
        public Guid ? GroupID { set; get; }

        public bool ? GenderID { set; get; }
        public int?  ModelElemType { set; get; }

        public Guid? ModelElemBaseID { set; get; }  

        public Guid? ModelElemListID { set; get; }
        /// <summary>
        /// 衣拿吊挂款式编码生成规则编码
        /// </summary>
        public InaStyleCodeEnums? InaStyleCode { set; get; }
    }
}

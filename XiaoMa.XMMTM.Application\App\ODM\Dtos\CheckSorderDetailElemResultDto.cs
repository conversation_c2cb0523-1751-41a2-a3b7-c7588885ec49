﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    CheckSorderDetailElemResultDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/12/12/星期六 16:16:15
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;


namespace XiaoMa.XMMTM.App.ODM.Dtos
{
    public class CheckSorderDetailElemResultDto
    {
        public CheckSorderDetailElemResultDto()
        {
            this.DetailModels = new List<DetailModelDto>();
        }
        public List<DetailModelDto> DetailModels { set; get; }
    }

    public class DetailModelDto
    {
        public Guid SorderDetailModelID { set; get; }
        public bool IsChecked { set; get; } = true;
        public string Message { set; get; }
        public Guid ModelID { set; get; }
    }
}

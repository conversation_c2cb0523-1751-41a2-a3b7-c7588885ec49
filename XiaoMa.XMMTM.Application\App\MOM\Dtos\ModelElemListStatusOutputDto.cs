/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ModelElemListStatusOutputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/12/14/星期一 9:31:23
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;


namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class ModelElemListStatusOutputDto
    {
        public Guid ModelElemListID { set; get; }

        /// <summary>
        /// 内容必输
        /// </summary>
        public bool InputRequired { set; get; }
        /// <summary>
        /// 物料必输
        /// </summary>
        public bool ItemRequired { set; get; }
        /// <summary>
        /// 款式明细必输
        /// </summary>
        public bool ModelElemRequired { set; get; }
        public bool ImageRequired { set; get; }
        /// <summary>
        /// 数量必输
        /// </summary>
        public bool QtyRequired { set; get; }
    }
}

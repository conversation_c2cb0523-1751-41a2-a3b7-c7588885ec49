﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeElemDtoGetAllOutputDto
// 功能描述：    
// 作者    zhangby
// 时间    2020/8/3/星期一 16:45:48
-----------------------------------------------*/

using System;
using System.Collections.Generic;
using System.Text;


namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    public class MOM_SizeElemDtoGetAllOutputDto
    {
        // ********************
        /// Tree：树
        // ********************
        //public int id { get; set; } //id必须不同
        public string text { get; set; }
        //public bool leaf { get; set; }
        //public bool expanded { get; set; }
        public virtual List<MOM_SizeElemDtoGetAllOutputDto> children { get; set; }

        public bool Selected { get; set; } //选中

        public MOM_SizeElemDtoGetAllOutputDto()
        {
            //this.leaf = false;
            //this.expanded = true;
            this.Selected = false;
            this.children = new List<MOM_SizeElemDtoGetAllOutputDto>();
        }
        // ********************
        /// Content：内容
        // ********************
        public string SizeElemType { get; set; } //虚拟字段，用于分辨ABCD
        public Guid? ID { get; set; }
        public int? Sequence { get; set; }
        public Guid? CreateID { get; set; }
        public string CreateBy { get; set; }
        public DateTime? CreateOn { get; set; }
        public Guid? ModifyID { get; set; }
        public string ModifyBy { get; set; }
        public DateTime? ModifyOn { get; set; }
        public Guid SizeListID { get; set; }
        public Guid SizeElemID { get; set; }
    }
}

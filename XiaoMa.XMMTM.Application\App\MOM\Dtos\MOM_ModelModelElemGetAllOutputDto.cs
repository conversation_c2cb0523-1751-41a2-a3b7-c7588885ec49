/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:22
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelModelElemGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_ModelModelElemGetAllOutputDto
    {
        public Guid ModelElemID { set; get; }
        public Guid ModelID { set; get; }
        public bool GenderID { set; get; }

        public string ModelCode { set; get; }

        public string ModelName { set; get; }

        public Guid GroupID { set; get; }

        public string GroupCode { set; get; }

        public string GroupName { set; get; }
        public string BusinessSubTypeText { set; get; }
        public string ModelGroupName { set; get; }
        /// <summary>
        ///业务类型
        /// </summary>

        public ModelBusinessSubType BusinessSubType { get; set; }
        public Guid? Id { set; get; }
        public bool Selected { set; get; }
        public bool Default { set; get; }

        public Guid? CreateID { set; get; }

        public string CreateBy { set; get; }
        public DateTime? CreateOn { set; get; }

        public Guid? ModifyID { set; get; }
        public string ModifyBy { set; get; }
        public DateTime? ModifyOn { set; get; }
        public Guid? ModelGroupID { set; get; }
    }
}

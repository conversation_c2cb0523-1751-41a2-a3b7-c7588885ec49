﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelImageGetAllInputDto
// 功能描述：    查询参数带分页
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:22
-----------------------------------------------*/

using System;
using System.ComponentModel.DataAnnotations;
using XiaoMa.XMMTM.Dtos;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelImageGetAllInputDto查询参数带分页
    /// </summary>
    public class MOM_ModelImageGetAllInputDto : PagedInput  //根据实际情况选择是否继承
    {

        public SYS_ModelImageType? ModelImageTypeID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public SYS_Position? PositionID { get; set; }

        public string Remark { set; get; }

    }
}

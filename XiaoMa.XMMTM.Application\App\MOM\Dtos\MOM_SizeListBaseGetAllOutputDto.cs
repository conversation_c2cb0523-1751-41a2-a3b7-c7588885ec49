﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_SizeListBaseGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/1/星期六 10:52:30
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_SizeListBaseGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_SizeListBaseGetAllOutputDto : SizeListBase
    {
        public string SizeList { set; get; }
        public string SizeColumn { set; get; }
        public string SizeElema { set; get; }
        public string SizeElemb { set; get; }
        public string SizeElemc { set; get; }
        public string SizeElemd { set; get; }
        public string AlgorithmTypeText { set; get; }

        public int Sequence { set; get; }
        public int? Sequence1 { set; get; }
        public int? Sequence2 { set; get; }
        public int? Sequence3 { set; get; }
        public int? Sequence4 { set; get; }
    }
}

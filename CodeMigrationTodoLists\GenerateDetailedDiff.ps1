# Generate Detailed Diff for Specific Files
param(
    [string]$FileName,
    [string]$SourcePath = "e:\workgit\xm\XiaoMa.MTM\XiaoMa.XMMTM.Core",
    [string]$TargetPath = "e:\workgit\Shop\XiaoMa.MTM\XiaoMa.Shop.XMMTM.Core"
)

function Get-FileDiff {
    param(
        [string]$SourceFile,
        [string]$TargetFile,
        [string]$RelativePath
    )
    
    Write-Host "Analyzing detailed differences for: $RelativePath" -ForegroundColor Cyan
    
    try {
        $sourceContent = Get-Content $SourceFile -Raw -Encoding UTF8
        $targetContent = Get-Content $TargetFile -Raw -Encoding UTF8
        
        $sourceLines = $sourceContent -split "`n"
        $targetLines = $targetContent -split "`n"
        
        # Find differences
        $differences = @()
        $maxLines = [Math]::Max($sourceLines.Count, $targetLines.Count)
        
        for ($i = 0; $i -lt $maxLines; $i++) {
            $sourceLine = if ($i -lt $sourceLines.Count) { $sourceLines[$i].TrimEnd() } else { "" }
            $targetLine = if ($i -lt $targetLines.Count) { $targetLines[$i].TrimEnd() } else { "" }
            
            if ($sourceLine -ne $targetLine) {
                # Check if it's just a namespace difference
                $normalizedSource = $sourceLine -replace "XiaoMa\.XMMTM", "XiaoMa.Shop.XMMTM"
                $isNamespaceOnly = ($normalizedSource -eq $targetLine)
                
                $differences += [PSCustomObject]@{
                    LineNumber = $i + 1
                    SourceLine = $sourceLine
                    TargetLine = $targetLine
                    IsNamespaceOnly = $isNamespaceOnly
                    DiffType = if ($isNamespaceOnly) { "Namespace" } else { "Content" }
                }
            }
        }
        
        # Analyze using statements
        $sourceUsings = $sourceLines | Where-Object { $_ -match "^using\s+" } | ForEach-Object { $_.Trim() }
        $targetUsings = $targetLines | Where-Object { $_ -match "^using\s+" } | ForEach-Object { $_.Trim() }
        
        $missingInTarget = $sourceUsings | Where-Object { $_ -notin $targetUsings }
        $extraInTarget = $targetUsings | Where-Object { $_ -notin $sourceUsings }
        
        # Analyze namespace declarations
        $sourceNamespace = ($sourceLines | Where-Object { $_ -match "^namespace\s+" } | Select-Object -First 1)
        $targetNamespace = ($targetLines | Where-Object { $_ -match "^namespace\s+" } | Select-Object -First 1)
        
        return [PSCustomObject]@{
            RelativePath = $RelativePath
            SourceFile = $SourceFile
            TargetFile = $TargetFile
            SourceLines = $sourceLines.Count
            TargetLines = $targetLines.Count
            TotalDifferences = $differences.Count
            NamespaceOnlyDiffs = ($differences | Where-Object { $_.IsNamespaceOnly }).Count
            ContentDiffs = ($differences | Where-Object { -not $_.IsNamespaceOnly }).Count
            SourceNamespace = $sourceNamespace
            TargetNamespace = $targetNamespace
            MissingUsingsInTarget = $missingInTarget
            ExtraUsingsInTarget = $extraInTarget
            Differences = $differences | Select-Object -First 20  # Limit to first 20 differences
            IsIdenticalExceptNamespace = (($differences | Where-Object { -not $_.IsNamespaceOnly }).Count -eq 0)
        }
        
    } catch {
        Write-Host "Error analyzing file: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Load comparison results
$comparisonFile = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\CoreModuleComparison.json"
$comparisonData = Get-Content $comparisonFile | ConvertFrom-Json
$bothExistFiles = $comparisonData.Files | Where-Object { $_.Status -eq "Both Exist" }

if ($FileName) {
    # Analyze specific file
    $file = $bothExistFiles | Where-Object { $_.RelativePath -eq $FileName -or $_.FileName -eq $FileName }
    if ($file) {
        $result = Get-FileDiff -SourceFile $file.SourcePath -TargetFile $file.TargetPath -RelativePath $file.RelativePath
        
        if ($result) {
            Write-Host ""
            Write-Host "=== Detailed Analysis for $($result.RelativePath) ===" -ForegroundColor Green
            Write-Host "Source lines: $($result.SourceLines)" -ForegroundColor White
            Write-Host "Target lines: $($result.TargetLines)" -ForegroundColor White
            Write-Host "Total differences: $($result.TotalDifferences)" -ForegroundColor White
            Write-Host "Namespace-only diffs: $($result.NamespaceOnlyDiffs)" -ForegroundColor Yellow
            Write-Host "Content diffs: $($result.ContentDiffs)" -ForegroundColor Red
            Write-Host "Identical except namespace: $($result.IsIdenticalExceptNamespace)" -ForegroundColor Cyan
            
            if ($result.MissingUsingsInTarget.Count -gt 0) {
                Write-Host ""
                Write-Host "Missing usings in target:" -ForegroundColor Red
                $result.MissingUsingsInTarget | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
            }
            
            if ($result.ExtraUsingsInTarget.Count -gt 0) {
                Write-Host ""
                Write-Host "Extra usings in target:" -ForegroundColor Green
                $result.ExtraUsingsInTarget | ForEach-Object { Write-Host "  + $_" -ForegroundColor Green }
            }
            
            if ($result.ContentDiffs -gt 0 -and $result.Differences.Count -gt 0) {
                Write-Host ""
                Write-Host "First few content differences:" -ForegroundColor Yellow
                $result.Differences | Where-Object { -not $_.IsNamespaceOnly } | Select-Object -First 5 | ForEach-Object {
                    Write-Host "  Line $($_.LineNumber):" -ForegroundColor White
                    Write-Host "    Source: $($_.SourceLine)" -ForegroundColor Red
                    Write-Host "    Target: $($_.TargetLine)" -ForegroundColor Green
                }
            }
            
            # Save detailed result
            $outputPath = "e:\workgit\Shop\XiaoMa.MTM\CodeMigrationTodoLists\03_DetailedComparisons\$($FileName -replace '\\', '_' -replace '\.cs$', '')_DetailedDiff.json"
            $result | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputPath -Encoding UTF8
            Write-Host ""
            Write-Host "Detailed analysis saved to: $outputPath" -ForegroundColor Green
        }
    } else {
        Write-Host "File not found: $FileName" -ForegroundColor Red
    }
} else {
    Write-Host "Please specify a filename with -FileName parameter" -ForegroundColor Yellow
    Write-Host "Available files:" -ForegroundColor Cyan
    $bothExistFiles | Select-Object -First 20 | ForEach-Object {
        Write-Host "  $($_.RelativePath)" -ForegroundColor White
    }
}

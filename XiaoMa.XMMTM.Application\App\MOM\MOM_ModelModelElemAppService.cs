/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelModelElemAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2020/8/5/星期三 21:27:29
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.ObjectMapping;
using Abp.Timing;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Threading.Tasks;
using XiaoMa.Extensions;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.App.ODM.Dtos;
using XiaoMa.XMMTM.Domain;
using XiaoMa.XMMTM.Enums;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 版型和款式明细关系
    /// </summary>
    public class MOM_ModelModelElemAppService : XMMTMAppServiceBase, IMOM_ModelModelElemAppService
    {
        private readonly IRepository<ModelModelElem, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<Item, Guid> itemRepository;
        private readonly IRepository<ModelElem, Guid> modelElemRepository;
        private readonly IRepository<ModelElemBase, Guid> modelElemBaseRepository;
        private readonly IRepository<ModelElemList, Guid> modelElemListRepository;
        private readonly IRepository<SorderDetailElem, Guid> sorderDetailElemRepository;
        private readonly IRepository<SorderDetailModel, Guid> sorderDetailModelRepository;
        private readonly IRepository<Group, Guid> groupRepository;
        private readonly IRepository<ModelElemImage, Guid> modelElemImageRepository;
        private readonly IRepository<ModelImage, Guid> modelImageRepository;
        private readonly IRepository<ModelBaseImage, Guid> modelBaseImageRepository;
        private readonly FileServer.FileServer fileServer;
        private readonly IRepository<ModelBase, Guid> modelBaseRepository;
        private readonly IRepository<ModelElemRule, Guid> modelElemRuleRepository;
        private readonly IRepository<ModelElemRuleDetail, Guid> modelElemRuleDetailRepository;
        private readonly IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository;
        private readonly IRepository<Class, Guid> classRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelModelElemAppService(
       IRepository<ModelModelElem, Guid> repository,
       IRepository<Model, Guid> modelRepository,
       IRepository<Item, Guid> itemRepository,
       IRepository<ModelElem, Guid> modelElemRepository,
       IRepository<ModelElemBase, Guid> modelElemBaseRepository,
       IRepository<ModelElemList, Guid> modelElemListRepository,
       IRepository<SorderDetailElem, Guid> sorderDetailElemRepository,
       IRepository<SorderDetailModel, Guid> sorderDetailModelRepository,
       IRepository<Group, Guid> groupRepository,
              IRepository<ModelElemImage, Guid> modelElemImageRepository,
                     IRepository<ModelImage, Guid> modelImageRepository,
                          IRepository<ModelBaseImage, Guid> modelBaseImageRepository,
                            FileServer.FileServer fileServer,
                            IRepository<ModelBase, Guid> modelBaseRepository,
                            IRepository<ModelElemRule, Guid> modelElemRuleRepository,
                                   IRepository<ModelElemRuleDetail, Guid> modelElemRuleDetailRepository,
                                          IRepository<ModelSizeColumn, Guid> modelSizeColumnRepository,
                                                 IRepository<Class, Guid> classRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.itemRepository = itemRepository;
            this.modelElemRepository = modelElemRepository;
            this.modelElemBaseRepository = modelElemBaseRepository;
            this.modelElemListRepository = modelElemListRepository;
            this.sorderDetailElemRepository = sorderDetailElemRepository;
            this.sorderDetailModelRepository = sorderDetailModelRepository;
            this.groupRepository = groupRepository;
            this.modelElemImageRepository = modelElemImageRepository;
            this.modelImageRepository = modelImageRepository;
            this.modelBaseImageRepository = modelBaseImageRepository;
            this.fileServer = fileServer;
            this.modelBaseRepository = modelBaseRepository;
            this.modelElemRuleRepository = modelElemRuleRepository;
            this.modelElemRuleDetailRepository = modelElemRuleDetailRepository;
            this.modelSizeColumnRepository = modelSizeColumnRepository;
            this.classRepository = classRepository;
            this.objectMapper = objectMapper;
        }
        /// <summary>
        /// 根据款式明细获取版型
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelModelElemGetAllOutputDto>> GetModelByModelElem(MOM_ModelModelElemGetAllInputDto input)
        {
            Guid? modelElemGroupID = Guid.Empty;
            // 根据款式明细ID，获取该款式的物料组
            //if (input.Id != Guid.Empty)
            //{
            //    modelElemGroupID = modelElemRepository.GetAll().Where(p => p.Id == input.Id).Include("MOM_ModelElemList").Select(p => p.MOM_ModelElemList.GroupID).FirstOrDefault(); //todo 
            //}
            var query = (from t in modelRepository.GetAll()
                         join t1x in repository.GetAll().Where(a => a.ModelElemID == input.Id) on t.Id equals t1x.ModelID into t1xx
                         from t1 in t1xx.DefaultIfEmpty()
                         join t2x in groupRepository.GetAll() on t.GroupID equals t2x.Id into t2xx
                         from t2 in t2xx.DefaultIfEmpty()
                         join t6x in classRepository.GetAll() on t.ModelGroupID equals t6x.Id into t6xx
                         from t6 in t6xx.DefaultIfEmpty()
                             //where t.IsActive
                         select new MOM_ModelModelElemGetAllOutputDto()
                         {

                             ModelID = t.Id,
                             ModelElemID = input.Id,
                             ModelCode = t.Code,
                             ModelName = t.CodeName,
                             GroupID = t.GroupID,
                             GroupCode = t2.Code,
                             GroupName = t2.CodeName,
                             Selected = t1 == null ? false : true, //虚拟字段， 用于指定是否被选中
                             Id = t1 == null ? Guid.Empty : t1.Id,
                             Default = t1 == null ? false : t1.Default,
                             CreateID = t1 == null ? null : t1.CreateID,
                             CreateBy = t1 == null ? null : t1.CreateBy,
                             CreateOn = t1 == null ? Clock.Now : t1.CreateOn,
                             ModifyID = t1 == null ? null : t1.ModifyID,
                             ModifyBy = t1 == null ? null : t1.ModifyBy,
                             ModifyOn = t1 == null ? null : t1.ModifyOn,
                             GenderID = t.GenderID,
                             BusinessSubTypeText = t.BusinessSubType.GetDescription(),
                             BusinessSubType = t.BusinessSubType,
                             ModelGroupID = t.ModelGroupID,
                             ModelGroupName =t6.Code+":"+ t6.CodeName,
                         })
                         //.WhereIf(modelElemGroupID.HasValue, a => a.GroupID == modelElemGroupID.Value)
                         .WhereIf(input.GroupID.HasValue, a => a.GroupID == input.GroupID.Value)
                         .WhereIf(input.ModelGroupID.HasValue, a => a.ModelGroupID == input.ModelGroupID.Value)
                         .WhereIf(input.BusinessSubType.HasValue, a => a.BusinessSubType == input.BusinessSubType.Value)
                         .WhereIf(input.Selected.HasValue, a => a.Selected == input.Selected.Value)
            .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelCode.Contains(input.Text) || a.ModelName.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderByDescending(a => a.Selected).ThenBy(a=>a.ModelGroupID).ThenBy(a=>a.BusinessSubType).ThenBy(a => a.ModelCode).PageBy(input).ToListAsync();
            //var list = objectMapper.Map<List<MOM_ModelModelElemGetAllOutputDto>>(result);
            return new PagedResultDto<MOM_ModelModelElemGetAllOutputDto>(count, result);
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Adds(List<MOM_ModelModelElemDto> input)
        {
            foreach (var entity in input)
            {
                if (repository.GetAll().Any(a => a.ModelElemID == entity.ModelElemID && a.ModelID == entity.ModelID))
                {
                    throw new UserFriendlyException("已经由重复项存在");

                }
                await ExistCodeAsync(entity);
                var oldentity = ObjectMapper.Map<ModelModelElem>(entity);
                await repository.InsertAsync(oldentity);
            }
        }
        /// <summary>
        /// 根据款式明细保存版型
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task UpdatesByModel(List<MOM_ModelModelElemDto> input)
        {
            var modelElemIDs = input.Select(a => a.ModelElemID).ToList();
            var modelIDs = input.Select(a => a.ModelID).Distinct().ToList();
            var modelElemLists = await (from t in modelElemRepository.GetAll()
                                        where modelElemIDs.Contains(t.Id)
                                        select new { ModelElemID = t.Id, t.ModelElemListID }).ToListAsync();
            var modelElemListIds = modelElemLists.Select(a => a.ModelElemListID).Distinct().ToList();
            var modelModelElemDefault = await (from t in repository.GetAll()
                                               join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                                               from t1 in t1xx.DefaultIfEmpty()
                                               join t2x in modelRepository.GetAll() on t.ModelID equals t2x.Id into t2xx
                                               from t2 in t2xx.DefaultIfEmpty()
                                               where modelElemListIds.Contains(t1.ModelElemListID)
                                               where t.Default
                                               where modelIDs.Contains(t.ModelID)
                                               select new
                                               {
                                                   ModelElemID = t.ModelElemID,
                                                   t.ModelID,
                                                   t.Default,
                                                   t1.ModelElemListID,
                                                   ModelElemcode = t1.Code,
                                                   ModelElemName = t1.CodeName,
                                                   ModelCode = t2.Code,
                                                   ModelName = t2.CodeName,
                                               }).ToListAsync();
            var adds = input.Where(a => a.Id == Guid.Empty && a.Selected).ToList();
            var edits = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && a.Selected).ToList();
            var dels = input.Where(a => a.Id.HasValue && a.Id != Guid.Empty && !a.Selected).ToList();
            foreach (var item in adds)
            {
                var modelElemListId = modelElemLists.FirstOrDefault(a => a.ModelElemID == item.ModelElemID)?.ModelElemListID;
                if (item.Default && modelElemListId.HasValue)
                {
                    var detilmodelElems = modelModelElemDefault.Where(a => a.ModelID == item.ModelID).Where(a => a.ModelElemListID == modelElemListId.Value).Where(a => a.Default).ToList();
                    var detilmodelElem = detilmodelElems.Where(a => a.ModelElemID != item.ModelElemID).FirstOrDefault();
                    if (detilmodelElem != null)
                    {
                        throw new UserFriendlyException($"版型{detilmodelElem.ModelName}已有绑定默认款式明显:{detilmodelElem.ModelElemcode}:{detilmodelElem.ModelElemName},无法再次默认,请修改");
                    }

                }
                var oldentity = ObjectMapper.Map<ModelModelElem>(item);
                await repository.InsertAsync(oldentity);
            }
            foreach (var entity in edits)
            {
                var modelElemListId = modelElemLists.FirstOrDefault(a => a.ModelElemID == entity.ModelElemID)?.ModelElemListID;
                if (entity.Default && modelElemListId.HasValue)
                {
                    var detilmodelElems = modelModelElemDefault.Where(a => a.ModelID == entity.ModelID).Where(a => a.ModelElemListID == modelElemListId.Value).Where(a => a.Default).ToList();
                    var detilmodelElem = detilmodelElems.Where(a => a.ModelElemID != entity.ModelElemID).FirstOrDefault();
                    if (detilmodelElem != null)
                    {
                        throw new UserFriendlyException($"版型{detilmodelElem.ModelName}已有绑定默认款式明显:{detilmodelElem.ModelElemcode}:{detilmodelElem.ModelElemName},无法再次默认,请修改");
                    }

                }
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
            foreach (var item in dels)
            {
                await repository.DeleteAsync(item.Id.Value);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Deletes(List<MOM_ModelModelElemDto> input)
        {
            foreach (var entity in input)
            {
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.DeleteAsync(oldentity);
            }
        }
        [HttpPost]
        [AbpAuthorize]
        public async Task<PagedResultDto<MOM_ModelModelElemGetAllOutputDto1>> GetByModelID(MOM_ModelModelElemGetAllInputDto1 input)
        {
            var query = (from t in repository.GetAll()
                         join t1 in modelElemRepository.GetAll() on t.ModelElemID equals t1.Id
                         join t2 in modelElemListRepository.GetAll() on t1.ModelElemListID equals t2.Id
                         join t3x in itemRepository.GetAll() on t1.ItemID equals t3x.Id into t3xx
                         from t3 in t3xx.DefaultIfEmpty()
                         join t4x in itemRepository.GetAll() on t.ItemID equals t4x.Id into t4xx
                         from t4 in t4xx.DefaultIfEmpty()
                         where input.Id == t.ModelID
                         select new MOM_ModelModelElemGetAllOutputDto1()
                         {
                             Id = t.Id,
                             ModelID = t.ModelID,
                             ModelElemID = t.ModelElemID,
                             Default = t.Default,
                             CreateID = t.CreateID,
                             CreateBy = t.CreateBy,
                             CreateOn = t.CreateOn,
                             ModifyID = t.ModifyID,
                             ModifyBy = t.ModifyBy,
                             ModifyOn = t.ModifyOn,
                             ModelElemText = t1.Code + ": " + t1.CodeName,
                             ModelElemListText = t2.Code + ": " + t2.CodeName,
                             ModelElemListID = t2.Id,
                             ModelElemTypeText = t2.ModelElemTypeID.GetDescription(),
                             ModelElemTypeID = (int)t2.ModelElemTypeID,
                             ModelElemListCode = t2.Code,
                             ModelElemCode = t1.Code,
                             ModelElemListCadSeq = t2.CadSeq,
                             ItemCode = t3.Code,
                             ItemName = t3.CodeName,
                             OriginalItemNo = t3.OriginalItemNo,
                             ItemID = t.ItemID,
                             Input = t.Input,
                             ItemWidth = t.ItemWidth,
                             Qty = t.Qty,
                             Selected = t.Selected,
                             NItemCode = t4.Code,
                             NItemName = t4.CodeName,
                             NOriginalItemNo = t4.OriginalItemNo,
                             ModelElemName = t1.CodeName,
                         })
                        .WhereIf(input.ModelElemTypeID.HasValue, a => a.ModelElemTypeID == input.ModelElemTypeID.Value)
                        .WhereIf(input.ModelElemListID.HasValue, a => a.ModelElemListID == input.ModelElemListID.Value)
                        .WhereIf(!string.IsNullOrEmpty(input.Text), a => a.ModelElemCode.Contains(input.Text) || a.ModelElemText.Contains(input.Text));
            var count = await query.CountAsync();
            var result = await query.OrderBy(a => a.ModelElemTypeID).ThenBy(a => a.ModelElemListCode).ThenBy(a => a.ModelElemCode).PageBy(input).ToListAsync();
            return new PagedResultDto<MOM_ModelModelElemGetAllOutputDto1>(count, result);
        }
        private async Task ExistCodeAsync(MOM_ModelModelElemDto input)
        {
            var modelelemlist = await modelElemRepository.GetAll().FirstOrDefaultAsync(a => a.Id == input.ModelElemID);
            if (modelelemlist == null)
            {
                throw new UserFriendlyException("款式不存在");
            }
            var any = await (from t in repository.GetAll()
                             join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                             from t1 in t1xx.DefaultIfEmpty()
                             where t.ModelID == input.ModelID && t1.ModelElemListID == modelelemlist.ModelElemListID
                             select new
                             {
                                 t.ModelID,
                                 t.ModelElemID,
                                 t.Id,
                                 t.Default,
                             }).WhereIf(input.Id.HasValue && Guid.Empty != input.Id.Value, a => a.Id != input.Id.Value).AnyAsync(a => a.Default);
            if (input.Default && any)
            {
                throw new UserFriendlyException("同一款式下禁止设置多个默认项");
            }



        }
        [HttpPost]
        [AbpAuthorize]
        public async Task Updates(List<MOM_ModelModelElemDto> input)
        {
            foreach (var entity in input)
            {
                if (repository.GetAll().Any(a => a.ModelElemID == entity.ModelElemID && a.ModelID == entity.ModelID && entity.Id != a.Id))
                {
                    throw new UserFriendlyException("已经由重复项存在");
                }
                await ExistCodeAsync(entity);
                var oldentity = repository.Get(entity.Id.Value);
                ObjectMapper.Map(entity, oldentity);
                await repository.UpdateAsync(oldentity);
            }
        }
        #region 固化版

        public async Task ModifyDesignModelElem(List<ModifyDesignModelElemInputDto> input)
        {
            var modelID = input.FirstOrDefault().ModelID;
            if (modelID == Guid.Empty)
            {
                return;
            }
            var modelModelModelElem = await repository.GetAll().Where(a => a.ModelID == modelID).ToListAsync();
            foreach (var item in modelModelModelElem)
            {
                var dto = input.FirstOrDefault(a => a.ModelElemID == item.ModelElemID);
                if (dto != null)
                {
                    item.ItemID = dto.ItemID;
                    item.Input = dto.Input;
                    item.Default = true;
                    item.ItemWidth = dto.ItemWidth;
                    item.Qty = dto.Qty;
                }
                else
                {
                    item.ItemID = null;
                    item.Input = null;
                    item.Default = false;
                    item.ItemWidth = null;
                    item.Qty = null;
                }
                await repository.UpdateAsync(item);
            }
        }

        [HttpPost]
        [AbpAuthorize]
        public async Task<List<PlusModelElemListOutputDto>> GetPlusModelElem(PlushModelElemGetInputDto input)
        {
            var modelDto = await modelRepository.FirstOrDefaultAsync(input.ModelID);
            if (modelDto == null || modelDto.BusinessSubType == ModelBusinessSubType.CommonModel || modelDto.OriginalModelID == null || modelDto.SoderDetailModelSource == null || modelDto.ItemID == null)
            {
                throw new UserFriendlyException("版型数据异常");
            }
            var sorderdetailmodel = await sorderDetailModelRepository.FirstOrDefaultAsync(modelDto.SoderDetailModelSource.Value);
            if (sorderdetailmodel == null)
            {
                throw new UserFriendlyException("订单样板不存在");
            }
            if (sorderdetailmodel.ModelID != modelDto.OriginalModelID)
            {
                throw new UserFriendlyException("订单样板班号与现有绑定的样版号不匹配");
            }
            var sorderDetailElems = await (from t in sorderDetailElemRepository.GetAll()
                                           join t1x in modelElemRepository.GetAll() on t.ModelElemID equals t1x.Id into t1xx
                                           from t1 in t1xx.DefaultIfEmpty()
                                           join t2x in modelElemListRepository.GetAll() on t1.ModelElemListID equals t2x.Id into t2xx
                                           from t2 in t2xx.DefaultIfEmpty()
                                           join t3x in modelElemBaseRepository.GetAll() on t2.ModelElemBaseID equals t3x.Id into t3xx
                                           from t3 in t3xx.DefaultIfEmpty()
                                           join t4x in itemRepository.GetAll() on t.ItemID equals t4x.Id into t4xx
                                           from t4 in t4xx.DefaultIfEmpty()
                                           where t.SorderDetailModelID == modelDto.SoderDetailModelSource.Value
                                           select new PlusModelElemListOutputDto
                                           {
                                               ModelElemID = t.ModelElemID,
                                               ItemID = t.ItemID,
                                               ItemCode = t4.Code,
                                               ItemName = t4.CodeName,
                                               ItemOriginalItemNo = t4.OriginalItemNo,
                                               ItemWidth = t.ItemWidth,
                                               Qty = t.Qty,
                                               Input = t.Input,
                                               ModelElemCode = t1.Code,
                                               ModelElemName = t1.CodeName,
                                               ModelElemListID = t1.ModelElemListID,
                                               ModelElemListCode = t2.Code,
                                               ModelElemListName = t2.CodeName,
                                               ModelElemTypeText = t2.ModelElemTypeID.GetDescription(),
                                               ModelElemBaseID = t2.ModelElemBaseID,
                                               ModelElemBaseCode = t3.Code,
                                               ModelElemBaseName = t3.CodeName,
                                               ModelElemBaseSequence = t3.Sequence,
                                               ModelElemListSequence = t2.ElemSeq,
                                               ModelElemSequence = t1.Sort,
                                           }).OrderBy(a => a.ModelElemBaseSequence).ThenBy(a => a.ModelElemListSequence).ThenBy(a => a.ModelElemSequence).ToListAsync();
            return sorderDetailElems;
        }

        /// <summary>
        /// 获取固化版的款式明细以及货号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task<List<SorderDetailElemOutputDto>> GetDesignModelElem(DesignModelElemInputDto input)
        {
            #region 获取款式明细

            var modelmodelelems = await (from t in repository.GetAll().AsNoTracking()
                                         join t1 in modelElemRepository.GetAll().AsNoTracking() on t.ModelElemID equals t1.Id
                                         join t2x in itemRepository.GetAll().AsNoTracking() on t.ItemID equals t2x.Id into t2xx
                                         from t2 in t2xx.DefaultIfEmpty()
                                         where t.ModelID == input.ModelID
                                         orderby t1.ModifyOn descending
                                         //select new ModelModelElemDto
                                         select new
                                         {
                                             ModelElemID = t.ModelElemID,
                                             Default = t.Default,
                                             ModelElemListID = t1.ModelElemListID,
                                             ModelID = t.ModelID,
                                             ModifyOn = t1.ModifyOn,
                                             Sort = t1.Sort,
                                             ItemID = t.ItemID,
                                             Input = t.Input,
                                             Qty = t.Qty,
                                             ModelElemCode = t1.Code,
                                             ItemCode = t2.Code,
                                             ItemName = t2.CodeName,
                                             ItemWidth = t2.Width,
                                             t1.IsInputItem,
                                             t1.IsInput,
                                             t1.IsItemAdd,
                                             t1.IsItemImageAdd,
                                         }).ToListAsync();

            var modelelemids = modelmodelelems.Select(a => a.ModelElemID).ToList();

            var modelelems = await (from t in modelElemRepository.GetAll().AsNoTracking()
                                    where modelelemids.Contains(t.Id)
                                    select t).ToListAsync();
            var itemids = modelelems.Where(a => a.ItemID.HasValue).Select(a => a.ItemID.Value).ToList();
            var items = await (from t in itemRepository.GetAll().AsNoTracking()
                               where itemids.Contains(t.Id)
                               select t).ToListAsync();

            var modelelemlistids = modelelems.Select(a => a.ModelElemListID).ToList();
            var modelelemlists = await (from t in modelElemListRepository.GetAll().AsNoTracking()
                                        where modelelemlistids.Contains(t.Id)
                                        select t).ToListAsync();
            var modelelembaseids = modelelemlists.Select(a => a.ModelElemBaseID).ToList();
            var modelelembases = await (from t in modelElemBaseRepository.GetAll().AsNoTracking()
                                        where modelelembaseids.Contains(t.Id)
                                        select t).ToListAsync();
            #region 版型相关的款式明细

            var modelelem1 = (from t in modelmodelelems
                              join t1 in modelelems on t.ModelElemID equals t1.Id
                              join gx in items on t1.ItemID equals gx.Id into gg
                              from g in gg.DefaultIfEmpty()
                              join t2 in modelelemlists on t1.ModelElemListID equals t2.Id
                              join t3 in modelelembases on t2.ModelElemBaseID equals t3.Id

                              orderby t2.ModelElemTypeID
                              select new
                              {
                                  t = t,
                                  //t1,
                                  t1 = new
                                  {
                                      t1.Id,
                                      t1.IsInput,
                                      t1.IsInputItem,
                                      ItemID = t.ItemID.HasValue ? t.ItemID.Value : t1.ItemID,
                                      ModelElemCode = t1.Code,
                                      ModelElemName = t1.CodeName,
                                      t1.ModelElemListID,
                                      t1.Price,
                                      t1.Qty,
                                      t1.Remark,
                                      t.Default,
                                      //ItemCode ="",
                                      //ItemName = "",
                                      //ItemWidth =0,
                                      ItemCode = g == null ? "" : g.Code,
                                      ItemName = g == null ? "" : g.CodeName,
                                      ItemWidth = g != null ? g.Width : 0,
                                      t.ModifyOn,
                                      t1.IsItemImageAdd,
                                      t1.IsItemAdd,
                                      t1.Sort,

                                  },
                                  t2 = new
                                  {
                                      t2.Id,
                                      t2.ModelElemTypeID,
                                      ModelElemTypeName = t2.ModelElemTypeID.ToString(),
                                      ModelElemTypeCode = t2.ModelElemTypeID.GetDescription(),
                                      ModelElemListCode = t2.Code,
                                      ModelElemListName = t2.CodeName,
                                      t2.ModelElemBaseID,
                                      t2.ReportShow,
                                      t2.IsPlanShow,
                                      t2.IsPlanEdit,
                                      t2.IsPlus,
                                      t2.IscustomerEdit,
                                      t2.IsCustomerShow,
                                      t2.IsItem,
                                      t2.IsInput,
                                      t2.IsClientShow,
                                      t2.ElemSeq,

                                  },
                                  t3 = new
                                  {
                                      ModelElemBaseCode = t3.Code,
                                      ModelElemBaseName = t3.CodeName,
                                      t3.Sequence,
                                      t3.Id,
                                      t.ModelID,
                                  },
                              }).ToList();
            #endregion
            var modelelem2 = (from t in modelelem1
                              group t by new
                              {
                                  t.t2,
                                  t.t3,
                              } into g1
                              select new
                              {
                                  g1.Key.t2,
                                  g1.Key.t3,
                                  defaultElem = g1.Select(item => item.t1).FirstOrDefault(p => p.Default == true),
                                  ModelElem = g1.Select(item => item.t1).OrderByDescending(a => a.Sort).ThenByDescending(a => a.ModifyOn)
                              }).ToList();


            #region 获取图片位置信息
            var positions = new List<SYS_Position>() {
                SYS_Position.Position1,
                SYS_Position.Position2,
                SYS_Position.Position3,
                SYS_Position.Position4,
                SYS_Position.Position5,
                SYS_Position.Position6,
                SYS_Position.Position7,
                SYS_Position.Position8,
                SYS_Position.Position9,
                SYS_Position.Position10,
                SYS_Position.Position11,
            };
            // 获取特定位置的款式信息
            var modelElemImage1 = from t in modelElemImageRepository.GetAll()
                                  join ge in modelElemRepository.GetAll() on t.ModelElemID equals ge.Id into gee
                                  from g1 in gee.DefaultIfEmpty()
                                  join gel in modelElemListRepository.GetAll() on g1.ModelElemListID equals gel.Id into gell
                                  from g2 in gell.DefaultIfEmpty()
                                  join t2 in modelImageRepository.GetAll() on t.ModelImageID equals t2.Id
                                  join t3 in repository.GetAll() on t.ModelElemID equals t3.ModelElemID
                                  where t3.ModelID == input.ModelID
                                  select new
                                  {
                                      PositionID = t.PositionID.Value,
                                      ModelElemBaseID = g2.ModelElemBaseID,
                                      ImagePath = fileServer.GetImageUrl(t2.ImagePath),
                                      g1.ModelElemListID,
                                      t.ModelElemID,
                                      t.ModelElemID1,
                                  };
            //ModelElemID1 
            var modelElemImage2 = from t in modelElemImageRepository.GetAll()
                                  join ge in modelElemRepository.GetAll() on t.ModelElemID1 equals ge.Id into gee
                                  from g1 in gee.DefaultIfEmpty()
                                  join gel in modelElemListRepository.GetAll() on g1.ModelElemListID equals gel.Id into gell
                                  from g2 in gell.DefaultIfEmpty()
                                  join t2 in modelImageRepository.GetAll() on t.ModelImageID equals t2.Id
                                  join t4x in repository.GetAll() on t.ModelElemID1 equals t4x.ModelElemID into t4xx
                                  from t4 in t4xx.DefaultIfEmpty()
                                  where t4 != null && t4.ModelID == input.ModelID
                                  select new
                                  {
                                      PositionID = t2.PositionID,
                                      ModelElemBaseID = (g2 == null ? Guid.Empty : g2.ModelElemBaseID),
                                      g1.ModelElemListID,
                                  };
            //ModelElemID2 
            var modelElemImage2_2 = from t in modelElemImageRepository.GetAll()
                                    join ge in modelElemRepository.GetAll() on t.ModelElemID2 equals ge.Id into gee
                                    from g1 in gee.DefaultIfEmpty()
                                    join gel in modelElemListRepository.GetAll() on g1.ModelElemListID equals gel.Id into gell
                                    from g2 in gell.DefaultIfEmpty()
                                    join t2 in modelImageRepository.GetAll() on t.ModelImageID equals t2.Id
                                    join t4x in repository.GetAll() on t.ModelElemID2 equals t4x.ModelElemID into t4xx
                                    from t4 in t4xx.DefaultIfEmpty()
                                    where t4 != null && t4.ModelID == input.ModelID
                                    select new
                                    {
                                        PositionID = t2.PositionID,
                                        ModelElemBaseID = (g2 == null ? Guid.Empty : g2.ModelElemBaseID),
                                        g1.ModelElemListID,
                                    };
            var modelElemBaseImage = from t in modelRepository.GetAll()
                                     join t1 in modelBaseRepository.GetAll() on t.ModelBaseID equals t1.Id
                                     join t2 in modelBaseImageRepository.GetAll() on t1.Id equals t2.ModelBaseID
                                     join gt in modelElemRepository.GetAll() on t2.ModelElemID1 equals gt.Id into gg
                                     from g in gg.DefaultIfEmpty()
                                     join t3 in modelImageRepository.GetAll() on t2.ModelImageID equals t3.Id
                                     where t.Id == input.ModelID
                                     select new
                                     {
                                         t3.PositionID,
                                         ModelElemBaseID = (g == null ? Guid.Empty : g.MOM_ModelElemList.ModelElemBaseID),
                                         g.ModelElemListID,

                                     };
            var modelelms = await modelElemImage1.ToListAsync();
            var modelElemBaseList1 = modelelms.Where(a => positions.Contains(a.PositionID)).Select(a => new { a.PositionID, a.ModelElemBaseID, a.ModelElemListID }).ToList();
            var modelElemBaseList2 = await modelElemImage2.Where(p => p.ModelElemBaseID != Guid.Empty).Where(a => positions.Contains(a.PositionID)).ToListAsync();
            var modelElemBaseList2_2 = await modelElemImage2_2.Where(p => p.ModelElemBaseID != Guid.Empty).Where(a => positions.Contains(a.PositionID)).ToListAsync();
            var modelElemBaseList3 = await modelElemBaseImage.Where(p => p.ModelElemBaseID != Guid.Empty).Where(a => positions.Contains(a.PositionID)).ToListAsync();

            var modelElemBaseList = modelElemBaseList1.Union(modelElemBaseList2).Union(modelElemBaseList3).Union(modelElemBaseList2_2).Distinct().ToList();

            #endregion

            #region 款式明细


            var modelelem = (from bb in modelelem2
                             orderby bb.t3.Sequence, bb.t2.ModelElemTypeID, bb.t2.ElemSeq
                             select new SorderDetailElemOutputDto
                             {
                                 ModelID = bb.t3.ModelID,
                                 ModelElemBaseID = bb.t3.Id,
                                 ModelElemBaseCode = bb.t3.ModelElemBaseCode,
                                 ModelElemBaseName = bb.t3.ModelElemBaseName,
                                 ModelElemTypeID = (int)bb.t2.ModelElemTypeID,
                                 ModelElemTypeName = bb.t2.ModelElemTypeName,
                                 IsPlanShow = bb.t2.IsPlanShow,
                                 IsPlanEdit = bb.t2.IsPlanEdit,
                                 IsPlus = bb.t2.IsPlus,
                                 IsCustomerShow = bb.t2.IsCustomerShow,
                                 IscustomerEdit = bb.t2.IscustomerEdit,
                                 IsClientShow = bb.t2.IsClientShow,
                                 ModelElemListID = bb.t2.Id,
                                 ModelElemListCode = bb.t2.ModelElemListCode,
                                 ModelElemListName = bb.t2.ModelElemListName,
                                 ModelElemID = bb.defaultElem?.Id,
                                 ModelElemCode = bb.defaultElem == null ? null : bb.defaultElem.ModelElemCode,
                                 ItemID = bb.defaultElem?.ItemID,
                                 ItemText = bb.defaultElem != null && bb.defaultElem.ItemID != null ? (bb.defaultElem.ItemCode + ":" + bb.defaultElem.ItemName) : null,
                                 ItemWidth = bb.defaultElem == null ? 0 : bb.defaultElem.ItemWidth,
                                 Input = "",
                                 Qty = bb.defaultElem == null ? "" : bb.defaultElem.Qty + "",
                                 Unit = "",
                                 IsInputItem = bb.defaultElem == null ? null : (bool?)bb.defaultElem.IsInputItem,
                                 IsInput = bb.defaultElem == null ? null : (bool?)bb.defaultElem.IsInput,
                                 IsItemAdd = bb.defaultElem == null ? false : bb.defaultElem.IsItemAdd,
                                 IsItemImageAdd = bb.defaultElem == null ? false : bb.defaultElem.IsItemImageAdd,
                                 //ItemImageUrl = bb.defaultElem?.ImageUrl,
                                 ModelElem = bb.ModelElem.OrderByDescending(b => b.Sort).ThenByDescending(a => a.ModifyOn).Select(b => new XiaoMa.XMMTM.App.ODM.Dtos.ModelElemDto()
                                 {
                                     Default = b.Default,
                                     ModelElemID = b.Id,
                                     IsInput = b.IsInput,
                                     IsInputItem = b.IsInputItem,
                                     ItemID = b.ItemID,
                                     ItemText = b.ItemID.HasValue ? b.ItemCode + ":" + b.ItemName : null,
                                     ModelElemCode = b.ModelElemCode,
                                     ModelElemName = b.ModelElemName + (b.Default ? "(默认)" : ""),
                                     Price = b.Price,
                                     Qty = b.Qty,
                                     ItemWidth = b.ItemWidth,
                                     IsItemImageAdd = b.IsItemImageAdd,
                                     IsItemAdd = b.IsItemAdd,
                                     ImagePath = modelelms.FirstOrDefault(a => a.ModelElemID == b.Id && !a.ModelElemID1.HasValue && a.PositionID == SYS_Position.Position16)?.ImagePath
                                 }).ToList(),
                                 PositionIDs = (from t in modelElemBaseList
                                                where t.ModelElemBaseID == bb.t3.Id
                                                select (int)t.PositionID
                                                ).ToList()
                             }).ToList();
            #endregion



            #endregion



            ////配色面料/辅料方案
            //modelelem = await ItemElemItem(modelelem, input);

            ////第一次计算
            //modelelem = await FirstElemRule(modelelem, input);
            //跟所有款式明细中带有货号的追加图片
            //modelelem = await itemImages(modelelem);
            //插入公式
            modelelem = await insertElemRules(modelelem, input.ModelID);
            return modelelem;

        }
        /// <summary>
        /// 向款式明细添加公式
        /// </summary>
        /// <param name="list"></param>
        /// <param name="ModelID"></param>
        /// <returns></returns>
        private async Task<List<SorderDetailElemOutputDto>> insertElemRules(List<SorderDetailElemOutputDto> list, Guid ModelID)
        {
            var _modelElemListIds = list.Select(a => a.ModelElemListID).ToList();

            var _modelElemIds = list.Where(a => a.ModelElemID.HasValue).Select(a => a.ModelElemID.Value).ToList();

            var ruledetails = await (from t in modelElemRuleRepository.GetAll()
                                     join t1 in modelElemRuleDetailRepository.GetAll() on t.Id equals t1.ModelElemRuleID
                                     where _modelElemListIds.Contains(t.ModelElemListID)
                                     && t.IsActive && t1.IsActive
                                     && t.ModelRuleType != ModelRuleTypeEnums.SorderProduct//不是大货的
                                     select new
                                     {
                                         ModelElemRuleID = t.Id,
                                         BomRuleType = t.BomRuleType,
                                         ModelElemListID = t.ModelElemListID,
                                         ModelElemListID1 = t1.ModelElemListID,
                                         ModelElemID = t1.ModelElemID,
                                         SizeColumnID = t1.SizeColumnID,
                                         ItemID = t1.ItemID
                                     }).ToListAsync();

            var _ruledetail_elems = (from t in ruledetails
                                     where t.ModelElemListID1.HasValue && _modelElemListIds.Contains(t.ModelElemListID1.Value)
                                     select t).ToList();

            //var _modelelemlistid = ruledetails.Where(a => a.ModelElemListID1.HasValue).Select(a => a.ModelElemListID1.Value).Distinct().ToList();
            //var _expElemListIds = _modelelemlistid.Except(_modelElemListIds).ToList();
            //var _v1 = ruledetails.Where(a => a.ModelElemListID1.HasValue && !_expElemListIds.Contains(a.ModelElemListID1.Value)).ToList();
            //foreach (var item in _v1)
            //{
            //    ruledetails.Remove(item);
            //}
            var _modelSizeColumnIds = modelSizeColumnRepository.GetAll().Where(a => a.IsActive && a.ModelID == ModelID).Select(a => a.SizeColumnID).ToList();
            var _ruledetail_sizes = (from t in ruledetails
                                     where t.SizeColumnID.HasValue && _modelSizeColumnIds.Contains(t.SizeColumnID.Value)
                                     select t).ToList();

            //var _ruleSizeIds = ruledetails.Where(a => a.SizeColumnID.HasValue).Select(a => a.SizeColumnID.Value).ToList();

            //var expSizeIds = _ruleSizeIds.Except(_modelSizeColumnIds).ToList();
            ////排除掉不包含的规格
            //var _v2 = ruledetails.Where(a => a.SizeColumnID.HasValue && expSizeIds.Contains(a.SizeColumnID.Value)).ToList();
            //foreach (var item in _v2)
            //{
            //    ruledetails.Remove(item);
            //}
            var rules = _ruledetail_elems.Union(_ruledetail_sizes);
            foreach (var item in list)
            {
                var _ruleids = rules.Where(a => a.ModelElemListID1.HasValue && a.ModelElemListID1 == item.ModelElemListID).Select(a => a.ModelElemRuleID).ToList();
                if (_ruleids.Any())
                {
                    var _elemlist = rules.Where(a => _ruleids.Contains(a.ModelElemRuleID)).GroupBy(a => a.ModelElemRuleID).Select(a => new ModelElemRuleDto()
                    {
                        ModelElemRuleID = a.Key,
                        BomRuleType = a.FirstOrDefault().BomRuleType,
                        ModelElemListID = a.FirstOrDefault().ModelElemListID,
                        ModelElemListIDs = a.Where(a => a.ModelElemListID1.HasValue).Select(a => a.ModelElemListID1.Value).Distinct().ToList(),
                        SizeElemListIDs = a.Where(a => a.SizeColumnID.HasValue).Select(a => a.SizeColumnID.Value).Distinct().ToList()
                    }).ToList();
                    if (_elemlist.Any())
                    {
                        item.ModelElemRules.AddRange(_elemlist);
                    }
                }


            }
            return list;


        }
        #endregion
    }
}

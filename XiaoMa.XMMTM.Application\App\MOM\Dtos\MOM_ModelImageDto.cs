﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelImageDto
// 功能描述：    实体Dto
// 作者    zhangby
// 时间    2020/8/8/星期六 10:18:17
-----------------------------------------------*/

using Abp.Application.Services.Dto;
using Microsoft.AspNetCore.Http;
using System;
using System.IO;
using XiaoMa.XMMTM.SystemConfig;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_ModelImageDto新增/修改数据对象Dto
    /// </summary>
    public class MOM_ModelImageDto : EntityDto<Guid?>
    {
        public virtual bool IsActive { set; get; }
        /// <summary>
        ///
        /// </summary>

        public string ImagePath { get; set; }

        /// <summary>
        ///
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        ///
        /// </summary>

        public SYS_ModelImageType ModelImageTypeID { get; set; }

        /// <summary>
        ///
        /// </summary>

        public SYS_Position PositionID { get; set; }

        public Guid? ModelID { set; get; }

        public string Code { set; get; }
        public string CodeName { set; get; }
        //public bool isActive { set; get; }

    }
}

/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IICAD_ETCADAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/12/9/星期三 8:37:24
-----------------------------------------------*/
using Abp.Application.Services;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.CAD.Dtos;
using XiaoMa.XMMTM.CAD.Dtos;

namespace XiaoMa.XMMTM.App.CAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface ICAD_ETCADAppService : IApplicationService
    {
        Task<ETCADDto> GetGD(CAD_ETCADGetAllOutputDto input);
        Task<GetETCADOutputDto> Get(GetETCADInputDto input);
        Task UpdateState(CAD_ETUpdateStateInputDto input);
        Task Update(CAD_ETUpdateInputDto input);
        Task UpdateNew(UpdateETCADInput input);

        //string GetMD5HashWithKey(string input, string key);

        Task SaveSvg(UpdateETCADSvgInput input);
    }
}

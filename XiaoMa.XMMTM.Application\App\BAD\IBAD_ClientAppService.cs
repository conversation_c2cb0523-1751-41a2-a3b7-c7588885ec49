﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    ISYM_ClientAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/7/10/星期五 21:51:58
-----------------------------------------------*/

using Abp.Application.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.BAD.Dtos;
using XiaoMa.XMMTM.Domain.BAD;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ClientAppService : IApplicationService
    {
        Task<List<Guid>> GetChildClient(List<Guid> clientIds, List<Guid> list, bool FilterInheritedParentBill = false, bool FilterInheritedParentModel = false);

        Task<Client> GetParentClient(ParentClientInputDto input);
    }
}

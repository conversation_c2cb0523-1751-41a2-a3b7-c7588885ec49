﻿{
    "Summary":  {
                    "GeneratedAt":  "2025-09-16 15:41:47",
                    "TargetOnly":  154,
                    "SourceOnly":  62,
                    "BothExist":  365,
                    "TotalFiles":  581
                },
    "Files":  [
                  {
                      "FileName":  "AppVersionHelper.cs",
                      "RelativePath":  "AppVersionHelper.cs",
                      "Directory":  "",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\AppVersionHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\AppVersionHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RateLimitingActionFilter.cs",
                      "RelativePath":  "RateLimitingActionFilter.cs",
                      "Directory":  "",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\RateLimitingActionFilter.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\RateLimitingActionFilter.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "XMMTMConsts.cs",
                      "RelativePath":  "XMMTMConsts.cs",
                      "Directory":  "",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\XMMTMConsts.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\XMMTMConsts.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "XMMTMCoreModule.cs",
                      "RelativePath":  "XMMTMCoreModule.cs",
                      "Directory":  "",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\XMMTMCoreModule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\XMMTMCoreModule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PermissionNames.cs",
                      "RelativePath":  "Authorization\\PermissionNames.cs",
                      "Directory":  "Authorization",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\PermissionNames.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\PermissionNames.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "XMMTMAuthorizationProvider.cs",
                      "RelativePath":  "Authorization\\XMMTMAuthorizationProvider.cs",
                      "Directory":  "Authorization",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\XMMTMAuthorizationProvider.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\XMMTMAuthorizationProvider.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SSOUserPermissionCacheItem.cs",
                      "RelativePath":  "Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "Directory":  "Authorization\\Users",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Authorization\\Users\\SSOUserPermissionCacheItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CadKeyManager.cs",
                      "RelativePath":  "CAD\\CadKeyManager.cs",
                      "Directory":  "CAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\CadKeyManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\CadKeyManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ETCadManger.cs",
                      "RelativePath":  "CAD\\ETCadManger.cs",
                      "Directory":  "CAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\ETCadManger.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\ETCadManger.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ICadKeyManager.cs",
                      "RelativePath":  "CAD\\ICadKeyManager.cs",
                      "Directory":  "CAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\ICadKeyManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\ICadKeyManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IETCadManager.cs",
                      "RelativePath":  "CAD\\IETCadManager.cs",
                      "Directory":  "CAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\IETCadManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\IETCadManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BlitCadManager.cs",
                      "RelativePath":  "CAD\\Blit\\BlitCadManager.cs",
                      "Directory":  "CAD\\Blit",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Blit\\BlitCadManager.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IBlitCadManager.cs",
                      "RelativePath":  "CAD\\Blit\\IBlitCadManager.cs",
                      "Directory":  "CAD\\Blit",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Blit\\IBlitCadManager.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "CadLayoutInputDto.cs",
                      "RelativePath":  "CAD\\Dtos\\CadLayoutInputDto.cs",
                      "Directory":  "CAD\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\CadLayoutInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Dtos\\CadLayoutInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ETCADOutputDto.cs",
                      "RelativePath":  "CAD\\Dtos\\ETCADOutputDto.cs",
                      "Directory":  "CAD\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\ETCADOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Dtos\\ETCADOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetDetailModelQtyInputDto.cs",
                      "RelativePath":  "CAD\\Dtos\\GetDetailModelQtyInputDto.cs",
                      "Directory":  "CAD\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\GetDetailModelQtyInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Dtos\\GetDetailModelQtyInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetDetailModelQtyOutput.cs",
                      "RelativePath":  "CAD\\Dtos\\GetDetailModelQtyOutput.cs",
                      "Directory":  "CAD\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\GetDetailModelQtyOutput.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Dtos\\GetDetailModelQtyOutput.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderCadlayout_EtCadDto.cs",
                      "RelativePath":  "CAD\\Dtos\\SorderCadlayout_EtCadDto.cs",
                      "Directory":  "CAD\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\SorderCadlayout_EtCadDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Dtos\\SorderCadlayout_EtCadDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BlitCADOuputDto.cs",
                      "RelativePath":  "CAD\\Dtos\\Blit\\BlitCADOuputDto.cs",
                      "Directory":  "CAD\\Dtos\\Blit",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\CAD\\Dtos\\Blit\\BlitCADOuputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "AppConfigurations.cs",
                      "RelativePath":  "Configuration\\AppConfigurations.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\AppConfigurations.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\AppConfigurations.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ETCadConfiguration.cs",
                      "RelativePath":  "Configuration\\ETCadConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\ETCadConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\ETCadConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ExternalOrderConfiguration.cs",
                      "RelativePath":  "Configuration\\ExternalOrderConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\ExternalOrderConfiguration.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ICadConfiguration.cs",
                      "RelativePath":  "Configuration\\ICadConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\ICadConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\ICadConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IdentityServerConfiguration.cs",
                      "RelativePath":  "Configuration\\IdentityServerConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IdentityServerConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\IdentityServerConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IExternalOrderConfiguration.cs",
                      "RelativePath":  "Configuration\\IExternalOrderConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IExternalOrderConfiguration.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IIdentityServerConfiguration.cs",
                      "RelativePath":  "Configuration\\IIdentityServerConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IIdentityServerConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\IIdentityServerConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IJYYMTMConfiguration.cs",
                      "RelativePath":  "Configuration\\IJYYMTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IJYYMTMConfiguration.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IMTMConfiguration.cs",
                      "RelativePath":  "Configuration\\IMTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IMTMConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\IMTMConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IUpsConfiguration.cs",
                      "RelativePath":  "Configuration\\IUpsConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\IUpsConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\IUpsConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "JYYMTMConfiguration.cs",
                      "RelativePath":  "Configuration\\JYYMTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\JYYMTMConfiguration.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "MTMConfiguration.cs",
                      "RelativePath":  "Configuration\\MTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\MTMConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\MTMConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MTMConfigurationExtensions.cs",
                      "RelativePath":  "Configuration\\MTMConfigurationExtensions.cs",
                      "Directory":  "Configuration",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\MTMConfigurationExtensions.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "UpsConfiguration.cs",
                      "RelativePath":  "Configuration\\UpsConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Configuration\\UpsConfiguration.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\UpsConfiguration.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Reference.cs",
                      "RelativePath":  "Connected Services\\UpsShippingServiceReference\\Reference.cs",
                      "Directory":  "Connected Services\\UpsShippingServiceReference",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Connected Services\\UpsShippingServiceReference\\Reference.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Connected Services\\UpsShippingServiceReference\\Reference.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Client.cs",
                      "RelativePath":  "Domain\\BAD\\Client.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\Client.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\Client.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientAddress.cs",
                      "RelativePath":  "Domain\\BAD\\ClientAddress.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientAddress.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientAddress.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientItem.cs",
                      "RelativePath":  "Domain\\BAD\\ClientItem.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientModel.cs",
                      "RelativePath":  "Domain\\BAD\\ClientModel.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientModelClass.cs",
                      "RelativePath":  "Domain\\BAD\\ClientModelClass.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientModelClass.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientModelClass.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientPerson.cs",
                      "RelativePath":  "Domain\\BAD\\ClientPerson.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientPerson.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientPerson.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientPersonImage.cs",
                      "RelativePath":  "Domain\\BAD\\ClientPersonImage.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientPersonImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientPersonImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ClientShop.cs",
                      "RelativePath":  "Domain\\BAD\\ClientShop.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ClientShop.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ClientShop.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Item.cs",
                      "RelativePath":  "Domain\\BAD\\Item.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\Item.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\Item.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemConfig.cs",
                      "RelativePath":  "Domain\\BAD\\ItemConfig.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemConfig.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemConfig.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemElemItem.cs",
                      "RelativePath":  "Domain\\BAD\\ItemElemItem.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemElemItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemElemItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemElemItemConfig.cs",
                      "RelativePath":  "Domain\\BAD\\ItemElemItemConfig.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemElemItemConfig.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemElemItemConfig.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemImage.cs",
                      "RelativePath":  "Domain\\BAD\\ItemImage.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemSeries.cs",
                      "RelativePath":  "Domain\\BAD\\ItemSeries.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemSeries.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemSeries.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemSeriesItem.cs",
                      "RelativePath":  "Domain\\BAD\\ItemSeriesItem.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\ItemSeriesItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemSeriesItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "WashingLabelModelElemList.cs",
                      "RelativePath":  "Domain\\BAD\\WashingLabelModelElemList.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\BAD\\WashingLabelModelElemList.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Body.cs",
                      "RelativePath":  "Domain\\MOM\\Body.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\Body.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\Body.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BodyImage.cs",
                      "RelativePath":  "Domain\\MOM\\BodyImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\BodyImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\BodyImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BodyList.cs",
                      "RelativePath":  "Domain\\MOM\\BodyList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\BodyList.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\BodyList.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BodyPrice.cs",
                      "RelativePath":  "Domain\\MOM\\BodyPrice.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\BodyPrice.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "CadLayoutModelElem.cs",
                      "RelativePath":  "Domain\\MOM\\CadLayoutModelElem.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\CadLayoutModelElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\CadLayoutModelElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CadRuleLayout.cs",
                      "RelativePath":  "Domain\\MOM\\CadRuleLayout.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\CadRuleLayout.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\CadRuleLayout.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISizeBaseDto.cs",
                      "RelativePath":  "Domain\\MOM\\ISizeBaseDto.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ISizeBaseDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ISizeBaseDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Model.cs",
                      "RelativePath":  "Domain\\MOM\\Model.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\Model.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\Model.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelBase.cs",
                      "RelativePath":  "Domain\\MOM\\ModelBase.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelBaseImage.cs",
                      "RelativePath":  "Domain\\MOM\\ModelBaseImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelBaseImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelBaseImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelBodyList.cs",
                      "RelativePath":  "Domain\\MOM\\ModelBodyList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelBodyList.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelBodyList.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElem.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElem.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemBase.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemBase.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemCad.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemCad.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemCad.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemCad.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemImage.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemItemGroup.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemItemGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemItemGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemItemGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemList.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemList.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemList.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemListStatus.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemListStatus.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemListStatus.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemListStatus.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemPrice.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemPrice.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemPrice.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemPrice.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemPriceByUser.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemPriceByUser.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemPriceByUser.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ModelElemRule.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemRule.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemRule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemRule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemRuleDetail.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemRuleDetail.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemRuleDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemRuleDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemVariant.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemVariant.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelElemVariant.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemVariant.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelImage.cs",
                      "RelativePath":  "Domain\\MOM\\ModelImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelModelElem.cs",
                      "RelativePath":  "Domain\\MOM\\ModelModelElem.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelModelElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelModelElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelModelElemClient.cs",
                      "RelativePath":  "Domain\\MOM\\ModelModelElemClient.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelModelElemClient.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelModelElemClient.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelSizeColumn.cs",
                      "RelativePath":  "Domain\\MOM\\ModelSizeColumn.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelSizeColumn.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelSizeColumn.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelSizeColumnBody.cs",
                      "RelativePath":  "Domain\\MOM\\ModelSizeColumnBody.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelSizeColumnBody.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelSizeColumnBody.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelType.cs",
                      "RelativePath":  "Domain\\MOM\\ModelType.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\ModelType.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelType.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MOMTabWorkshop.cs",
                      "RelativePath":  "Domain\\MOM\\MOMTabWorkshop.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\MOMTabWorkshop.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOMTabWorkshop.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PartPrice.cs",
                      "RelativePath":  "Domain\\MOM\\PartPrice.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\PartPrice.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\PartPrice.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Sew.cs",
                      "RelativePath":  "Domain\\MOM\\Sew.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\Sew.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\Sew.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SewBase.cs",
                      "RelativePath":  "Domain\\MOM\\SewBase.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SewBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SewBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SewList.cs",
                      "RelativePath":  "Domain\\MOM\\SewList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SewList.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SewList.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Size.cs",
                      "RelativePath":  "Domain\\MOM\\Size.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\Size.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\Size.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeBaseDto.cs",
                      "RelativePath":  "Domain\\MOM\\SizeBaseDto.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeBaseDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SizeColumnImage.cs",
                      "RelativePath":  "Domain\\MOM\\SizeColumnImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeColumnImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeColumnImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeColumnPrice.cs",
                      "RelativePath":  "Domain\\MOM\\SizeColumnPrice.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeColumnPrice.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SizeList.cs",
                      "RelativePath":  "Domain\\MOM\\SizeList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeList.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeList.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeListBase.cs",
                      "RelativePath":  "Domain\\MOM\\SizeListBase.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeListBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeListBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeListSizeElema.cs",
                      "RelativePath":  "Domain\\MOM\\SizeListSizeElema.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeListSizeElema.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeListSizeElema.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeListSizeElemb.cs",
                      "RelativePath":  "Domain\\MOM\\SizeListSizeElemb.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemb.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemb.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeListSizeElemc.cs",
                      "RelativePath":  "Domain\\MOM\\SizeListSizeElemc.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemc.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemc.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeListSizeElemd.cs",
                      "RelativePath":  "Domain\\MOM\\SizeListSizeElemd.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemd.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeListSizeElemd.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeRule.cs",
                      "RelativePath":  "Domain\\MOM\\SizeRule.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeRule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeRule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeRuleCheck.cs",
                      "RelativePath":  "Domain\\MOM\\SizeRuleCheck.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeRuleCheck.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeRuleCheck.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeRuleDetail.cs",
                      "RelativePath":  "Domain\\MOM\\SizeRuleDetail.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\MOM\\SizeRuleDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\SizeRuleDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "External_Order.cs",
                      "RelativePath":  "Domain\\ODM\\External_Order.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\External_Order.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "External_OrderBody.cs",
                      "RelativePath":  "Domain\\ODM\\External_OrderBody.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\External_OrderBody.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "External_OrderElem.cs",
                      "RelativePath":  "Domain\\ODM\\External_OrderElem.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\External_OrderElem.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "External_OrderModel.cs",
                      "RelativePath":  "Domain\\ODM\\External_OrderModel.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\External_OrderModel.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "External_OrderSize.cs",
                      "RelativePath":  "Domain\\ODM\\External_OrderSize.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\External_OrderSize.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ODM_SorderDetailBarcode.cs",
                      "RelativePath":  "Domain\\ODM\\ODM_SorderDetailBarcode.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\ODM_SorderDetailBarcode.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\ODM_SorderDetailBarcode.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ODM_SorderDetailSchedule.cs",
                      "RelativePath":  "Domain\\ODM\\ODM_SorderDetailSchedule.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\ODM_SorderDetailSchedule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\ODM_SorderDetailSchedule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ODM_SorderSize.cs",
                      "RelativePath":  "Domain\\ODM\\ODM_SorderSize.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\ODM_SorderSize.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\ODM_SorderSize.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Sorder.cs",
                      "RelativePath":  "Domain\\ODM\\Sorder.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\Sorder.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\Sorder.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderAfterSale.cs",
                      "RelativePath":  "Domain\\ODM\\SorderAfterSale.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderAfterSale.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderAfterSale.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderBill.cs",
                      "RelativePath":  "Domain\\ODM\\SorderBill.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderBill.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderBill.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderBillDetail.cs",
                      "RelativePath":  "Domain\\ODM\\SorderBillDetail.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderBillDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderBillDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderCadDetail.cs",
                      "RelativePath":  "Domain\\ODM\\SorderCadDetail.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderCadDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderCadDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderCadLayout.cs",
                      "RelativePath":  "Domain\\ODM\\SorderCadLayout.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderCadLayout.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderCadLayout.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderCadState.cs",
                      "RelativePath":  "Domain\\ODM\\SorderCadState.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderCadState.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderCadState.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetail.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetail.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailBody.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailBody.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailBody.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailBody.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailElem.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailElem.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailElemVariant.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailElemVariant.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailElemVariant.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailElemVariant.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailFile.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailFile.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailFile.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderDetailImage.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailImage.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailModel.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailModel.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailOtherModel.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailOtherModel.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailOtherModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailOtherModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailSize.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailSize.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailSize.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailSize.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailSizeVariant.cs",
                      "RelativePath":  "Domain\\ODM\\SorderDetailSizeVariant.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderDetailSizeVariant.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderDetailSizeVariant.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderItemState.cs",
                      "RelativePath":  "Domain\\ODM\\SorderItemState.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderItemState.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderItemStateLog.cs",
                      "RelativePath":  "Domain\\ODM\\SorderItemStateLog.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderItemStateLog.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderLog.cs",
                      "RelativePath":  "Domain\\ODM\\SorderLog.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderLog.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderLog.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProModel.cs",
                      "RelativePath":  "Domain\\ODM\\SorderProModel.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderProModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderProModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepair.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepair.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepair.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepair.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairBody.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairBody.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairBody.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairBody.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairElem.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairElem.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairImage.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairImage.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairModel.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairModel.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairPartPrice.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairPartPrice.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairPartPrice.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairPartPrice.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairSize.cs",
                      "RelativePath":  "Domain\\ODM\\SorderRepairSize.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\ODM\\SorderRepairSize.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderRepairSize.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_ProduceOrder.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_ProduceOrder.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrder.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrder.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_ProduceOrderItem.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_ProduceOrderItem.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrderItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrderItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_ProduceOrder_ModelGydGgd.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_ProduceOrder_ModelGydGgd.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrder_ModelGydGgd.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_ProduceOrder_ModelGydGgd.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_Workshop.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_Workshop.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_Workshop.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_Workshop.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_WorkshopCheckGroup.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_WorkshopCheckGroup.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_WorkshopCheckGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_WorkshopCheckGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_WorkshopModTabWorkshop.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_WorkshopModTabWorkshop.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_WorkshopModTabWorkshop.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_WorkshopModTabWorkshop.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_ZHDCX.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_ZHDCX.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_ZHDCX.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_ZHDCX.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PRD_ZHDCX_FIlENAME.cs",
                      "RelativePath":  "Domain\\PRD\\PRD_ZHDCX_FIlENAME.cs",
                      "Directory":  "Domain\\PRD",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\PRD\\PRD_ZHDCX_FIlENAME.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\PRD\\PRD_ZHDCX_FIlENAME.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Action.cs",
                      "RelativePath":  "Domain\\SYM\\Action.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\Action.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\Action.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ActionGroup.cs",
                      "RelativePath":  "Domain\\SYM\\ActionGroup.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ActionGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ActionGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ActionType.cs",
                      "RelativePath":  "Domain\\SYM\\ActionType.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ActionType.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ActionType.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Department.cs",
                      "RelativePath":  "Domain\\SYM\\Department.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\Department.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\Department.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderBase.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderBase.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderBaseDetail.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderBaseDetail.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderBaseDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderBaseDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelBody.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderModelBody.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderModelBody.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderModelBody.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelElem.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderModelElem.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderModelElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderModelElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelElemDetail.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderModelElemDetail.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderModelElemDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderModelElemDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelGroup.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderModelGroup.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderModelGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderModelGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelSize.cs",
                      "RelativePath":  "Domain\\SYM\\ImportSorderModelSize.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\ImportSorderModelSize.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\ImportSorderModelSize.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Role.cs",
                      "RelativePath":  "Domain\\SYM\\Role.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\Role.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\Role.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RoleAction.cs",
                      "RelativePath":  "Domain\\SYM\\RoleAction.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\RoleAction.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\RoleAction.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYM_Corporation.cs",
                      "RelativePath":  "Domain\\SYM\\SYM_Corporation.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\SYM_Corporation.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\SYM_Corporation.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "User.cs",
                      "RelativePath":  "Domain\\SYM\\User.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\User.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\User.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UserClient.cs",
                      "RelativePath":  "Domain\\SYM\\UserClient.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\UserClient.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\UserClient.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UserRole.cs",
                      "RelativePath":  "Domain\\SYM\\UserRole.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\UserRole.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\UserRole.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Workbench.cs",
                      "RelativePath":  "Domain\\SYM\\Workbench.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\Workbench.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\Workbench.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "WorkbenchDetail.cs",
                      "RelativePath":  "Domain\\SYM\\WorkbenchDetail.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\WorkbenchDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\WorkbenchDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "WorkbenchReceiverDetail.cs",
                      "RelativePath":  "Domain\\SYM\\WorkbenchReceiverDetail.cs",
                      "Directory":  "Domain\\SYM",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYM\\WorkbenchReceiverDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYM\\WorkbenchReceiverDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Bank.cs",
                      "RelativePath":  "Domain\\SYS\\Bank.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Bank.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Bank.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Brand.cs",
                      "RelativePath":  "Domain\\SYS\\Brand.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Brand.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Brand.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BrandLine.cs",
                      "RelativePath":  "Domain\\SYS\\BrandLine.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\BrandLine.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\BrandLine.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CadLayout.cs",
                      "RelativePath":  "Domain\\SYS\\CadLayout.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\CadLayout.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\CadLayout.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CadRule.cs",
                      "RelativePath":  "Domain\\SYS\\CadRule.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\CadRule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\CadRule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CadSizeColumnAndBody.cs",
                      "RelativePath":  "Domain\\SYS\\CadSizeColumnAndBody.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\CadSizeColumnAndBody.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Class.cs",
                      "RelativePath":  "Domain\\SYS\\Class.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Class.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Class.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Currency.cs",
                      "RelativePath":  "Domain\\SYS\\Currency.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Currency.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Currency.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "DeliveryDate.cs",
                      "RelativePath":  "Domain\\SYS\\DeliveryDate.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\DeliveryDate.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\DeliveryDate.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "DeliveryMethod.cs",
                      "RelativePath":  "Domain\\SYS\\DeliveryMethod.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\DeliveryMethod.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "DocumentDescription.cs",
                      "RelativePath":  "Domain\\SYS\\DocumentDescription.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\DocumentDescription.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "FileManagement.cs",
                      "RelativePath":  "Domain\\SYS\\FileManagement.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\FileManagement.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Group.cs",
                      "RelativePath":  "Domain\\SYS\\Group.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Group.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Group.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemTextureLength.cs",
                      "RelativePath":  "Domain\\SYS\\ItemTextureLength.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\ItemTextureLength.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\ItemTextureLength.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MoveType.cs",
                      "RelativePath":  "Domain\\SYS\\MoveType.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\MoveType.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\MoveType.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PaymentMethod.cs",
                      "RelativePath":  "Domain\\SYS\\PaymentMethod.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\PaymentMethod.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\PaymentMethod.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PaymentTerms.cs",
                      "RelativePath":  "Domain\\SYS\\PaymentTerms.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\PaymentTerms.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\PaymentTerms.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Season.cs",
                      "RelativePath":  "Domain\\SYS\\Season.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Season.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Season.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeColumn.cs",
                      "RelativePath":  "Domain\\SYS\\SizeColumn.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeColumn.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeColumn.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeColumnGroup.cs",
                      "RelativePath":  "Domain\\SYS\\SizeColumnGroup.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeColumnGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeColumnGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeElema.cs",
                      "RelativePath":  "Domain\\SYS\\SizeElema.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeElema.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeElema.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeElemb.cs",
                      "RelativePath":  "Domain\\SYS\\SizeElemb.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeElemb.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeElemb.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeElemc.cs",
                      "RelativePath":  "Domain\\SYS\\SizeElemc.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeElemc.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeElemc.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeElemd.cs",
                      "RelativePath":  "Domain\\SYS\\SizeElemd.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SizeElemd.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SizeElemd.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Status.cs",
                      "RelativePath":  "Domain\\SYS\\Status.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Status.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Status.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SubGroup.cs",
                      "RelativePath":  "Domain\\SYS\\SubGroup.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SubGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SubGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYS_FormulaGroup.cs",
                      "RelativePath":  "Domain\\SYS\\SYS_FormulaGroup.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SYS_FormulaGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SYS_FormulaGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYS_GroupElem.cs",
                      "RelativePath":  "Domain\\SYS\\SYS_GroupElem.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\SYS_GroupElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\SYS_GroupElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Unit.cs",
                      "RelativePath":  "Domain\\SYS\\Unit.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Unit.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Unit.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Vat.cs",
                      "RelativePath":  "Domain\\SYS\\Vat.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\Vat.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SYS\\Vat.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "WorkCalendar.cs",
                      "RelativePath":  "Domain\\SYS\\WorkCalendar.cs",
                      "Directory":  "Domain\\SYS",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\SYS\\WorkCalendar.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ItemBatch.cs",
                      "RelativePath":  "Domain\\WAR\\ItemBatch.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemBatch.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemBatch.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemBom.cs",
                      "RelativePath":  "Domain\\WAR\\ItemBom.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemBom.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemBom.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemBomDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ItemBomDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemBomDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemBomDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemPurchase.cs",
                      "RelativePath":  "Domain\\WAR\\ItemPurchase.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemPurchase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemPurchase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemPurchaseDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ItemPurchaseDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemPurchaseDetail.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ItemStock.cs",
                      "RelativePath":  "Domain\\WAR\\ItemStock.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemStock.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemStock.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ItemStockDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemStockDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemStockDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockInvoices.cs",
                      "RelativePath":  "Domain\\WAR\\ItemStockInvoices.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemStockInvoices.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemStockInvoices.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockInvoicesDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ItemStockInvoicesDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemStockInvoicesDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemStockInvoicesDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockPosition.cs",
                      "RelativePath":  "Domain\\WAR\\ItemStockPosition.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ItemStockPosition.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemStockPosition.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductPacking.cs",
                      "RelativePath":  "Domain\\WAR\\ProductPacking.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ProductPacking.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ProductPacking.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductPackingDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ProductPackingDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ProductPackingDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ProductPackingDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductWarehouse.cs",
                      "RelativePath":  "Domain\\WAR\\ProductWarehouse.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ProductWarehouse.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ProductWarehouse.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductWarehouseDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ProductWarehouseDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ProductWarehouseDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ProductWarehouseDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportForm.cs",
                      "RelativePath":  "Domain\\WAR\\ReportForm.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ReportForm.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ReportForm.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormConfig.cs",
                      "RelativePath":  "Domain\\WAR\\ReportFormConfig.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ReportFormConfig.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ReportFormConfig.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ReportFormDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ReportFormDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ReportFormDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportForm_1.cs",
                      "RelativePath":  "Domain\\WAR\\ReportForm_1.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ReportForm_1.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ReportForm_1.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportForm_2.cs",
                      "RelativePath":  "Domain\\WAR\\ReportForm_2.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ReportForm_2.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ReportForm_2.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Shipment.cs",
                      "RelativePath":  "Domain\\WAR\\Shipment.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\Shipment.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\Shipment.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ShipmentDetail.cs",
                      "RelativePath":  "Domain\\WAR\\ShipmentDetail.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ShipmentDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ShipmentDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ShipmentInfo.cs",
                      "RelativePath":  "Domain\\WAR\\ShipmentInfo.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\ShipmentInfo.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ShipmentInfo.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UpsAccountConfig.cs",
                      "RelativePath":  "Domain\\WAR\\UpsAccountConfig.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Domain\\WAR\\UpsAccountConfig.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\UpsAccountConfig.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "FileImageDirectoryDto.cs",
                      "RelativePath":  "Dto\\FileImageDirectoryDto.cs",
                      "Directory":  "Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\FileImageDirectoryDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ICodeEntityDto.cs",
                      "RelativePath":  "Dto\\ICodeEntityDto.cs",
                      "Directory":  "Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ICodeEntityDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\ICodeEntityDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IHasCodeEntityDto.cs",
                      "RelativePath":  "Dto\\IHasCodeEntityDto.cs",
                      "Directory":  "Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\IHasCodeEntityDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\IHasCodeEntityDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImageFileoutputDto.cs",
                      "RelativePath":  "Dto\\ImageFileoutputDto.cs",
                      "Directory":  "Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ImageFileoutputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IExternalOrder.cs",
                      "RelativePath":  "Dto\\ExternalOrder\\IExternalOrder.cs",
                      "Directory":  "Dto\\ExternalOrder",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ExternalOrder\\IExternalOrder.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IExternalOrderBody.cs",
                      "RelativePath":  "Dto\\ExternalOrder\\IExternalOrderBody.cs",
                      "Directory":  "Dto\\ExternalOrder",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ExternalOrder\\IExternalOrderBody.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IExternalOrderElem.cs",
                      "RelativePath":  "Dto\\ExternalOrder\\IExternalOrderElem.cs",
                      "Directory":  "Dto\\ExternalOrder",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ExternalOrder\\IExternalOrderElem.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IExternalOrderModel.cs",
                      "RelativePath":  "Dto\\ExternalOrder\\IExternalOrderModel.cs",
                      "Directory":  "Dto\\ExternalOrder",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ExternalOrder\\IExternalOrderModel.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "IExternalOrderSize.cs",
                      "RelativePath":  "Dto\\ExternalOrder\\IExternalOrderSize.cs",
                      "Directory":  "Dto\\ExternalOrder",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Dto\\ExternalOrder\\IExternalOrderSize.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Enums.cs",
                      "RelativePath":  "Enums\\Enums.cs",
                      "Directory":  "Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Enums\\Enums.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Enums\\Enums.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "DataRowUtiles.cs",
                      "RelativePath":  "Excel\\DataRowUtiles.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\DataRowUtiles.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\DataRowUtiles.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ExcelHelper.cs",
                      "RelativePath":  "Excel\\ExcelHelper.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\ExcelHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\ExcelHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IExcelHelper.cs",
                      "RelativePath":  "Excel\\IExcelHelper.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\IExcelHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\IExcelHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderDetailSizeExcelExport.cs",
                      "RelativePath":  "Excel\\ISorderDetailSizeExcelExport.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\ISorderDetailSizeExcelExport.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\ISorderDetailSizeExcelExport.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderProExcel.cs",
                      "RelativePath":  "Excel\\ISorderProExcel.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\ISorderProExcel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\ISorderProExcel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailSizeExcelExport.cs",
                      "RelativePath":  "Excel\\SorderDetailSizeExcelExport.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\SorderDetailSizeExcelExport.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\SorderDetailSizeExcelExport.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProExcel.cs",
                      "RelativePath":  "Excel\\SorderProExcel.cs",
                      "Directory":  "Excel",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\SorderProExcel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\SorderProExcel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ExcelSorderDetailSizeDbDto.cs",
                      "RelativePath":  "Excel\\Dto\\ExcelSorderDetailSizeDbDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\ExcelSorderDetailSizeDbDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\ExcelSorderDetailSizeDbDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ExportTemplateInputDto.cs",
                      "RelativePath":  "Excel\\Dto\\ExportTemplateInputDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\ExportTemplateInputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ExportTemplateSorderModelDto.cs",
                      "RelativePath":  "Excel\\Dto\\ExportTemplateSorderModelDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\ExportTemplateSorderModelDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ImportSorderProMessageDto.cs",
                      "RelativePath":  "Excel\\Dto\\ImportSorderProMessageDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\ImportSorderProMessageDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\ImportSorderProMessageDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProDetailSizeDto.cs",
                      "RelativePath":  "Excel\\Dto\\SorderProDetailSizeDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\SorderProDetailSizeDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderProExcelInputDto.cs",
                      "RelativePath":  "Excel\\Dto\\SorderProExcelInputDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\SorderProExcelInputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderProImportSizeTempDto.cs",
                      "RelativePath":  "Excel\\Dto\\SorderProImportSizeTempDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\SorderProImportSizeTempDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\SorderProImportSizeTempDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProImportTempDto.cs",
                      "RelativePath":  "Excel\\Dto\\SorderProImportTempDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Excel\\Dto\\SorderProImportTempDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\SorderProImportTempDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "FileServer.cs",
                      "RelativePath":  "FileServer\\FileServer.cs",
                      "Directory":  "FileServer",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\FileServer\\FileServer.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\FileServer\\FileServer.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IFileServer.cs",
                      "RelativePath":  "FileServer\\IFileServer.cs",
                      "Directory":  "FileServer",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\FileServer\\IFileServer.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\FileServer\\IFileServer.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "FileServerInputDto.cs",
                      "RelativePath":  "FileServer\\Dtos\\FileServerInputDto.cs",
                      "Directory":  "FileServer\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\FileServer\\Dtos\\FileServerInputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "FileServerOutputDto.cs",
                      "RelativePath":  "FileServer\\Dtos\\FileServerOutputDto.cs",
                      "Directory":  "FileServer\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\FileServer\\Dtos\\FileServerOutputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Helper.cs",
                      "RelativePath":  "Helper\\Helper.cs",
                      "Directory":  "Helper",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Helper\\Helper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Helper\\Helper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "TransExpV2.cs",
                      "RelativePath":  "Helper\\TransExpV2.cs",
                      "Directory":  "Helper",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Helper\\TransExpV2.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Helper\\TransExpV2.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IMESRemoteService.cs",
                      "RelativePath":  "Http\\Api\\IMESRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\IMESRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\IMESRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MESRemoteService.cs",
                      "RelativePath":  "Http\\Api\\MESRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\MESRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\MESRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PSSOUserRemoteService.cs",
                      "RelativePath":  "Http\\Api\\PSSOUserRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\PSSOUserRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\PSSOUserRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SSOUserRemoteService.cs",
                      "RelativePath":  "Http\\Api\\SSOUserRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\SSOUserRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\SSOUserRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BlitCadRemoteService.cs",
                      "RelativePath":  "Http\\Api\\BlitApi\\BlitCadRemoteService.cs",
                      "Directory":  "Http\\Api\\BlitApi",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\BlitApi\\BlitCadRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\BlitApi\\BlitCadRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IBlitCadRemoteService.cs",
                      "RelativePath":  "Http\\Api\\BlitApi\\IBlitCadRemoteService.cs",
                      "Directory":  "Http\\Api\\BlitApi",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\BlitApi\\IBlitCadRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\BlitApi\\IBlitCadRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckPlanDetailScheduleInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckPlanDetailScheduleInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckPlanDetailScheduleInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckPlanDetailScheduleInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckPlanDetailScheduleOutPut.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckPlanDetailScheduleOutPut.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckPlanDetailScheduleOutPut.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckPlanDetailScheduleOutPut.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckProductionPlanStateGetBySorderIdDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckProductionPlanStateGetBySorderIdDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckProductionPlanStateGetBySorderIdDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckProductionPlanStateGetBySorderIdDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckProductionPlanStateOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckProductionPlanStateOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckProductionPlanStateOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckProductionPlanStateOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckStateInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckStateInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckStateInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckStateInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CheckStateOutputtDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CheckStateOutputtDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\CheckStateOutputtDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CheckStateOutputtDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetProductionPlanDetailInput.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\GetProductionPlanDetailInput.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\GetProductionPlanDetailInput.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "GetProductionPlanDetailOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\GetProductionPlanDetailOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\GetProductionPlanDetailOutputDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "MESSorderInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\MESSorderInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\MESSorderInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\MESSorderInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductionPlanStateGetBySorderIdDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\ProductionPlanStateGetBySorderIdDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\ProductionPlanStateGetBySorderIdDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\ProductionPlanStateGetBySorderIdDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductionPlanStateOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\ProductionPlanStateOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\ProductionPlanStateOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\ProductionPlanStateOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PutInStorageBySerialNumberInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\PutInStorageBySerialNumberInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\PutInStorageBySerialNumberInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\PutInStorageBySerialNumberInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PutInStorageBySerialNumberOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\PutInStorageBySerialNumberOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\PutInStorageBySerialNumberOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\PutInStorageBySerialNumberOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderMesInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SorderMesInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\SorderMesInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SorderMesInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderMesOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SorderMesOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\Dtos\\SorderMesOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SorderMesOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ETCadRemoteService.cs",
                      "RelativePath":  "Http\\Api\\ETApi\\ETCadRemoteService.cs",
                      "Directory":  "Http\\Api\\ETApi",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\ETApi\\ETCadRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\ETApi\\ETCadRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IETCadRemoteService.cs",
                      "RelativePath":  "Http\\Api\\ETApi\\IETCadRemoteService.cs",
                      "Directory":  "Http\\Api\\ETApi",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Api\\ETApi\\IETCadRemoteService.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\ETApi\\IETCadRemoteService.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "EtCadResultDto.cs",
                      "RelativePath":  "Http\\Model\\EtCadResultDto.cs",
                      "Directory":  "Http\\Model",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Model\\EtCadResultDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Model\\EtCadResultDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UserInfoAndPermissionsModel.cs",
                      "RelativePath":  "Http\\Model\\UserInfoAndPermissionsModel.cs",
                      "Directory":  "Http\\Model",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Http\\Model\\UserInfoAndPermissionsModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Model\\UserInfoAndPermissionsModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "XMMTMLocalizationConfigurer.cs",
                      "RelativePath":  "Localization\\XMMTMLocalizationConfigurer.cs",
                      "Directory":  "Localization",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Localization\\XMMTMLocalizationConfigurer.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Localization\\XMMTMLocalizationConfigurer.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IRuleManager.cs",
                      "RelativePath":  "ModelElemRuleManager\\IRuleManager.cs",
                      "Directory":  "ModelElemRuleManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\IRuleManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\IRuleManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RuleManager.cs",
                      "RelativePath":  "ModelElemRuleManager\\RuleManager.cs",
                      "Directory":  "ModelElemRuleManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\RuleManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\RuleManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RuleManagerAsync.cs",
                      "RelativePath":  "ModelElemRuleManager\\RuleManagerAsync.cs",
                      "Directory":  "ModelElemRuleManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\RuleManagerAsync.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\RuleManagerAsync.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RuleManagerTest.cs",
                      "RelativePath":  "ModelElemRuleManager\\RuleManagerTest.cs",
                      "Directory":  "ModelElemRuleManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\RuleManagerTest.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\RuleManagerTest.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ModelElemRuleOutputDto.cs",
                      "RelativePath":  "ModelElemRuleManager\\Dtos\\ModelElemRuleOutputDto.cs",
                      "Directory":  "ModelElemRuleManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Dtos\\ModelElemRuleOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Dtos\\ModelElemRuleOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Operand.cs",
                      "RelativePath":  "ModelElemRuleManager\\Dtos\\Operand.cs",
                      "Directory":  "ModelElemRuleManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Dtos\\Operand.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Dtos\\Operand.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RuleOperand.cs",
                      "RelativePath":  "ModelElemRuleManager\\Dtos\\RuleOperand.cs",
                      "Directory":  "ModelElemRuleManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Dtos\\RuleOperand.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Dtos\\RuleOperand.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailElemRuleGetInputDto.cs",
                      "RelativePath":  "ModelElemRuleManager\\Dtos\\SorderDetailElemRuleGetInputDto.cs",
                      "Directory":  "ModelElemRuleManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Dtos\\SorderDetailElemRuleGetInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Dtos\\SorderDetailElemRuleGetInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderDetailElemRuleOutputDto.cs",
                      "RelativePath":  "ModelElemRuleManager\\Dtos\\SorderDetailElemRuleOutputDto.cs",
                      "Directory":  "ModelElemRuleManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Dtos\\SorderDetailElemRuleOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Dtos\\SorderDetailElemRuleOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "AnalysisRule.cs",
                      "RelativePath":  "ModelElemRuleManager\\Helper\\AnalysisRule.cs",
                      "Directory":  "ModelElemRuleManager\\Helper",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\ModelElemRuleManager\\Helper\\AnalysisRule.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\ModelElemRuleManager\\Helper\\AnalysisRule.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderPrint.cs",
                      "RelativePath":  "Pdf\\ISorderPrint.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ISorderPrint.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ISorderPrint.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderPrintPro.cs",
                      "RelativePath":  "Pdf\\ISorderPrintPro.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ISorderPrintPro.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ISorderPrintPro.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderRepairPrint.cs",
                      "RelativePath":  "Pdf\\ISorderRepairPrint.cs",
                      "Directory":  "Pdf",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ISorderRepairPrint.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ItemStockInvociesPdf.cs",
                      "RelativePath":  "Pdf\\ItemStockInvociesPdf.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ItemStockInvociesPdf.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ItemStockInvociesPdf.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MyCellEvent.cs",
                      "RelativePath":  "Pdf\\MyCellEvent.cs",
                      "Directory":  "Pdf",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\MyCellEvent.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "PDFBase.cs",
                      "RelativePath":  "Pdf\\PDFBase.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\PDFBase.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\PDFBase.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PDFBase1.cs",
                      "RelativePath":  "Pdf\\PDFBase1.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\PDFBase1.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\PDFBase1.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductPackingPdf.cs",
                      "RelativePath":  "Pdf\\ProductPackingPdf.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ProductPackingPdf.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ProductPackingPdf.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_1.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_1.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_1.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_1.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_2.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_2.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_2.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_2.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_3.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_3.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_3.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_3.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_4.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_4.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_4.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_4.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_4_4.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_4_4.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_4_4.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_4_4.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFormPdf_5.cs",
                      "RelativePath":  "Pdf\\ReportFormPdf_5.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\ReportFormPdf_5.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\ReportFormPdf_5.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderItemStatePdf.cs",
                      "RelativePath":  "Pdf\\SorderItemStatePdf.cs",
                      "Directory":  "Pdf",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\SorderItemStatePdf.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderPrint.cs",
                      "RelativePath":  "Pdf\\SorderPrint.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\SorderPrint.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\SorderPrint.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderPrint20201124.cs",
                      "RelativePath":  "Pdf\\SorderPrint20201124.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\SorderPrint20201124.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\SorderPrint20201124.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderPrintPro.cs",
                      "RelativePath":  "Pdf\\SorderPrintPro.cs",
                      "Directory":  "Pdf",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\SorderPrintPro.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\SorderPrintPro.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairPrint.cs",
                      "RelativePath":  "Pdf\\SorderRepairPrint.cs",
                      "Directory":  "Pdf",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\SorderRepairPrint.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "CanvasDTO.cs",
                      "RelativePath":  "Pdf\\Dtos\\CanvasDTO.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\CanvasDTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\CanvasDTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockInvociesDetailPdfDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\ItemStockInvociesDetailPdfDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesDetailPdfDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesDetailPdfDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockInvociesPdfDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\ItemStockInvociesPdfDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesPdfDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesPdfDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ItemStockInvociesPdf_2_Dto.cs",
                      "RelativePath":  "Pdf\\Dtos\\ItemStockInvociesPdf_2_Dto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesPdf_2_Dto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\ItemStockInvociesPdf_2_Dto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ProductPackingDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\ProductPackingDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\ProductPackingDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\ProductPackingDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReportFromPdf_1_Dto.cs",
                      "RelativePath":  "Pdf\\Dtos\\ReportFromPdf_1_Dto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\ReportFromPdf_1_Dto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\ReportFromPdf_1_Dto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderItemStatePdfDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\SorderItemStatePdfDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\SorderItemStatePdfDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderPrintOutputDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\SorderPrintOutputDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\SorderPrintOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\Dtos\\SorderPrintOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderRepairModelPrintDto.cs",
                      "RelativePath":  "Pdf\\Dtos\\SorderRepairModelPrintDto.cs",
                      "Directory":  "Pdf\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Pdf\\Dtos\\SorderRepairModelPrintDto.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "AssemblyInfo.cs",
                      "RelativePath":  "Properties\\AssemblyInfo.cs",
                      "Directory":  "Properties",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Properties\\AssemblyInfo.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Properties\\AssemblyInfo.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CreateSizeHelper.cs",
                      "RelativePath":  "SizeCheck\\CreateSizeHelper.cs",
                      "Directory":  "SizeCheck",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\CreateSizeHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\CreateSizeHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\ISizeCheckHelper.cs",
                      "Directory":  "SizeCheck",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\ISizeCheckHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\ISizeCheckHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\SizeCheckHelper.cs",
                      "Directory":  "SizeCheck",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\SizeCheckHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\SizeCheckHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MOM_SizeRuleDTO.cs",
                      "RelativePath":  "SizeCheck\\Dtos\\MOM_SizeRuleDTO.cs",
                      "Directory":  "SizeCheck\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Dtos\\MOM_SizeRuleDTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Dtos\\MOM_SizeRuleDTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SizeCheck_SorderDetailModelInputDto.cs",
                      "RelativePath":  "SizeCheck\\Dtos\\SizeCheck_SorderDetailModelInputDto.cs",
                      "Directory":  "SizeCheck\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Dtos\\SizeCheck_SorderDetailModelInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Dtos\\SizeCheck_SorderDetailModelInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CommonSizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\New\\CommonSizeCheckHelper.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\CommonSizeCheckHelper.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Finish.cs",
                      "RelativePath":  "SizeCheck\\New\\Finish.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\Finish.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ISizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\New\\ISizeCheckHelper.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\ISizeCheckHelper.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Measure.cs",
                      "RelativePath":  "SizeCheck\\New\\Measure.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\Measure.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Standard.cs",
                      "RelativePath":  "SizeCheck\\New\\Standard.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\Standard.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "StandardNo.cs",
                      "RelativePath":  "SizeCheck\\New\\StandardNo.cs",
                      "Directory":  "SizeCheck\\New",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\New\\StandardNo.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "CommonSizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\Old\\CommonSizeCheckHelper.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\CommonSizeCheckHelper.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Finish.cs",
                      "RelativePath":  "SizeCheck\\Old\\Finish.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\Finish.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "ISizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\Old\\ISizeCheckHelper.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\ISizeCheckHelper.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Measure.cs",
                      "RelativePath":  "SizeCheck\\Old\\Measure.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\Measure.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "Standard.cs",
                      "RelativePath":  "SizeCheck\\Old\\Standard.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\Standard.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "StandardNo.cs",
                      "RelativePath":  "SizeCheck\\Old\\StandardNo.cs",
                      "Directory":  "SizeCheck\\Old",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Old\\StandardNo.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "CommonSizeCheckHelper_Plus.cs",
                      "RelativePath":  "SizeCheck\\Plus\\CommonSizeCheckHelper_Plus.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\CommonSizeCheckHelper_Plus.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\CommonSizeCheckHelper_Plus.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Finish.cs",
                      "RelativePath":  "SizeCheck\\Plus\\Finish.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\Finish.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\Finish.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISizeCheckHelper.cs",
                      "RelativePath":  "SizeCheck\\Plus\\ISizeCheckHelper.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\ISizeCheckHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\ISizeCheckHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Measure.cs",
                      "RelativePath":  "SizeCheck\\Plus\\Measure.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\Measure.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\Measure.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "Standard.cs",
                      "RelativePath":  "SizeCheck\\Plus\\Standard.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\Standard.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\Standard.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "StandardNo.cs",
                      "RelativePath":  "SizeCheck\\Plus\\StandardNo.cs",
                      "Directory":  "SizeCheck\\Plus",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SizeCheck\\Plus\\StandardNo.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SizeCheck\\Plus\\StandardNo.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderImport.cs",
                      "RelativePath":  "SorderManager\\ISorderImport.cs",
                      "Directory":  "SorderManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\ISorderImport.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\ISorderImport.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderManage.cs",
                      "RelativePath":  "SorderManager\\ISorderManage.cs",
                      "Directory":  "SorderManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\ISorderManage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\ISorderManage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderImport.cs",
                      "RelativePath":  "SorderManager\\SorderImport.cs",
                      "Directory":  "SorderManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\SorderImport.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\SorderImport.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderManage.cs",
                      "RelativePath":  "SorderManager\\SorderManage.cs",
                      "Directory":  "SorderManager",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\SorderManage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\SorderManage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetSorderInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\GetSorderInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\GetSorderInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\GetSorderInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetSorderOutputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\GetSorderOutputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\GetSorderOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\GetSorderOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetSorderProInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\GetSorderProInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\GetSorderProInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\GetSorderProInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GetSorderProOutputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\GetSorderProOutputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\GetSorderProOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\GetSorderProOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportModelModelElemDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportModelModelElemDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportModelModelElemDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportModelModelElemDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSizeColumnDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSizeColumnDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSizeColumnDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSizeColumnDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderDetailModelDtoByMTO.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSorderDetailModelDtoByMTO.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderDetailModelDtoByMTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderDetailModelDtoByMTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderMessageDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSorderMessageDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderMessageDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderMessageDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelBodyDtoByMTO.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSorderModelBodyDtoByMTO.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelBodyDtoByMTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelBodyDtoByMTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelElemDtoByMTO.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSorderModelElemDtoByMTO.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelElemDtoByMTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelElemDtoByMTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSorderModelSizeDtoByMTO.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSorderModelSizeDtoByMTO.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelSizeDtoByMTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSorderModelSizeDtoByMTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ImportSysName.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSysName.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\ImportSysName.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSysName.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MapperAttribute.cs",
                      "RelativePath":  "SorderManager\\Dtos\\MapperAttribute.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\MapperAttribute.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "SorderDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\SorderDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderOutputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderOutputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\SorderOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderProInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\SorderProInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderProInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProModelDelInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderProModelDelInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\SorderProModelDelInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderProModelDelInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProOutputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderProOutputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SorderManager\\Dtos\\SorderProOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderProOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ConfigManager.cs",
                      "RelativePath":  "SystemConfig\\ConfigManager.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\ConfigManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\ConfigManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IConfigManager.cs",
                      "RelativePath":  "SystemConfig\\IConfigManager.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\IConfigManager.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\IConfigManager.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ISorderProImportTemplate.cs",
                      "RelativePath":  "SystemConfig\\ISorderProImportTemplate.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\ISorderProImportTemplate.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\ISorderProImportTemplate.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "JsonConifgHelper - 复制.cs",
                      "RelativePath":  "SystemConfig\\JsonConifgHelper - 复制.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Source Only",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\JsonConifgHelper - 复制.cs",
                      "TargetPath":  "Not Exist",
                      "Action":  "Copy to Target"
                  },
                  {
                      "FileName":  "JsonConifgHelper.cs",
                      "RelativePath":  "SystemConfig\\JsonConifgHelper.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\JsonConifgHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\JsonConifgHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderProImportTemplate.cs",
                      "RelativePath":  "SystemConfig\\SorderProImportTemplate.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\SorderProImportTemplate.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\SorderProImportTemplate.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SystemConfig.cs",
                      "RelativePath":  "SystemConfig\\SystemConfig.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\SystemConfig.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\SystemConfig.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SystemConfigGuid.cs",
                      "RelativePath":  "SystemConfig\\SystemConfigGuid.cs",
                      "Directory":  "SystemConfig",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\SystemConfigGuid.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\SystemConfigGuid.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ConfigDto.cs",
                      "RelativePath":  "SystemConfig\\Dto\\ConfigDto.cs",
                      "Directory":  "SystemConfig\\Dto",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\SystemConfig\\Dto\\ConfigDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SystemConfig\\Dto\\ConfigDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MSG_MessageGroup.cs",
                      "RelativePath":  "TEST\\MSG_MessageGroup.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\MSG_MessageGroup.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\MSG_MessageGroup.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MSG_MessageReceive.cs",
                      "RelativePath":  "TEST\\MSG_MessageReceive.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\MSG_MessageReceive.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\MSG_MessageReceive.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "MSG_Messages.cs",
                      "RelativePath":  "TEST\\MSG_Messages.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\MSG_Messages.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\MSG_Messages.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_AddressSync.cs",
                      "RelativePath":  "TEST\\ORC_AddressSync.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_AddressSync.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_AddressSync.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Customer.cs",
                      "RelativePath":  "TEST\\ORC_Customer.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Customer.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Customer.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_CustomerSync.cs",
                      "RelativePath":  "TEST\\ORC_CustomerSync.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_CustomerSync.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_CustomerSync.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Invoice.cs",
                      "RelativePath":  "TEST\\ORC_Invoice.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Invoice.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Invoice.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_InvoiceShipping.cs",
                      "RelativePath":  "TEST\\ORC_InvoiceShipping.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_InvoiceShipping.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_InvoiceShipping.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_OrderDetailSync.cs",
                      "RelativePath":  "TEST\\ORC_OrderDetailSync.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_OrderDetailSync.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_OrderDetailSync.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_OrderSync.cs",
                      "RelativePath":  "TEST\\ORC_OrderSync.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_OrderSync.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_OrderSync.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Receive.cs",
                      "RelativePath":  "TEST\\ORC_Receive.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Receive.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Receive.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_ReceiveDetail.cs",
                      "RelativePath":  "TEST\\ORC_ReceiveDetail.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_ReceiveDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_ReceiveDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_ReceiveShipType.cs",
                      "RelativePath":  "TEST\\ORC_ReceiveShipType.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_ReceiveShipType.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_ReceiveShipType.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Shipping.cs",
                      "RelativePath":  "TEST\\ORC_Shipping.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Shipping.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Shipping.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_ShippingDetail.cs",
                      "RelativePath":  "TEST\\ORC_ShippingDetail.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_ShippingDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_ShippingDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_ShippingDetailContent.cs",
                      "RelativePath":  "TEST\\ORC_ShippingDetailContent.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_ShippingDetailContent.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_ShippingDetailContent.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Status.cs",
                      "RelativePath":  "TEST\\ORC_Status.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Status.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Status.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_SyncChargeIn.cs",
                      "RelativePath":  "TEST\\ORC_SyncChargeIn.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_SyncChargeIn.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_SyncChargeIn.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_SyncChargeInSum.cs",
                      "RelativePath":  "TEST\\ORC_SyncChargeInSum.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_SyncChargeInSum.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_SyncChargeInSum.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_SyncShippingOld.cs",
                      "RelativePath":  "TEST\\ORC_SyncShippingOld.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_SyncShippingOld.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_SyncShippingOld.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_Warehouse.cs",
                      "RelativePath":  "TEST\\ORC_Warehouse.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_Warehouse.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_Warehouse.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ORC_WarehouseStock.cs",
                      "RelativePath":  "TEST\\ORC_WarehouseStock.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\ORC_WarehouseStock.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\ORC_WarehouseStock.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RPT_SorderMTM.cs",
                      "RelativePath":  "TEST\\RPT_SorderMTM.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\RPT_SorderMTM.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\RPT_SorderMTM.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RPT_SorderMTMDetailElem.cs",
                      "RelativePath":  "TEST\\RPT_SorderMTMDetailElem.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\RPT_SorderMTMDetailElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\RPT_SorderMTMDetailElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "RPT_SorderMTMElemImage.cs",
                      "RelativePath":  "TEST\\RPT_SorderMTMElemImage.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\RPT_SorderMTMElemImage.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\RPT_SorderMTMElemImage.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SAP_MaterialCutData.cs",
                      "RelativePath":  "TEST\\SAP_MaterialCutData.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SAP_MaterialCutData.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SAP_MaterialCutData.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SharmoonSnap.cs",
                      "RelativePath":  "TEST\\SharmoonSnap.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SharmoonSnap.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SharmoonSnap.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SharmoonSnap.Designer.cs",
                      "RelativePath":  "TEST\\SharmoonSnap.Designer.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SharmoonSnap.Designer.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SharmoonSnap.Designer.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_Sorder.cs",
                      "RelativePath":  "TEST\\SNAP_Sorder.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_Sorder.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_Sorder.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_SorderDetail.cs",
                      "RelativePath":  "TEST\\SNAP_SorderDetail.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_SorderDetail.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_SorderDetail.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_SorderDetailBody.cs",
                      "RelativePath":  "TEST\\SNAP_SorderDetailBody.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_SorderDetailBody.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_SorderDetailBody.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_SorderDetailElem.cs",
                      "RelativePath":  "TEST\\SNAP_SorderDetailElem.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_SorderDetailElem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_SorderDetailElem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_SorderDetailModel.cs",
                      "RelativePath":  "TEST\\SNAP_SorderDetailModel.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_SorderDetailModel.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_SorderDetailModel.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SNAP_SorderDetailSize.cs",
                      "RelativePath":  "TEST\\SNAP_SorderDetailSize.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SNAP_SorderDetailSize.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SNAP_SorderDetailSize.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SorderListDTO.cs",
                      "RelativePath":  "TEST\\SorderListDTO.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SorderListDTO.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SorderListDTO.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYNC_Client.cs",
                      "RelativePath":  "TEST\\SYNC_Client.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SYNC_Client.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SYNC_Client.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYNC_ClientAddress.cs",
                      "RelativePath":  "TEST\\SYNC_ClientAddress.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SYNC_ClientAddress.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SYNC_ClientAddress.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "SYNC_Item.cs",
                      "RelativePath":  "TEST\\SYNC_Item.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\SYNC_Item.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\SYNC_Item.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "TST_Class.cs",
                      "RelativePath":  "TEST\\TST_Class.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\TST_Class.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\TST_Class.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "TST_College.cs",
                      "RelativePath":  "TEST\\TST_College.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\TST_College.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\TST_College.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "TST_DeliverGoods.cs",
                      "RelativePath":  "TEST\\TST_DeliverGoods.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\TST_DeliverGoods.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\TST_DeliverGoods.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "TST_Students.cs",
                      "RelativePath":  "TEST\\TST_Students.cs",
                      "Directory":  "TEST",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\TST_Students.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\TST_Students.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ConsumptionTypeEnums.cs",
                      "RelativePath":  "TEST\\Enums\\ConsumptionTypeEnums.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\ConsumptionTypeEnums.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\ConsumptionTypeEnums.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CtClEnums.cs",
                      "RelativePath":  "TEST\\Enums\\CtClEnums.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\CtClEnums.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\CtClEnums.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "EffectiveConsumptionTypeEnum.cs",
                      "RelativePath":  "TEST\\Enums\\EffectiveConsumptionTypeEnum.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\EffectiveConsumptionTypeEnum.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\EffectiveConsumptionTypeEnum.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "FinalSubTextureEnum.cs",
                      "RelativePath":  "TEST\\Enums\\FinalSubTextureEnum.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\FinalSubTextureEnum.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\FinalSubTextureEnum.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "GroupViewScope.cs",
                      "RelativePath":  "TEST\\Enums\\GroupViewScope.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\GroupViewScope.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\GroupViewScope.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "OptTypeEnums.cs",
                      "RelativePath":  "TEST\\Enums\\OptTypeEnums.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\OptTypeEnums.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\OptTypeEnums.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "OrderFromEnums.cs",
                      "RelativePath":  "TEST\\Enums\\OrderFromEnums.cs",
                      "Directory":  "TEST\\Enums",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\OrderFromEnums.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\OrderFromEnums.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "EnumItem.cs",
                      "RelativePath":  "TEST\\Enums\\Enum\\EnumItem.cs",
                      "Directory":  "TEST\\Enums\\Enum",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\Enum\\EnumItem.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\Enum\\EnumItem.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ReturnCodeEnum.cs",
                      "RelativePath":  "TEST\\Enums\\Enum\\ReturnCodeEnum.cs",
                      "Directory":  "TEST\\Enums\\Enum",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\TEST\\Enums\\Enum\\ReturnCodeEnum.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\TEST\\Enums\\Enum\\ReturnCodeEnum.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "AppTimes.cs",
                      "RelativePath":  "Timing\\AppTimes.cs",
                      "Directory":  "Timing",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Timing\\AppTimes.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Timing\\AppTimes.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "IUPSHelper.cs",
                      "RelativePath":  "UPS\\IUPSHelper.cs",
                      "Directory":  "UPS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\IUPSHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\IUPSHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UPSHelper.cs",
                      "RelativePath":  "UPS\\UPSHelper.cs",
                      "Directory":  "UPS",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\UPSHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\UPSHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "CreateShortNumberInputDto.cs",
                      "RelativePath":  "UPS\\Dtos\\CreateShortNumberInputDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\Dtos\\CreateShortNumberInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\CreateShortNumberInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UpsDto.cs",
                      "RelativePath":  "UPS\\Dtos\\UpsDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\Dtos\\UpsDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\UpsDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UpsInputDto.cs",
                      "RelativePath":  "UPS\\Dtos\\UpsInputDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\Dtos\\UpsInputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\UpsInputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UPSNumberDictionaries.cs",
                      "RelativePath":  "UPS\\Dtos\\UPSNumberDictionaries.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\Dtos\\UPSNumberDictionaries.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\UPSNumberDictionaries.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "UpsOutputDto.cs",
                      "RelativePath":  "UPS\\Dtos\\UpsOutputDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\UPS\\Dtos\\UpsOutputDto.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\UpsOutputDto.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "ValidationHelper.cs",
                      "RelativePath":  "Validation\\ValidationHelper.cs",
                      "Directory":  "Validation",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Validation\\ValidationHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Validation\\ValidationHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "PreventSpamAttribute.cs",
                      "RelativePath":  "Web\\PreventSpamAttribute.cs",
                      "Directory":  "Web",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Web\\PreventSpamAttribute.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Web\\PreventSpamAttribute.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "WebContentFolderHelper.cs",
                      "RelativePath":  "Web\\WebContentFolderHelper.cs",
                      "Directory":  "Web",
                      "Status":  "Both Exist",
                      "SourcePath":  "E:\\workgit\\xm\\XiaoMa.MTM\\XiaoMa.XMMTM.Core\\Web\\WebContentFolderHelper.cs",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Web\\WebContentFolderHelper.cs",
                      "Action":  "Need Content Check"
                  },
                  {
                      "FileName":  "BokeCadManager.cs",
                      "RelativePath":  "CAD\\Boke\\BokeCadManager.cs",
                      "Directory":  "CAD\\Boke",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Boke\\BokeCadManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IBokeCadManager.cs",
                      "RelativePath":  "CAD\\Boke\\IBokeCadManager.cs",
                      "Directory":  "CAD\\Boke",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Boke\\IBokeCadManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BokeCADOuputDto.cs",
                      "RelativePath":  "CAD\\Boke\\Dtos\\BokeCADOuputDto.cs",
                      "Directory":  "CAD\\Boke\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\CAD\\Boke\\Dtos\\BokeCADOuputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "CustomsPlatform.cs",
                      "RelativePath":  "Configuration\\CustomsPlatform.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\CustomsPlatform.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ICustomsPlatform.cs",
                      "RelativePath":  "Configuration\\ICustomsPlatform.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\ICustomsPlatform.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyConfiguration.cs",
                      "RelativePath":  "Configuration\\ISuitSupplyConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\ISuitSupplyConfiguration.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IXMMTMConfiguration.cs",
                      "RelativePath":  "Configuration\\IXMMTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\IXMMTMConfiguration.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyConfiguration.cs",
                      "RelativePath":  "Configuration\\SuitSupplyConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\SuitSupplyConfiguration.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "XMMTMConfiguration.cs",
                      "RelativePath":  "Configuration\\XMMTMConfiguration.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\XMMTMConfiguration.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "XMMTMConfigurationExtensions.cs",
                      "RelativePath":  "Configuration\\XMMTMConfigurationExtensions.cs",
                      "Directory":  "Configuration",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Configuration\\XMMTMConfigurationExtensions.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CadLayoutStore.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CadLayoutStore.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CadLayoutStore.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CadLayoutStoreElemVariant.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CadLayoutStoreElemVariant.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CadLayoutStoreElemVariant.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CadLayoutStorePiece.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CadLayoutStorePiece.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CadLayoutStorePiece.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CadLayoutStoreSize.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CadLayoutStoreSize.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CadLayoutStoreSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CtClCode.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CtClCode.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CtClCode.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_CtClMode.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_CtClMode.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_CtClMode.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdCadLayout.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdCadLayout.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdCadLayout.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdModel.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdModel.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdModel.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdModelBody.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdModelBody.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdModelBody.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdModelElem.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdModelElem.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdModelElem.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdModelElemVariant.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdModelElemVariant.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdModelElemVariant.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdModelQCSizeFix.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdModelQCSizeFix.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdModelQCSizeFix.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProdSubProduct.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProdSubProduct.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProdSubProduct.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_Product.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_Product.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_Product.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProductBom_Head.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProductBom_Head.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProductBom_Head.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "BAD_ProductBom_Item.cs",
                      "RelativePath":  "Domain\\BAD\\BAD_ProductBom_Item.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\BAD_ProductBom_Item.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "GlobalCountry.cs",
                      "RelativePath":  "Domain\\BAD\\GlobalCountry.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\GlobalCountry.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ItemModelElem.cs",
                      "RelativePath":  "Domain\\BAD\\ItemModelElem.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemModelElem.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ItemModelGroup.cs",
                      "RelativePath":  "Domain\\BAD\\ItemModelGroup.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\ItemModelGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitsupplyItemComp.cs",
                      "RelativePath":  "Domain\\BAD\\SuitsupplyItemComp.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\SuitsupplyItemComp.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitsupplyItemModelElemList.cs",
                      "RelativePath":  "Domain\\BAD\\SuitsupplyItemModelElemList.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\SuitsupplyItemModelElemList.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitsupplyMarkWash.cs",
                      "RelativePath":  "Domain\\BAD\\SuitsupplyMarkWash.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\SuitsupplyMarkWash.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitsupplyMarkWashDetail.cs",
                      "RelativePath":  "Domain\\BAD\\SuitsupplyMarkWashDetail.cs",
                      "Directory":  "Domain\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\BAD\\SuitsupplyMarkWashDetail.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ModelDesignByElemList.cs",
                      "RelativePath":  "Domain\\MOM\\ModelDesignByElemList.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelDesignByElemList.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ModelDesignByElemListDetail.cs",
                      "RelativePath":  "Domain\\MOM\\ModelDesignByElemListDetail.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelDesignByElemListDetail.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ModelElemListImage.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemListImage.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemListImage.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ModelElemRule1.cs",
                      "RelativePath":  "Domain\\MOM\\ModelElemRule1.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\ModelElemRule1.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_BodyListGroup.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_BodyListGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_BodyListGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_BusinessSubTypeCardRule.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_BusinessSubTypeCardRule.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_BusinessSubTypeCardRule.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_ModelBaseModelElem.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_ModelBaseModelElem.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_ModelBaseModelElem.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_ModelElemListSapParts.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_ModelElemListSapParts.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_ModelElemListSapParts.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_ModelSub.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_ModelSub.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_ModelSub.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_SewPart.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_SewPart.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_SewPart.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_SizeListExtend.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_SizeListExtend.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_SizeListExtend.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_SizeListGroup.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_SizeListGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_SizeListGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_SizeListReference.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_SizeListReference.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_SizeListReference.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_SizeRange.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_SizeRange.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_SizeRange.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabCheckGroup.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabCheckGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabCheckGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabCheckHost.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabCheckHost.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabCheckHost.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabCheckHostTabCheckGroup.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabCheckHostTabCheckGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabCheckHostTabCheckGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabCheckSeq.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabCheckSeq.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabCheckSeq.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabGroup.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabGroup.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabGroup.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabGroupCheckRouter.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabGroupCheckRouter.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabGroupCheckRouter.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabMod.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabMod.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabMod.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabName.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabName.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabName.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabReport.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabReport.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabReport.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabReport_his.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabReport_his.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabReport_his.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "MOM_TabSubURL.cs",
                      "RelativePath":  "Domain\\MOM\\MOM_TabSubURL.cs",
                      "Directory":  "Domain\\MOM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\MOM\\MOM_TabSubURL.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderSuitSupply.cs",
                      "RelativePath":  "Domain\\ODM\\SorderSuitSupply.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderSuitSupply.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderSuitSupplyBom.cs",
                      "RelativePath":  "Domain\\ODM\\SorderSuitSupplyBom.cs",
                      "Directory":  "Domain\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\SorderSuitSupplyBom.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyBomDto.cs",
                      "RelativePath":  "Domain\\ODM\\Dto\\ISuitSupplyBomDto.cs",
                      "Directory":  "Domain\\ODM\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\Dto\\ISuitSupplyBomDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyOrderDto.cs",
                      "RelativePath":  "Domain\\ODM\\Dto\\ISuitSupplyOrderDto.cs",
                      "Directory":  "Domain\\ODM\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\Dto\\ISuitSupplyOrderDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyBomDto.cs",
                      "RelativePath":  "Domain\\ODM\\Dto\\SuitSupplyBomDto.cs",
                      "Directory":  "Domain\\ODM\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\Dto\\SuitSupplyBomDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyOrderDto.cs",
                      "RelativePath":  "Domain\\ODM\\Dto\\SuitSupplyOrderDto.cs",
                      "Directory":  "Domain\\ODM\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\ODM\\Dto\\SuitSupplyOrderDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_CustomerDetails.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_CustomerDetails.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_CustomerDetails.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Jacket.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Jacket.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Jacket.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_JacketSize.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_JacketSize.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_JacketSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Option.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Option.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Option.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_OptionSize.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_OptionSize.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_OptionSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_OptionValue.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_OptionValue.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_OptionValue.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Order.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Order.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Order.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_OrderDetail.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_OrderDetail.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_OrderDetail.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Product.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Product.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Product.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Trouser.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Trouser.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Trouser.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_TrouserSize.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_TrouserSize.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_TrouserSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_Waistcoat.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_Waistcoat.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_Waistcoat.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupply_WaistcoatSize.cs",
                      "RelativePath":  "Domain\\SuitSupply\\SuitSupply_WaistcoatSize.cs",
                      "Directory":  "Domain\\SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\SuitSupply\\SuitSupply_WaistcoatSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_Body.cs",
                      "RelativePath":  "Domain\\Translation\\Language_Body.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_Body.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_BodyList.cs",
                      "RelativePath":  "Domain\\Translation\\Language_BodyList.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_BodyList.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_Model.cs",
                      "RelativePath":  "Domain\\Translation\\Language_Model.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_Model.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_ModelElem.cs",
                      "RelativePath":  "Domain\\Translation\\Language_ModelElem.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_ModelElem.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_ModelElemList.cs",
                      "RelativePath":  "Domain\\Translation\\Language_ModelElemList.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_ModelElemList.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "Language_SizeColumn.cs",
                      "RelativePath":  "Domain\\Translation\\Language_SizeColumn.cs",
                      "Directory":  "Domain\\Translation",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\Translation\\Language_SizeColumn.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ItemBatchEoriNo.cs",
                      "RelativePath":  "Domain\\WAR\\ItemBatchEoriNo.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ItemBatchEoriNo.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ProductBoxConfig.cs",
                      "RelativePath":  "Domain\\WAR\\ProductBoxConfig.cs",
                      "Directory":  "Domain\\WAR",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Domain\\WAR\\ProductBoxConfig.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ILanguageBaseDto.cs",
                      "RelativePath":  "Dto\\ILanguageBaseDto.cs",
                      "Directory":  "Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\ILanguageBaseDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ICustomer_details.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\ICustomer_details.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\ICustomer_details.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IJacket.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IJacket.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IJacket.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IJacketDto.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IJacketDto.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IJacketDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IJacketSize.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IJacketSize.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IJacketSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IJacket_Monogram.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IJacket_Monogram.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IJacket_Monogram.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IProduct.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IProduct.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IProduct.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IShipping_address.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IShipping_address.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IShipping_address.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ITrouser.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\ITrouser.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\ITrouser.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ITrouserSize.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\ITrouserSize.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\ITrouserSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IWaistcoat.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IWaistcoat.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IWaistcoat.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IWaistcoatSize.cs",
                      "RelativePath":  "Dto\\SuitSupplyDto\\IWaistcoatSize.cs",
                      "Directory":  "Dto\\SuitSupplyDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Dto\\SuitSupplyDto\\IWaistcoatSize.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyOrderExport.cs",
                      "RelativePath":  "Excel\\ISuitSupplyOrderExport.cs",
                      "Directory":  "Excel",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\ISuitSupplyOrderExport.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyOrderExport.cs",
                      "RelativePath":  "Excel\\SuitSupplyOrderExport.cs",
                      "Directory":  "Excel",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\SuitSupplyOrderExport.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "DBDeliveryOrderOutputDto.cs",
                      "RelativePath":  "Excel\\Dto\\DBDeliveryOrderOutputDto.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\DBDeliveryOrderOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ExcelColumnAttribute.cs",
                      "RelativePath":  "Excel\\Dto\\ExcelColumnAttribute.cs",
                      "Directory":  "Excel\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Excel\\Dto\\ExcelColumnAttribute.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "GetModelImageByIdOutputDto.cs",
                      "RelativePath":  "FileServer\\GetModelImageByIdOutputDto.cs",
                      "Directory":  "FileServer",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\FileServer\\GetModelImageByIdOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "FileOutputDto.cs",
                      "RelativePath":  "FileServer\\Dtos\\FileOutputDto.cs",
                      "Directory":  "FileServer\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\FileServer\\Dtos\\FileOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "TimeZoneHelper.cs",
                      "RelativePath":  "Helper\\TimeZoneHelper.cs",
                      "Directory":  "Helper",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Helper\\TimeZoneHelper.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ToLocalTimeHelper.cs",
                      "RelativePath":  "Helper\\ToLocalTimeHelper.cs",
                      "Directory":  "Helper",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Helper\\ToLocalTimeHelper.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "CustomsOrderRemoteService.cs",
                      "RelativePath":  "Http\\Api\\CustomsOrderRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\CustomsOrderRemoteService.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ICustomsOrderRemoteService.cs",
                      "RelativePath":  "Http\\Api\\ICustomsOrderRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\ICustomsOrderRemoteService.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISSOUserRemoteService.cs",
                      "RelativePath":  "Http\\Api\\ISSOUserRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\ISSOUserRemoteService.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyRemoteService.cs",
                      "RelativePath":  "Http\\Api\\ISuitSupplyRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\ISuitSupplyRemoteService.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyRemoteService.cs",
                      "RelativePath":  "Http\\Api\\SuitSupplyRemoteService.cs",
                      "Directory":  "Http\\Api",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\SuitSupplyRemoteService.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "CustomsOrderOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\CustomsOrderOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\CustomsOrderOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ExportCustomsOrderInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\ExportCustomsOrderInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\ExportCustomsOrderInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "GetUserInfoInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\GetUserInfoInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\GetUserInfoInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "GetUserInfoOutputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\GetUserInfoOutputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\GetUserInfoOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyGetTokenResultDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SuitSupplyGetTokenResultDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SuitSupplyGetTokenResultDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyInvoiceInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SuitSupplyInvoiceInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SuitSupplyInvoiceInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyOrderShipmentStateInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SuitSupplyOrderShipmentStateInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SuitSupplyOrderShipmentStateInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyOrderUpdateWebHookInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SuitSupplyOrderUpdateWebHookInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SuitSupplyOrderUpdateWebHookInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyStockAddedWebhookInputDto.cs",
                      "RelativePath":  "Http\\Api\\Dtos\\SuitSupplyStockAddedWebhookInputDto.cs",
                      "Directory":  "Http\\Api\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Http\\Api\\Dtos\\SuitSupplyStockAddedWebhookInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ClientManager.cs",
                      "RelativePath":  "Manager\\BAD\\ClientManager.cs",
                      "Directory":  "Manager\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\BAD\\ClientManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ClientModelClassManager.cs",
                      "RelativePath":  "Manager\\BAD\\ClientModelClassManager.cs",
                      "Directory":  "Manager\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\BAD\\ClientModelClassManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IClientManager.cs",
                      "RelativePath":  "Manager\\BAD\\IClientManager.cs",
                      "Directory":  "Manager\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\BAD\\IClientManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IClientModelClassManager.cs",
                      "RelativePath":  "Manager\\BAD\\IClientModelClassManager.cs",
                      "Directory":  "Manager\\BAD",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\BAD\\IClientModelClassManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "GetParentClientManagerInputDto.cs",
                      "RelativePath":  "Manager\\BAD\\Dtos\\GetParentClientManagerInputDto.cs",
                      "Directory":  "Manager\\BAD\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\BAD\\Dtos\\GetParentClientManagerInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISorderBillManager.cs",
                      "RelativePath":  "Manager\\ODM\\ISorderBillManager.cs",
                      "Directory":  "Manager\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\ISorderBillManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISorderLogManager.cs",
                      "RelativePath":  "Manager\\ODM\\ISorderLogManager.cs",
                      "Directory":  "Manager\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\ISorderLogManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderBillManager.cs",
                      "RelativePath":  "Manager\\ODM\\SorderBillManager.cs",
                      "Directory":  "Manager\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\SorderBillManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderLogManager.cs",
                      "RelativePath":  "Manager\\ODM\\SorderLogManager.cs",
                      "Directory":  "Manager\\ODM",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\SorderLogManager.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ODM_SorderBillCreateManagerInputDto.cs",
                      "RelativePath":  "Manager\\ODM\\Dtos\\ODM_SorderBillCreateManagerInputDto.cs",
                      "Directory":  "Manager\\ODM\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\Dtos\\ODM_SorderBillCreateManagerInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ODM_SorderBillDetailManagerDto.cs",
                      "RelativePath":  "Manager\\ODM\\Dtos\\ODM_SorderBillDetailManagerDto.cs",
                      "Directory":  "Manager\\ODM\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\Dtos\\ODM_SorderBillDetailManagerDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderLogManagerInputDto.cs",
                      "RelativePath":  "Manager\\ODM\\Dtos\\SorderLogManagerInputDto.cs",
                      "Directory":  "Manager\\ODM\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Manager\\ODM\\Dtos\\SorderLogManagerInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "IQCode.cs",
                      "RelativePath":  "Pdf\\IQCode.cs",
                      "Directory":  "Pdf",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\IQCode.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "QCode.cs",
                      "RelativePath":  "Pdf\\QCode.cs",
                      "Directory":  "Pdf",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\Pdf\\QCode.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderImport_API.cs",
                      "RelativePath":  "SorderManager\\SorderImport_API.cs",
                      "Directory":  "SorderManager",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\SorderImport_API.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ExportTemplateInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ExportTemplateInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ExportTemplateInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ExportTemplateSorderModelDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ExportTemplateSorderModelDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ExportTemplateSorderModelDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ImportSuitSupplyAPISorderInpuDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\ImportSuitSupplyAPISorderInpuDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\ImportSuitSupplyAPISorderInpuDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "OldSuitSupplyOrderDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\OldSuitSupplyOrderDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\OldSuitSupplyOrderDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderDeepCloneManagerOutputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderDeepCloneManagerOutputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderDeepCloneManagerOutputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderManagerInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderManagerInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderManagerInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderProDetailSizeDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderProDetailSizeDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderProDetailSizeDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SorderProExcelInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SorderProExcelInputDto.cs",
                      "Directory":  "SorderManager\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SorderProExcelInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "CreateSorderDetailModelBySuitSupplyInputDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SuitSupplyAPIDto\\CreateSorderDetailModelBySuitSupplyInputDto.cs",
                      "Directory":  "SorderManager\\Dtos\\SuitSupplyAPIDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SuitSupplyAPIDto\\CreateSorderDetailModelBySuitSupplyInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyOrderValuesDto.cs",
                      "RelativePath":  "SorderManager\\Dtos\\SuitSupplyAPIDto\\SuitSupplyOrderValuesDto.cs",
                      "Directory":  "SorderManager\\Dtos\\SuitSupplyAPIDto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SorderManager\\Dtos\\SuitSupplyAPIDto\\SuitSupplyOrderValuesDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "ISuitSupplyManage.cs",
                      "RelativePath":  "SuitSupply\\ISuitSupplyManage.cs",
                      "Directory":  "SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\ISuitSupplyManage.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyManage.cs",
                      "RelativePath":  "SuitSupply\\SuitSupplyManage.cs",
                      "Directory":  "SuitSupply",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\SuitSupplyManage.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "DetailModelStateByMesInputDto.cs",
                      "RelativePath":  "SuitSupply\\Dto\\DetailModelStateByMesInputDto.cs",
                      "Directory":  "SuitSupply\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\Dto\\DetailModelStateByMesInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "EPCUpdateInputDto.cs",
                      "RelativePath":  "SuitSupply\\Dto\\EPCUpdateInputDto.cs",
                      "Directory":  "SuitSupply\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\Dto\\EPCUpdateInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "OrderDetailDto.cs",
                      "RelativePath":  "SuitSupply\\Dto\\OrderDetailDto.cs",
                      "Directory":  "SuitSupply\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\Dto\\OrderDetailDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "OrderUpdateWebhookInputDto.cs",
                      "RelativePath":  "SuitSupply\\Dto\\OrderUpdateWebhookInputDto.cs",
                      "Directory":  "SuitSupply\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\Dto\\OrderUpdateWebhookInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "SuitSupplyShipmentInfo.cs",
                      "RelativePath":  "SuitSupply\\Dto\\SuitSupplyShipmentInfo.cs",
                      "Directory":  "SuitSupply\\Dto",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\SuitSupply\\Dto\\SuitSupplyShipmentInfo.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "OAuth2Client.cs",
                      "RelativePath":  "UPS\\OAuth2Client.cs",
                      "Directory":  "UPS",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\OAuth2Client.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "PaperLessDocumnetsUploadInputDto.cs",
                      "RelativePath":  "UPS\\Dtos\\PaperLessDocumnetsUploadInputDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\PaperLessDocumnetsUploadInputDto.cs",
                      "Action":  "Target Specific"
                  },
                  {
                      "FileName":  "PaperLessDocumnetsUploadOutputDto.cs",
                      "RelativePath":  "UPS\\Dtos\\PaperLessDocumnetsUploadOutputDto.cs",
                      "Directory":  "UPS\\Dtos",
                      "Status":  "Target Only",
                      "SourcePath":  "Not Exist",
                      "TargetPath":  "E:\\workgit\\Shop\\XiaoMa.MTM\\XiaoMa.Shop.XMMTM.Core\\UPS\\Dtos\\PaperLessDocumnetsUploadOutputDto.cs",
                      "Action":  "Target Specific"
                  }
              ]
}

# 代码迁移对比 - 模块选择 TodoList

## 📋 使用说明

- ✅ 勾选需要对比的模块
- 🔍 点击模块名查看该模块下的文件统计
- ⚠️ 建议按优先级逐个模块进行对比
- 📝 完成选择后，告知 AI 开始生成对应模块的文件对比清单

## 🎯 项目信息

- **源项目**: `e:\workgit\xm\XiaoMa.MTM\` (XiaoMa.XMMTM)
- **目标项目**: `e:\workgit\Shop\XiaoMa.MTM\` (XiaoMa.Shop.XMMTM)
- **对比规则**: 只对比 `.cs` 文件，自动忽略命名空间差异
- **排除内容**: 无特殊排除（之前提到的 ODM 命名空间经确认不需要排除）

---

## 📦 模块列表

### [x] **Core 模块** ✅ 已完成对比

- 📁 **源路径**: `XiaoMa.XMMTM.Core`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.Core`
- 📊 **文件统计**:
  - 总计: 581 个文件
  - 两项目都存在: 365 个 (需要内容对比)
  - 仅源项目: 62 个 (需要复制)
  - 仅目标项目: 154 个 (目标特有)
- 🎯 **优先级**: 🔴 高 (核心业务逻辑)
- 💡 **说明**: 包含实体定义、领域服务、核心业务逻辑等
- 📝 **状态**: ✅ 对比完成，详细清单已生成
- 📄 **详细清单**: `02_FileComparisons/Core_Files.md`

### [ ] **Application 模块**

- 📁 **源路径**: `XiaoMa.XMMTM.Application`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.Application`
- 📊 **预估文件数**: 待统计
- 🎯 **优先级**: 🔴 高 (应用服务层)
- 💡 **说明**: 包含应用服务、DTO、应用层业务逻辑等

### [ ] **EntityFrameworkCore 模块**

- 📁 **源路径**: `XiaoMa.XMMTM.EntityFrameworkCore`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.EntityFrameworkCore`
- 📊 **预估文件数**: 待统计
- 🎯 **优先级**: 🟡 中 (数据访问层)
- 💡 **说明**: 包含 DbContext、实体配置、数据访问逻辑等

### [ ] **Web.Core 模块**

- 📁 **源路径**: `XiaoMa.XMMTM.Web.Core`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.Web.Core`
- 📊 **预估文件数**: 待统计
- 🎯 **优先级**: 🟡 中 (Web 核心)
- 💡 **说明**: 包含 Web 层核心功能、中间件、过滤器等

### [ ] **Web.Host 模块**

- 📁 **源路径**: `XiaoMa.XMMTM.Web.Host`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.Web.Host`
- 📊 **预估文件数**: 待统计
- 🎯 **优先级**: 🟢 低 (宿主配置)
- 💡 **说明**: 包含启动配置、控制器、Web 宿主相关代码

### [ ] **Migrator 模块**

- 📁 **源路径**: `XiaoMa.XMMTM.Migrator`
- 📁 **目标路径**: `XiaoMa.Shop.XMMTM.Migrator`
- 📊 **预估文件数**: 待统计
- 🎯 **优先级**: 🟢 低 (数据迁移)
- 💡 **说明**: 包含数据库迁移脚本和相关工具

---

## 🚀 下一步操作

1. **选择模块**: 勾选您想要对比的模块
2. **通知 AI**: 告知 AI 您选择的模块，AI 将生成详细的文件对比清单
3. **逐步对比**: 建议从高优先级模块开始，逐个进行对比

## 📝 备注

- 此 TodoList 会根据实际扫描结果更新文件统计信息
- 建议优先处理 Core 和 Application 模块，因为它们包含核心业务逻辑
- 每个模块的对比结果将保存在 `02_FileComparisons/` 文件夹中
- 详细的文件差异分析将保存在 `03_DetailedComparisons/` 文件夹中

---

**生成时间**: 2024-12-19  
**状态**: 等待模块选择

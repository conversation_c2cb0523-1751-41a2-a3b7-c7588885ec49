/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_ModelBodyListAppService
// 功能描述：    应用服务类
// 作者    zhangby
// 时间    2021/4/19/星期一 9:01:03
-----------------------------------------------*/

using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.ObjectMapping;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App.MOM.Dtos;
using XiaoMa.XMMTM.Domain;
using Abp.UI;
using EFCore.BulkExtensions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using Model = XiaoMa.XMMTM.Domain.Model;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Abp.EntityFrameworkCore.Repositories;
using System.Collections.Generic;

namespace XiaoMa.XMMTM.App.MOM
{
    /// <summary>
    /// 应用服务类
    /// </summary>
    public class MOM_ModelBodyListAppService : XMMTMAppServiceBase, IMOM_ModelBodyListAppService
    {
        private readonly IRepository<Domain.ModelBodyList, Guid> repository;
        private readonly IRepository<Model, Guid> modelRepository;
        private readonly IRepository<BodyList, Guid> bodyListRepository;
        private readonly IObjectMapper objectMapper;
        public MOM_ModelBodyListAppService(
       IRepository<Domain.ModelBodyList, Guid> repository,
       IRepository<Model, Guid> modelRepository,
       IRepository<BodyList, Guid> bodyListRepository,
       IObjectMapper objectMapper
         )
        {
            this.repository = repository;
            this.modelRepository = modelRepository;
            this.bodyListRepository = bodyListRepository;
            this.objectMapper = objectMapper;
        }
        /// <summary>
        /// 版型绑定特体
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [AbpAuthorize]
        public async Task UpdatePlus(UpdatePlusGetInputDto input)
        {
            if (input.ModelIDs==null||input.ModelIDs.Count == 0)
            {
                throw new UserFriendlyException("请选择版型");
            }
            var modelbodyList = await repository.GetAll().Where(a => input.ModelIDs.Contains(a.ModelID)).ToListAsync();
            if (input.IsCleared)
            {
                await repository.GetDbContext().BulkDeleteAsync(modelbodyList);
            }
            var list = new List<ModelBodyList>();
            foreach (var modelid in input.ModelIDs)
            {
                foreach (var bodylistid in input.BodyListIDs)
                {
                    var dto = modelbodyList.FirstOrDefault(a => a.ModelID == modelid && a.BodyListID == bodylistid);
                    if (dto == null|| input.IsCleared)
                    {
                        list.Add(new ModelBodyList()
                        {
                            BodyListID = bodylistid,
                            CreateBy = this.SSOSession.Name,
                            CreateID = this.SSOSession.UserId,
                            CreateOn = DateTime.Now,
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            ModelID = modelid
                        });
                    }
                }
            }
            if (list.Any())
            {
               await repository.GetDbContext().BulkInsertAsync(list);
            }

        }
    }
}

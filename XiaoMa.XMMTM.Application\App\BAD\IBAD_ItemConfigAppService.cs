﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    IBAD_ItemConfigAppService
// 功能描述：    应用接口服务类
// 作者    zhangby
// 时间    2020/10/26/星期一 9:10:31
-----------------------------------------------*/
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;
using XiaoMa.XMMTM.App;
using XiaoMa.XMMTM.App.BAD.Dtos;

namespace XiaoMa.XMMTM.App.BAD
{
    /// <summary>
    /// 接口类
    /// </summary>
    public interface IBAD_ItemConfigAppService : IApplicationService
    {
        Task<PagedResultDto<BAD_ItemConfigGetAllOutputDto>> Get(BAD_ItemConfigGetAllInputDto input);
        Task Adds(List<BAD_ItemConfigDto> input);
        Task Updates(List<BAD_ItemConfigDto> input);
        Task Deletes(List<BAD_ItemConfigDto> input);
    }
}

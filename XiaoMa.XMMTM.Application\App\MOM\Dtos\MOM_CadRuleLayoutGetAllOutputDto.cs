﻿/*-----------------------------------------------
// Copyright (C) 2020 青岛骁码云智能科技有限公司  版权所有。
// 文件名称：    MOM_CadRuleLayoutGetAllOutputDto
// 功能描述：    查询返回Dto
// 作者    zhangby
// 时间    2020/8/6/星期四 15:59:40
-----------------------------------------------*/

using System;
using XiaoMa.XMMTM.Domain;

namespace XiaoMa.XMMTM.App.MOM.Dtos
{
    /// <summary>
    /// MOM_CadRuleLayoutGetAllOutputDto查询返回实体对象
    /// </summary>
    public class MOM_CadRuleLayoutGetAllOutputDto : CadRuleLayout
    {
        public string GroupText { set; get; }

        public string CadRuleText { set; get; }

        public string CadLayoutText { set; get; }

        public string ModelElemText { set; get; }
        public string ModelElemListText { set; get; }

        public Guid? GroupID { set; get; }

        public string CadRuleCode { set; get; }

        public string ModelElemCode { set; get; }

        public string ModelElemName { set; get; }
    }
}
